import json
import logging
import os
import re
import subprocess
import urllib.parse

import requests
import yaml
from colorlog import ColoredFormatter

"""
This is a high level script that will be used to compare the configuration files
of the services in the current merge request with the master branch.

If changes are detected in multiple services chart folder, it will process them 1 by 1.

The script will output the differences in markdown format and post them as a comment in the merge request.

To run this script on local, you will need to provide your own environment variables.
"""

BASE_BRANCH = os.getenv("CI_MERGE_REQUEST_TARGET_BRANCH_NAME")
DIFF_SHA = os.getenv("CI_MERGE_REQUEST_DIFF_BASE_SHA")
DEBUG_HEAD_SHA = os.getenv("DEBUG_HEAD_SHA")
GITLAB_TOKEN = os.getenv("DBMY_CI_TOKEN")
# local setup
if GITLAB_TOKEN is None or GITLAB_TOKEN == "":
    GITLAB_TOKEN = os.getenv("GXBANK_GITLAB_WRITE_ACCESS_TOKEN")

GITLAB_PROJECT_ID = os.getenv("CI_PROJECT_ID")
CI_MERGE_REQUEST_IID = os.getenv("CI_MERGE_REQUEST_IID")
GITLAB_HOST = "https://gitlab.com"
ATTENTION_LABELS = {"config-needed","high-risk","lock-step"}

# setup console logger with color
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
ch = logging.StreamHandler()
formatter = ColoredFormatter(
    "%(log_color)s%(levelname)s:%(name)s:%(filename)s:%(lineno)d:%(message)s",
    log_colors={
        "DEBUG": "cyan",
        "INFO": "green",
        "WARNING": "yellow",
        "ERROR": "red",
        "CRITICAL": "bold_red",
    },
)
ch.setFormatter(formatter)
logger.addHandler(ch)


def fetch_master() -> subprocess.CompletedProcess:
    try:
        out = subprocess.run(['git', 'fetch', 'origin', BASE_BRANCH], capture_output=True, check=True)
        return out
    except subprocess.CalledProcessError as e:
        raise Exception(f"Error fetching master branch: {e.stderr.decode()}")


def get_file_changes() -> list[str]:
    try:
        out = subprocess.run(['git', 'diff', '--name-only', DIFF_SHA], capture_output=True, check=True)
        files = out.stdout.decode()
        files = [f.strip() for f in files.split("\n") if not f == ""]
        return files
    except subprocess.CalledProcessError as e:
        raise Exception(f"Error getting commit changes: {e.stderr.decode()}")


def get_build_commit_changes(filepath: str) -> dict:
    """
    Extracts commit changes from a file path that matches a specified regex pattern.

    The function validates the file path against a specific regex to ensure it adheres
    to the expected format. It then parses the current and base commits from image tags
    stored in the YAML file content. The function retrieves historical file content for
    specific Git commit SHAs if required. It ensures proper error handling and logging for
    cases such as missing image tags or unparseable file content, returning relevant commit
    information if successful.

    :param filepath: The path to the YAML file to be processed.
    :type filepath: str
    :return: A dictionary containing the service name, latest commit hash, base commit hash,
        and the file path, or None if the file does not meet the criteria or an error occurs
        during processing.
    :rtype: dict
    """
    #  skip if the file doesnt match the regex pattern of "charts/<service_name>/.values.<dev,stg,prd>.yaml"
    regex = r"charts\/[a-zA-Z0-9_-]+\/\.values\.(dev|stg|prd)\.yaml"
    if not re.match(regex, filepath):
        return None
    # get the service name from the filepath

    service_name = filepath.split("/")[1]
    if DEBUG_HEAD_SHA is not None and DEBUG_HEAD_SHA != "":
        out = subprocess.run(['git', 'show', f"{DEBUG_HEAD_SHA}:{filepath}"], capture_output=True, check=True)
        if out.returncode != 0:
            logger.warning(f"Error getting file {filepath} at commit {DEBUG_HEAD_SHA}: {out.stderr.decode()}")
            return None
        values = yaml.safe_load(out.stdout.decode())
        latest_tag = values.get("image", {}).get("tag", None)
    else:
        values = yaml.safe_load(open(filepath, "r"))
        # get the values from the yaml file
        latest_tag = values.get("image", {}).get("tag", None)

    if latest_tag is None:
        logger.warning(f"Image tag not found in {filepath}")
        return None
    latest_commit = latest_tag.split("-")
    if len(latest_commit) < 2:
        logger.warning(f"Invalid image tag format in {filepath}")
        return None
    latest_commit = latest_commit[-2]
    # fetch the older version of filepath of commit DIFF_SHA
    out = subprocess.run(['git', 'show', f"{DIFF_SHA}:{filepath}"], capture_output=True, check=True)
    # if there's error just log it and return as the file might not exist
    if out.returncode != 0:
        logger.warning(f"Error getting file {filepath} at commit {DIFF_SHA}: {out.stderr.decode()}")
        return None
    # parse the yaml file
    old_values = yaml.safe_load(out.stdout.decode())
    older_tag = old_values.get("image", {}).get("tag", None)
    if older_tag is None:
        logger.warning(f"Image tag not found in {filepath} at commit {DIFF_SHA}")
        return None
    base_commit = older_tag.split("-")
    if len(base_commit) < 2:
        logger.warning(f"Invalid image tag format in {filepath} at commit {DIFF_SHA}")
        return None
    base_commit = base_commit[-2]
    return {
        "service_name": service_name,
        "latest_commit": latest_commit,
        "base_commit": base_commit,
        "value_filepath": filepath,
    }


def get_diff_merge_requests(base_commit: str, head_commit: str, config: dict) -> list[dict]:
    """
    Fetches a list of scoped merge requests associated with the diff between two commits
    in a GitLab repository. The function communicates with the GitLab API to retrieve
    commit information, associated merge requests, and their metadata. It optionally
    filters merge requests based on a specified subpath, narrowing down the results
    to relevant changes.

    :param base_commit: The SHA of the base commit for the comparison.
    :type base_commit: str
    :param head_commit: The SHA of the head commit for the comparison.
    :type head_commit: str
    :param config: A dictionary containing necessary configuration details such as
        the project path and an optional subpath for filtering.
    :type config: dict
    :return: A list of dictionaries containing data for scoped merge requests,
        including their ids, titles, authors, labels, merge timestamps, and URLs.
    :rtype: list[dict]
    """
    # get gitlab project path from the config
    project_path = config.get("project_path", None)
    if is_empty_string(project_path):
        raise Exception("project_path not found in config")
    encoded_project_path = urllib.parse.quote(project_path, safe="")
    # get the merge requests associated with the diff between base_commit  and head_commit using gitlab api
    api_url = f"{GITLAB_HOST}/api/v4/projects/{encoded_project_path}/repository/compare"
    logger.info(f"Fetching {api_url}")
    params = {"to": head_commit, "from": base_commit}
    headers = {"PRIVATE-TOKEN": GITLAB_TOKEN}

    commits_resp = requests.get(api_url, headers=headers, params=params, timeout=10)
    if commits_resp.status_code != 200:
        raise Exception(f"Error fetching commits: {commits_resp.json()}")

    commits = commits_resp.json()

    merge_requests = []
    mr_iids = set()
    logger.info(f"Found {len(commits['commits'])} commits in the diff")
    counter = 0
    for commit in commits['commits']:
        counter += 1
        commit_sha = commit["id"]
        mr_url = f"{GITLAB_HOST}/api/v4/projects/{encoded_project_path}/repository/commits/{commit_sha}/merge_requests"
        logger.info(f"Fetching merge requests from {mr_url} [{counter}/{len(commits['commits'])}]")
        mr_resp = requests.get(mr_url, headers=headers, timeout=10)
        if mr_resp.status_code == 200:
            for mr in mr_resp.json():
                if mr["iid"] in mr_iids:
                    continue
                else:
                    mr_iids.add(mr["iid"])
                mr_details_url = f"{GITLAB_HOST}/api/v4/projects/{encoded_project_path}/merge_requests/{mr['iid']}"
                logger.info(f"Fetching merge request details from {mr_details_url}")
                mr_details_resp = requests.get(mr_details_url, headers=headers, timeout=10)
                if mr_details_resp.status_code == 200:
                    mr_details = mr_details_resp.json()
                    merge_requests.append({
                        "id": mr["id"],
                        "iid": mr["iid"],
                        "title": mr["title"],
                        "labels": mr_details.get("labels", []),
                        "author": mr_details.get("author", {}).get("name", ""),
                        "merged_at": mr_details.get("merged_at", ""),
                        "url": f"{GITLAB_HOST}/{project_path}/-/merge_requests/{mr['iid']}",
                        "pipeline_url": mr_details.get("pipeline", {}).get("web_url", ""),
                    })
                else:
                    logger.warning(f"Error fetching details for merge request {mr['iid']}: {mr_details_resp.json()}")
        else:
            logger.warning(f"Error fetching merge requests for commit {commit_sha}: {mr_resp.json()}")

    # filter merge requests that related to the subpath
    subpath = config.get("subpath", None)
    if is_empty_string(subpath):
        return merge_requests
    scoped_merge_requests = []
    for mr in merge_requests:
        mr_iid = mr["iid"]
        mr_url = f"{GITLAB_HOST}/api/v4/projects/{encoded_project_path}/merge_requests/{mr_iid}/diffs"
        logger.info(f"Fetching merge request diff from {mr_url}")
        mr_diff_resp = requests.get(mr_url, headers=headers, timeout=10)
        if mr_diff_resp.status_code == 200:
            mr_diff = mr_diff_resp.json()
            for diff in mr_diff:
                if diff["new_path"].startswith(subpath) or diff["old_path"].startswith(subpath):
                    scoped_merge_requests.append(mr)
                    break
        else:
            logger.warning(f"Error fetching merge request diff for {mr_iid}: {mr_diff_resp.json()}")

    return scoped_merge_requests


def is_empty_string(string: str | None) -> bool:
    return string is None or string == ""


def get_config_filepaths(paths: list[str]) -> dict[str, list[str]]:
    config_files = {}
    for path in paths:
        if path == "" or path is None:
            continue
        if not os.path.exists(path):
            raise Exception(f"Directory path {path} does not exist")
        if not path.startswith("charts/"):
            continue
        service_name = path.split("/")[1]

        if service_name in config_files:
            continue

        service_dir = "/".join(path.split("/")[:2])

        stg_config = f"{service_dir}/config.stg.json"
        prd_config = f"{service_dir}/config.prd.json"
        if not os.path.exists(stg_config) or not os.path.exists(prd_config):
            continue
        config_files[service_name] = [stg_config, prd_config]

    return config_files


def run_compare_config():
    """
    Compares and processes configuration JSON files to identify changes and report them. The function performs
    this operation as follows:
    1. Creates a temporary output directory for storing configuration difference markdown files.
    2. Fetches the latest changes from the master branch.
    3. Identifies file changes and retrieves configuration file paths for services.
    4. Compares configuration JSON files using a comparison script, stores the differences in markdown format,
       and applies an optional configuration for comparison if available.
    5. Posts the resulting configuration difference markdown files as comments in the merge request.

    The function attempts to generate detailed markdown files for service configuration differences and handle
    posting these files as comments in a merge request.

    :raises FileNotFoundError: If a required file or directory does not exist.
    :raises subprocess.CalledProcessError: If the comparison script or any subprocess fails during execution.
    :raises requests.exceptions.RequestException: If an HTTP request fails while posting comments.

    :return: None
    """
    output_root_dir = "tmp/config-diff"
    os.makedirs(output_root_dir, exist_ok=True)
    result = fetch_master()
    logger.info(result.stdout.decode())
    commits = get_file_changes()
    config_files = get_config_filepaths(commits)

    cwd = os.getcwd()
    logger.info(f"Current working directory: {cwd}")

    # Generate the config diff in table format and output them to respective markdown file
    for service, files in config_files.items():
        logger.info(f"Generating diff table for service: {service}")
        logger.info(f"Files: {files}")
        args = ["python", "scripts/compare_json.py", files[0], files[1],
                "--format", "markdown", "--output", f"{output_root_dir}/{service}-diff.md",
                "--name", service]
        service_dir = "/".join(files[0].split("/")[:-1])
        diff_config = f"{service_dir}/compare_config.json"
        if os.path.exists(diff_config):
            args.extend(["--config", diff_config])
        try:
            result = subprocess.run(
                args,
                cwd=cwd, capture_output=True, check=True
            )
            print(result.stdout.decode())
        except subprocess.CalledProcessError as e:
            logger.warning(f"Error comparing JSON files: {e.stderr.decode()}")
            continue

    # list all the file in the output root dir and process them
    output_files = os.listdir(output_root_dir)
    for file in output_files:
        if os.path.isdir(file):
            continue
        logger.info(f"Posting diff table as comment for file: {file}")
        with open(f"{output_root_dir}/{file}", "r") as f:
            content = f.read()
        content = f"<details><summary>View config diff</summary>\n\n{content}\n\n</details>"
        logger.info(f"Creating comment in merge request from {file}")
        api_resp = requests.post(
            f"{GITLAB_HOST}/api/v4/projects/{GITLAB_PROJECT_ID}/merge_requests/{CI_MERGE_REQUEST_IID}/notes",
            headers={"PRIVATE-TOKEN": GITLAB_TOKEN, "Content-Type": "application/json"},
            json={"body": content, "internal": True},
            timeout=10
        )
        logger.info(f"status code: {api_resp.status_code}")
        if api_resp.status_code > 299:
            logger.warning(api_resp.json())


def run_config_change_reminder():
    """
    Processes configuration file changes and generates reminders about required
    configuration changes for specific builds based on detected file modifications,
    associated commits, and filtered merge requests.

    The function performs the following steps:
    1. Detects changed files.
    2. Retrieves commit changes for each file.
    3. Identifies and processes configuration files for changed services.
    4. Fetches and filters merge requests associated with the changes.
    5. Generates and posts internal notes summarizing configuration updates.

    :return: None
    """
    changed_files = get_file_changes()
    if len(changed_files) == 0:
        logger.info("No changed files detected")
        return
    # sort the dict in the list by one of the dict key
    sorted(changed_files)
    value_file_commits = []
    for changed_file in changed_files:
        commits = get_build_commit_changes(changed_file)
        if commits is None or len(commits) == 0:
            continue
        value_file_commits.append(commits)

    if value_file_commits is None or len(value_file_commits) == 0:
        logger.info("No value file changes detected")
        return
    counter = 0
    config_store = dict()
    for commit in value_file_commits:
        counter += 1
        logger.info(f"Processing for {commit.get('value_filepath')} [{counter}/{len(value_file_commits)}]")
        try:
            changed_value_file = commit["value_filepath"]
            conf_filepath = f"charts/{commit['service_name']}/compare_config.json"
            # load config processing config file for the service
            if not os.path.exists(conf_filepath):
                logger.warning(f"File {conf_filepath} does not exist. Skipping it...")
                continue
            if commit["service_name"] not in config_store:
                conf = json.load(open(conf_filepath))
                config_store[commit["service_name"]] = conf
            else:
                conf = config_store[commit["service_name"]]

            merge_requests = get_diff_merge_requests(commit["base_commit"], commit["latest_commit"],
                                                     {"project_path": conf.get("project_path"),
                                                      "subpath": conf.get("subpath")})
            logger.info(f"Found {len(merge_requests)} merge requests for {commit['service_name']}")
            if len(merge_requests) == 0:
                logger.info(f"No merge requests found for {commit['service_name']}")
                continue
            filtered_merge_requests = [
                mr for mr in merge_requests if ATTENTION_LABELS.intersection(set(mr["labels"]))
            ] if os.getenv("FILTER_CONFIG_CHANGE_MR") == "true" else merge_requests
            logger.info(f'Found {len(filtered_merge_requests)} merge requests with config changes')

            if len(filtered_merge_requests) == 0:
                logger.info(f"No merge requests with config changes found for {commit['service_name']}")
                continue
            # generate internal notes
            md_str = f"## Following merge requests cause this build\n\n"
            md_str += f"### {changed_value_file} \n\n"
            md_str += "Refer to the following merge requests for more details. Pay attention to MR with 🔥 as config change might be required for release.\n\n"
            md_str += f"|MR title|Author|Pipeline|Merge At|Labels|\n"
            md_str += f"|---|---|---|---|---|\n"

            for mr in filtered_merge_requests:
                labels_text = ", ".join(mr.get("labels", []))
                mr_title = f"[{mr['title']}]({mr['url']})"
                if ATTENTION_LABELS.intersection(set(mr.get("labels", []))):
                    mr_title = f"🔥 {mr_title}"
                md_str += f"|{mr_title}|{mr['author']}|[pipeline]({mr['pipeline_url']})|{mr['merged_at']}|{labels_text}|\n"
            md_str += f"\n\n"
            logger.debug(f'sending internal note to gitlab')
            api_resp = requests.post(
                f"{GITLAB_HOST}/api/v4/projects/{GITLAB_PROJECT_ID}/merge_requests/{CI_MERGE_REQUEST_IID}/notes",
                headers={"PRIVATE-TOKEN": GITLAB_TOKEN, "Content-Type": "application/json"},
                json={"body": md_str, "internal": True},
                timeout=10
            )
            if api_resp.status_code > 299:
                logger.warning(f"Error posting comment: {api_resp.json()}")
        except Exception as e:
            logger.warning(f"Failed to process {json.dumps(commit)}")
            logger.warning(f'Error posting comment: {e}')
            continue


if __name__ == "__main__":
    logger.info(f"Current directory: {os.getcwd()}")
    # compare config
    try:
        logger.info("Comparing config...")
        run_compare_config()
    except Exception as e:
        logger.warning(f'Error comparing config: {e}')

    # detect if config changes is needed from merge request label
    try:
        logger.info("Running config changes reminder...")
        run_config_change_reminder()
    except Exception as e:
        logger.warning(f'Error getting merge request: {e}')
