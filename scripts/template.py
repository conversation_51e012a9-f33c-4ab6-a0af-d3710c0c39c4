import os
import sys
import json
import requests
import subprocess
import re
import random
import time

# Get environment variables
SERVICE_NAME = os.getenv("PARENT")
ENV = os.getenv("ENV")
BRANCH = os.getenv("CI_COMMIT_REF_NAME")
GITLAB_TOKEN = os.getenv("GITLAB_TOKEN")
GITLAB_API_URL = os.getenv("CI_API_V4_URL")
PROJECT_ID = os.getenv("CI_PROJECT_ID")
MR_IID = os.getenv("CI_MERGE_REQUEST_IID")
ARGOCD_AUTH_TOKEN = os.getenv("ARGOCD_AUTH_TOKEN")

syncOutput = None
errorOutput = None
result = None
comment = None
errorBool = None

BASE_WAIT = 30
MAX_RETRIES = 5

class RetryError(Exception):
	"""A custom exception class"""
	def __init__(self, message):
		super().__init__(message)  # Call the parent class constructor with the message
		self.message = message

	def __str__(self):
		return f"RetryError: {self.message}"


def execute_cli():
	global syncOutput
	global errorOutput
	global result
	global comment
	global errorBool

	sync_command = [
		"argocd", "app", "sync",
		"--grpc-web",
		"--server", "argocd.g-bank.app",
		"--auth-token", ARGOCD_AUTH_TOKEN,
		"--selector", f"service={SERVICE_NAME},environment={ENV}",
		"--revision", BRANCH,
		"--dry-run"
	]

	print(f"Executing sync for {SERVICE_NAME} in environment {ENV}")
	result = subprocess.run(sync_command, capture_output=True, text=True)
	syncOutput = result.stdout.strip()
	errorOutput = result.stderr.strip()
	syncOutput = syncOutput.replace("```", "\\`\\`\\`")

	# Handle cases where environment does not exist for certain service
	if result.returncode == 1:
		print(f"{errorOutput}")
		sys.exit(0)

	elif result.returncode != 0:
		raise RetryError("Retry")

	# Search for phase error message, as error phase will still return status code 0
	elif re.search(r"phase:\s*error", syncOutput, re.IGNORECASE) or re.search(r"phase:\s*failed", syncOutput, re.IGNORECASE) :
		# Format the GitLab comment with Markdown
		comment = f"""
### ❌ ArgoCD Dry Run Failed for -> (project: {ENV}-{SERVICE_NAME})

📂 **Service Name:** `{SERVICE_NAME}`  
🌍 **Environment:** `{ENV}`   

```diff
{syncOutput}
		"""
		print(comment)
		# Exit with non zero status code
		errorBool = True
		
	else:
		comment = f"""
### ✅ ArgoCD Dry Run Succeeded for -> (project: {ENV}-{SERVICE_NAME})

📂 **Service Name:** `{SERVICE_NAME}`  
🌍 **Environment:** `{ENV}`   

```diff
{syncOutput}
		"""
		print(comment)

# Ensure required environment variables are present
if not GITLAB_TOKEN or not GITLAB_API_URL or not PROJECT_ID or not MR_IID:
	print("Error: Missing GitLab environment variables.", file=sys.stderr)
	sys.exit(1)

# Run ArgoCD sync command
try:
	execute_cli()
	
except RetryError:
	print(f"Error: ArgoCD sync command error.\n{errorOutput}", file=sys.stderr)
	for attempt in range(MAX_RETRIES + 1):  # +1 to include the initial attempt
		try:
			print("Rerunning command due to error....")
			execute_cli()  # Try executing the function
		except RetryError as e:
			print(f"Error: ArgoCD sync command error.\n{errorOutput}", file=sys.stderr)
			if attempt == MAX_RETRIES:
				print(f"[Attempt {attempt}] All retries failed.")
				raise e  # Re-raise the last exception
			wait_time = BASE_WAIT * (2 ** attempt)  # Exponential backoff
			# Optional: Add jitter to avoid thundering herd problem
			jitter = random.uniform(0, 1)
			wait_time += jitter
			print(f"[Attempt {attempt}] Error: {e}. Retrying in {wait_time:.2f} seconds...")
			time.sleep(wait_time)
		else:
			break

except FileNotFoundError:
	print("Error: ArgoCD CLI is not installed.", file=sys.stderr)
	sys.exit(1)


GITLAB_COMMENT_URL = f"{GITLAB_API_URL}/projects/{PROJECT_ID}/merge_requests/{MR_IID}/notes"
headers = { "PRIVATE-TOKEN": GITLAB_TOKEN, "Content-Type": "application/json" }

print("Posting comment in gitlab...")
response = requests.post(GITLAB_COMMENT_URL, headers=headers, json={"body": comment}, timeout=10)


if response.status_code != 201:
	print(f"Error posting comment: {response.text}", file=sys.stderr) 
	sys.exit(1)
elif errorBool:
	print("Exiting due to sync error")
	sys.exit(1)
else:   
	print("✅ Comment posted successfully!")
