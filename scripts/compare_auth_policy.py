import argparse
import glob
import json
from os import path

# third party packages
import yaml

# projects = glob.glob('../charts/customer-master/*')
# projects = ['/Users/<USER>/grab-projects/dbmy/deploy/helm/charts/customer-master']
MISSING_RULES_IN_TARGET = {}


def build_rules_dict(rules):
    dict = {}
    for rule in rules:
        if rule.get("endpoints") is None:
            continue
        for src in rule["sources"]:
            for ep in rule["endpoints"]:
                if dict.get(src) is None:
                    dict[src] = set()

                # each endpoint has methods and paths
                for method in ep["methods"]:
                    for p in ep.get("paths", []):
                        dict[src].add(method + " " + p)
    return dict


def main(owner: str, src_env: str, target_env: str, filtered_services: list[str], output_filepath: str) -> bool:
    has_error = False
    projects = [f'charts/{owner}']
    if owner == "all":
        projects = glob.glob('charts/*')
    for project in projects:
        try:
            if path.exists(project + f'/values.{target_env}.yaml'):
                target = yaml.unsafe_load(open(project + f'/values.{target_env}.yaml'))
                source = yaml.unsafe_load(open(project + f'/values.{src_env}.yaml'))
                # check if AuthorizationPolicy is enabled, else skip
                if target is None or target.get("authorizationpolicy") is None or target["authorizationpolicy"][
                    "enabled"] is False:
                    print(f"INFO: AuthorizationPolicy is not found or disabled in  {project} target environment.")
                    continue

                target_rules = build_rules_dict(target["authorizationpolicy"].get('rules', []))
                source_rules = build_rules_dict(source["authorizationpolicy"].get('rules', []))

                for source_rule in source_rules:
                    key = "{}_{}.{}_{}".format("grantor", project.split("/")[-1], "grantee", source_rule.split("/")[-1])
                    source_rules_set = source_rules[source_rule]
                    target_rule_set = target_rules.get(source_rule)
                    if target_rule_set is None:
                        MISSING_RULES_IN_TARGET[key] = source_rules_set
                        continue
                    missing = source_rules_set - target_rule_set
                    if len(missing) > 0:
                        MISSING_RULES_IN_TARGET[key] = missing

            for key in MISSING_RULES_IN_TARGET:
                MISSING_RULES_IN_TARGET[key] = list(MISSING_RULES_IN_TARGET[key])
        except Exception as e:
            has_error = True
            print(f"ERROR: Failed to process {project}.")
            print(f"ERROR: {e}")

    output = MISSING_RULES_IN_TARGET
    # filter output by services
    if len(filtered_services) > 0:
        output = {}
        for key in MISSING_RULES_IN_TARGET:
            if _is_grantee_in_filtered_services(key, filtered_services):
                output[key] = MISSING_RULES_IN_TARGET[key]

    results = json.dumps(output, indent=4, sort_keys=True)

    # write output to file
    try:
        if output_filepath not in [None, "", "none"]:
            with open(output_filepath, "w") as file:
                json.dump(output, file, indent=4, sort_keys=True)
                print(f"INFO: Output written to {output_filepath}.")
    except Exception as e:
        has_error = True
        print(f"ERROR: Failed to write output to {output_filepath}.")
        print(f"ERROR: {e}")
    print(results)
    return has_error


def _is_grantee_in_filtered_services(grantee: str, filtered_services: list[str]) -> bool:
    for service in filtered_services:
        if f'grantee_{service}' in grantee:
            return True
    return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process RBAC rules.")
    parser.add_argument("-w", "--owner", required=True, help="Name of service that owns the authorization policy")
    parser.add_argument("-o", "--output", required=False, help="Output file")
    parser.add_argument("-e", "--src-env", required=True, help="Source environment")
    parser.add_argument("-t", "--target-env", required=True, help="Target environment")
    parser.add_argument("-f", "--filtered-services", required=False, default="none",
                        help="Only show missing rules for filtered services. Use comma separated for multiple services")
    args = parser.parse_args()
    filtered_services = args.filtered_services.split(
        ",") if args.filtered_services != 'none' and args.filtered_services is not None else []
    has_error = main(args.owner, args.src_env, args.target_env, filtered_services, args.output)
    if has_error:
        print("ERROR: Some errors occurred during the process, please inspect the console logs.")
        exit(1)
