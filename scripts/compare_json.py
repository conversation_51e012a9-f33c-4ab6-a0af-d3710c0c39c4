import argparse
import json
import os


def extract_json_paths(json_obj, path=""):
    """Extract all JSON paths from a nested JSON object."""
    paths = set()

    if isinstance(json_obj, dict):
        for key, value in json_obj.items():
            if key.startswith("_"):  # Skip private keys
                continue
            new_path = f"{path}.{key}" if path else key
            paths.add(new_path)
            paths.update(extract_json_paths(value, new_path))

    elif isinstance(json_obj, list):
        for i, item in enumerate(json_obj):
            new_path = f"{path}[{i}]"
            paths.add(new_path)
            paths.update(extract_json_paths(item, new_path))

    return paths


def is_excluded(path, excluded_paths):
    if excluded_paths is None or len(excluded_paths) == 0:
        return False

    """Check if a path is excluded."""
    for excluded_path in excluded_paths:
        if path == excluded_path or path.startswith(f"{excluded_path}.") or path.startswith(f"{excluded_path}["):
            return True
    return False


def deep_diff(json1, json2, exclude_paths=None, json1_name="", json2_name=""):
    if json1_name is None or json1_name == "":
        json1_name = "json1"
    if json2_name is None or json2_name == "":
        json2_name = "json2"

    """Perform a deep diff between two JSON objects."""
    if exclude_paths is None:
        exclude_paths = set()

    differences = []
    json1_paths = extract_json_paths(json1)
    json2_paths = extract_json_paths(json2)
    all_paths = json1_paths.union(json2_paths)

    for path in sorted(all_paths):
        if is_excluded(path, exclude_paths):
            continue

        keys = path.replace("[", ".[").split(".")  # Handle nested dicts and lists
        value1 = json1
        value2 = json2

        try:
            for key in keys:
                if key.startswith("[") and key.endswith("]"):
                    index = int(key[1:-1])
                    value1 = value1[index] if isinstance(value1, list) and index < len(value1) else None
                    value2 = value2[index] if isinstance(value2, list) and index < len(value2) else None
                else:
                    value1 = value1.get(key) if isinstance(value1, dict) else None
                    value2 = value2.get(key) if isinstance(value2, dict) else None
        except (KeyError, IndexError, TypeError):
            value1 = None
            value2 = None

        if isinstance(value1, dict) or isinstance(value1, list) or isinstance(value2, dict) or isinstance(value2, list):
            continue
        if value1 != value2:
            differences.append({"path": path, json1_name: value1, json2_name: value2})

    return differences


def output_json(differences):
    """Return differences in JSON format."""
    return json.dumps(differences, indent=2)


def output_markdown_table(differences, json1_name="value1", json2_name="value2", title="Differences", format="compact"):
    """Return differences in Markdown table format."""
    if not differences:
        return "No differences found."

    if not title or title == "":
        title = "Differences"

    if not format or format == "":
        format = "compact"

    # Print the diff single column layout.
    # Each row will contain the path and the values of the two JSON files, separated by a line break.
    table = ""
    if format == "compact":
        table = "## {}\n\n| Path |\n|------|\n".format(title)
        for diff in differences:
            row = f"<b>{diff['path']}</b>"
            row += f"<br/>{json1_name}: `{json.dumps(diff[json1_name], ensure_ascii=False)}`<br/>{json2_name}: `{json.dumps(diff[json2_name], ensure_ascii=False)}`"
            table += f"| {row} |\n"

    # Print the diff in multi column layout.
    # The columns will contain the path, the value of the first JSON file, and the value of the second JSON file.
    if format == "long":
        table = "## {}\n\n| Path | {} | {} |\n|------|---------|---------|\n".format(title, json1_name, json2_name)
        for diff in differences:
            row = diff["path"]
            value1 = json.dumps(diff[json1_name], ensure_ascii=False)
            value2 = json.dumps(diff[json2_name], ensure_ascii=False)
            table += f"| {row} | {value1} | {value2} |\n"

    return table


def load_json(filepath):
    """Load JSON data from a file."""
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"File not found: {filepath}")
    with open(filepath, "r", encoding="utf-8") as f:
        return json.load(f)


def load_exclusions(conf):
    if not conf:
        return set()
    exclusion = conf.get("exclude", [])
    if not isinstance(exclusion, list):
        raise ValueError("Exclusion must be a list")
    return set(exclusion)


def load_config(filepath) -> dict:
    """Load exclusion paths from a JSON file."""
    if not filepath:
        return {}
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Exclusion file not found: {filepath}")
    with open(filepath, "r", encoding="utf-8") as f:
        return json.load(f)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Perform a deep diff between two JSON files.")
    parser.add_argument("json1", help="Path to the first JSON file")
    parser.add_argument("json2", help="Path to the second JSON file")
    parser.add_argument("--format", choices=["json", "markdown"], default="json", help="Output format (default: json)")
    parser.add_argument("--config", help="Path to JSON file containing exclusion paths (optional)")
    parser.add_argument("--output", help="Path to output file (optional)")
    parser.add_argument("--name", help="Name of this diff ops (optional), for UX purpose")

    args = parser.parse_args()

    # Load JSON data
    json1 = load_json(args.json1)
    json2 = load_json(args.json2)

    json1_name = args.json1.split("/")[-1]
    json2_name = args.json2.split("/")[-1]

    # Load config
    config = load_config(args.config)

    # Load exclusion paths
    excluded_paths = load_exclusions(config)

    # Compute differences
    differences = deep_diff(json1, json2, exclude_paths=excluded_paths, json1_name=json1_name, json2_name=json2_name)

    formatted_output = None
    # Output results in the requested format
    if args.format == "json":
        formatted_output = output_json(differences)
    else:
        formatted_output = output_markdown_table(
            differences, json1_name=json1_name, json2_name=json2_name, title=args.name
        )

    if args.output:
        with open(args.output, "w") as f:
            f.write(formatted_output)

    print(formatted_output)
