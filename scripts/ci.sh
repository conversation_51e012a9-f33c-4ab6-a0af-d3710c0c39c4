#!/usr/bin/env bash

set -e

CURRENT_DIR=${BASH_SOURCE:-scripts}
echo $CURRENT_DIR

function list_files {
  printf '%s' "$(git diff --name-only "$1" | grep '^charts/' | grep -Ev '(^charts/go-app-lib/|infra-central/argowf-common/)' | awk -F'/' '{if($2 ~ /infra-central/) {print $2 "/" $3 } else {print $2}}' | sort | uniq)"
}

git fetch --all --no-tag

if [[ "$CI_COMMIT_SHA" = "$(git rev-parse origin/main)" ]]; then
  files="$(list_files "$(git log --merges --pretty=format:'%h' | awk 'NR==2')")"
else
  files="$(list_files "$(git merge-base "$CI_COMMIT_SHA" origin/main)")"
  echo "Affected services = $files"
fi

for f in $files; do
  if [[ -f ${CURRENT_DIR%/*}/../charts/$f/Chart.yaml ]]; then
    env=dev
    if [[ -f "${CURRENT_DIR%/*}/../charts/$f/.values.cen.yaml" ]]; then
      env=cen
    fi
    echo "Rendering Helm chart of [${env}] $f..."
    ${CURRENT_DIR%/*}/helm-template.sh ${CURRENT_DIR%/*}/../charts/$f ${env}
    cat ${CURRENT_DIR%/*}/../charts/$f/output.${env}.yaml
    echo "OK"
  fi
done
