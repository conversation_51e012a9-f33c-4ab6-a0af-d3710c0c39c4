#!/bin/bash

if [ $# -lt 2 ]; then
    echo "It needs two params: service and environment"
    exit 1
fi

BASE_DIR=$(dirname "$0")
CHART_DIR=$BASE_DIR/../charts
ENV=$2
REGION=$3

# adapt to cicd/common/k8s-deploy.sh, select environment values
if [ $2 = 'sgdev' ] || [ $2 = 'sgcb-dev' ]; then
    ENV='dev'
fi

CURRENT_DIR=${BASH_SOURCE:-scripts}
# TARGET_NS=$(${CURRENT_DIR%/*}/set-namespace.sh ${1##*/} $2)
# echo $TARGET_NS
values_file="values.${ENV}.yaml"
dot_values_file=".values.${ENV}.yaml"
output_file="output.${ENV}.yaml"
if [ -n "${REGION}" ];
then
  values_file="values.${REGION}.${ENV}.yaml"
  dot_values_file=".values.${REGION}.${ENV}.yaml"
  output_file="output.${REGION}.${ENV}.yaml"
fi
# get the last elements of the path as the chart name
chart_name=${1##*/}
service_chart_dir=$CHART_DIR/${1#*/}

# infra-central charts are nested, we handle them differently
if [ "${1%/*}" = "infra-central" ]; then
  service_chart_dir=$CHART_DIR/$1
fi

helm dependency update ${service_chart_dir}/
#helm template ${1##*/} $CHART_DIR/${1##*/}/ -f $CHART_DIR/go-app-lib/values.yaml -f $CHART_DIR/${1##*/}/values.$ENV.yaml -f $CHART_DIR/${1##*/}/.values.$ENV.yaml --wait --debug --dry-run > $CHART_DIR/${1##*/}/output.$ENV.yaml -n $TARGET_NS
helm template $chart_name ${service_chart_dir}/ -f $CHART_DIR/base/values.yaml -f ${service_chart_dir}/$values_file -f ${service_chart_dir}/${dot_values_file} --wait --debug --dry-run > ${service_chart_dir}/$output_file
