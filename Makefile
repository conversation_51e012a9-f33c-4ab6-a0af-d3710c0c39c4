.PHONY: compare-auth-policy test-compare-json test-compare-config

compare-auth-policy:
	@echo 'Usage make compare-auth-policy owner=<service> src=<src> dest=<dest> filters=<filter> output=<output-path>.'
	@echo '"owner=all" means checking against all policy owner services'
	@echo '"filters" argument is optional'
	@echo '"output" argument is optional'
	@echo "Example: make compare-auth-policy service=customer-master src=stg dest=prd filters=account-service,loan-core"
	@echo "Listing policies owned by owner=$(owner) in src=$(src) but not in dest=$(dest) for filter=$(filters)"
	@(\
  		if [ -d venv/bin ]; then \
  			source venv/bin/activate; \
  		fi; \
  		flags="-w $(owner) -e $(src) -t $(dest)"; \
		if [ -n "$(filters)" ]; then \
			flags+=" -f $(filters)"; \
  		fi; \
		if [ -n "$(output)" ]; then \
			flags+=" -o $(output)"; \
  		fi; \
  		echo "Running python script with flags: $$flags"; \
  		python3 scripts/compare_auth_policy.py $$flags; \
    )

test-compare-json:
	@(\
		if [ -d venv/bin ]; then \
			source venv/bin/activate; \
		fi; \
		flags=""; \
		if [ -n "$(format)" ]; then \
			flags+=" --format $(format)"; \
		fi; \
		if [ -n "$(config)" ]; then \
			flags+=" --config $(config)"; \
		fi; \
		if [ -n "$(output)" ]; then \
			flags+=" --output $(output)"; \
		fi; \
		echo "Running python script with flags: $$flags"; \
		python3 scripts/compare_json.py $(json1) $(json2) $$flags; \
	)

test-compare-config:
	@(\
		if [ -d venv/bin ]; then \
			source venv/bin/activate; \
		fi; \
		branch=""; \
		commit=""; \
		if [ -z "$(branch)" ]; then \
			branch="main"; \
		fi; \
		if [ -z "$(commit)" ]; then \
			commit="a1a66a689"; \
		fi; \
		CI_MERGE_REQUEST_TARGET_BRANCH_NAME=$$branch CI_MERGE_REQUEST_DIFF_BASE_SHA=$$commit python3 scripts/compare_config.py; \
	)
