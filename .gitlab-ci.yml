stages:
  - config
  - validate
  - deploy

variables:
  ECR_REPO: 712221657655.dkr.ecr.ap-southeast-1.amazonaws.com

compare-config:
  stage: config
  tags:
    - dbmy-infra-01
  image:
    name: ${ECR_REPO}/mirror/docker.io/library/python:3.11.9-bullseye
    entrypoint: [""]
  rules:
    - if: $CI_MERGE_REQUEST_ID
  script:
    - pip install requests
    - pip install pyyaml
    - pip install colorlog
    - python scripts/compare_config.py
  allow_failure: true

get-changed-files:
  stage: config
  tags:
    - dbmy-infra-01
  image:
    name: ${ECR_REPO}/mirror/docker.io/alpine/k8s:1.25.13
  script:
    - |
      git config --global user.email "<EMAIL>"
      git config --global user.name "Gitlab CI"
      git fetch --unshallow origin && git rebase origin/main
      CHANGED_FILES=$(git diff --name-only origin/${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}..HEAD | grep -v '^\.')
      FILTERED_FILES=$(echo "$CHANGED_FILES" | grep '^charts/' || true)  

      echo "$FILTERED_FILES" > changed_files.txt
      cat changed_files.txt
  artifacts:
    paths:
      - changed_files.txt
  rules:
    - if: $CI_MERGE_REQUEST_ID
      changes:
        - charts/**/*
  allow_failure: true

sync-environment:
  stage: validate
  needs:
    - job: get-changed-files
      artifacts: true
  tags:
    - dbmy-infra-01
  image:
    name: ${ECR_REPO}/build/helm-ci:v3

  script:
    - |
      CHANGED_FILES=$(cat changed_files.txt)
      declare -A ENVIRONMENTS=()
      declare -A PARENT_ENV_MAPPING=()
      PARENT_DIRS=()

      for FILE in $CHANGED_FILES; do
        echo "Processing file: '$FILE'"
        FILE=${FILE#charts/}
        
        PARENT_DIR=$(echo "$FILE" | cut -d'/' -f1)
        # Debug: Print current file and parent directory
        
        echo "Parent directory: $PARENT_DIR"
        
        # Debug: Print the value of $FILE to check for hidden characters or issues
         echo "Checking regex match for: $FILE"

        if [[ ! " ${PARENT_DIRS[@]} " =~ " ${PARENT_DIR} " ]]; then
          PARENT_DIRS+=("$PARENT_DIR")
        fi

        # Check if the file contains 'prd' (and exclude it from processing)
        if [[ $FILE == *prd* ]]; then
            echo "Skipping file due to 'prd' in filename: $FILE"
            continue
        fi
        if [[ "$FILE" =~ ^(.*/)?values\.(.+)\.yaml$ || "$FILE" =~ ^(.*/)?config\.(.+)\.json$ || "$FILE" =~ ^(.*/)?\.?values\.(.+)\.yaml$ ]]; then
            echo "File matches regex, capturing environment..."
            ENV=${BASH_REMATCH[2]}  
            # Debug: Print the captured environment
            echo "Captured ENV: $ENV"
            ENVIRONMENTS["$ENV"]=1
            PARENT_ENV_MAPPING["$PARENT_DIR"]="$ENV" 
            # Debug: Print the mappings
            echo "ENVIRONMENTS[$ENV]: ${ENVIRONMENTS[$ENV]}"
            echo "PARENT_ENV_MAPPING[$PARENT_DIR]: ${PARENT_ENV_MAPPING[$PARENT_DIR]}"
        elif [[ $FILE =~ templates/ ]]; then
          ENVIRONMENTS["dev"]=1
          ENVIRONMENTS["stg"]=1
          ENVIRONMENTS["cen"]=1
          PARENT_ENV_MAPPING["$PARENT_DIR"]="dev stg cen"
          break ## prevent modification of 2 charts manifest in one MR
        fi
      done

      # The loop:
      for PARENT in "${PARENT_DIRS[@]}"; do
          echo "Processing Parent: $PARENT"
          export PARENT=$PARENT  # Export PARENT for further use in the loop

          ENV=${PARENT_ENV_MAPPING[$PARENT]}  # Get the environments for this parent
          for ENV in $ENV; do
              echo "Processing Environment: $ENV"
              export ENV=$ENV  # Export ENV for use in the template script

              if [[ ${ENVIRONMENTS[$ENV]} -eq 1 ]]; then
                  echo "Running Python script for $PARENT/$ENV"
                  python3 scripts/template.py  # Execute the script
              fi
          done
      done
  rules:
    - if: $CI_MERGE_REQUEST_ID
      changes:
        - charts/**/*

dev:
  stage: deploy
  tags:
    - dbmy-infra-01
  image: ${ECR_REPO}/build/deploy:20230319
  rules:
    - if: $DEPLOY_TO == 'dev' && $SERVICE_NAME != 'sentry-t6' && $SERVICE_NAME != 'partner-gateway'
  before_script:
    - if [[ -z $SERVICE_NAME || -z $IMAGE_TAG ]]; then echo '$SERVICE_NAME and $IMAGE_TAG are required'; exit 1; fi
    - "[[ ! -d 'tmp' ]] && mkdir tmp"
    - |
      echo "DEPLOY_TO=${DEPLOY_TO}"
      echo "SERVICE_NAME=${SERVICE_NAME}"
      echo "IMAGE_TAG=${IMAGE_TAG}"
      echo "PROJECT_URL=${PROJECT_URL}"
      echo "MERGE_REQUEST_SOURCE_PROJECT_URL=${MERGE_REQUEST_SOURCE_PROJECT_URL}"
      echo "MERGE_REQUEST_IID=${MERGE_REQUEST_IID}"
      echo "MERGE_REQUEST_TITLE=${MERGE_REQUEST_TITLE}"
      echo "DEPLOY_BY=${DEPLOY_BY}"
      echo "CI_JOB_URL=${CI_JOB_URL}"
      echo "COMMIT_SHA=${COMMIT_SHA}"
      echo "COMMIT_MESSAGE=${COMMIT_MESSAGE}"
    - |
      if [[ -n "${MERGE_REQUEST_IID}" ]]
      then 
        export REF_URL=${PROJECT_URL}/-/merge_requests/${MERGE_REQUEST_IID}
      elif [[ -n "${COMMIT_SHA}" ]]
      then
        export REF_URL=${PROJECT_URL}/-/commit/${COMMIT_SHA}
      else
        export REF_URL=${PROJECT_URL}
      fi
      echo REF_URL=${REF_URL}
    - |
      if [[ -n "${MERGE_REQUEST_TITLE}" ]]
      then
        export TITLE=${MERGE_REQUEST_TITLE}
      else
        export TITLE=${COMMIT_MESSAGE}
      fi
      echo TITLE=${TITLE}
    - "if [[ -n ${CI_PIPELINE_URL} ]]; then export PIPELINE_URL=${CI_PIPELINE_URL};else export PIPELINE_URL=${REF};fi"
    - |
      ( echo "cat <<EOF > tmp/slack-deploy.json";
        cat cicd/common/slack-deploy.tmpl.json;
        echo "";
        echo "EOF";
      ) > tmp/values.sh
    - source tmp/values.sh
    # debugger
    #    - "while true; do sleep 1; [[ -f stop.txt ]] && break; done"
    - ./scripts/notify_deployment.sh ${GBANK_SLACK_DEVOPS_BOT_TOKEN} || true
  script:
    - |
      ( echo "cat <<EOF > charts/${SERVICE_NAME}/.values.dev.yaml";
        cat cicd/common/values.tmpl.yml;
        [ ! -z $MYSQL_IMAGE_TAG ] && cat cicd/common/mysql.tmpl.yml;
        echo "";
        echo "EOF";
      ) > values.sh
    - git fetch --all --unshallow --no-tag || git fetch --all --no-tag
    - git checkout main
    - source values.sh
    - cat charts/${SERVICE_NAME}/.values.dev.yaml
    - git add charts/${SERVICE_NAME}/.values.dev.yaml
    - 'git commit -m "ci: deploy ${SERVICE_NAME} to dev (${IMAGE_TAG})"'
    - cat .git/config
    - git remote set-url origin https://${DBMY_CI_USER}:${DBMY_CI_TOKEN}@gitlab.com/gx-regional/dbmy/deploy/helm.git
    - cat .git/config
    - git push origin main

t6_dev:
  stage: deploy
  tags:
    - dbmy-infra-01
  image: ${ECR_REPO}/build/deploy:********
  rules:
    - if: $DEPLOY_TO == 'dev' && ($SERVICE_NAME == 'sentry-t6' || $SERVICE_NAME == 'partner-gateway')
  before_script:
    - if [[ -z $SERVICE_NAME || -z $T6_IMAGE_TAG || -z $T6_RATE_LIMITER_IMAGE_TAG ]]; then echo '$SERVICE_NAME, $T6_IMAGE_TAG and $T6_RATE_LIMITER_IMAGE_TAG are required'; exit 1; fi
    - T6_IMAGE_REPO_NAME='t6-gateway' && T6_RATE_LIMITER_IMAGE_REPO_NAME='t6-ratelimiter'
    - if [[ $SERVICE_NAME == 'partner-gateway' ]]; then T6_IMAGE_REPO_NAME='t6-partner-gateway' && T6_RATE_LIMITER_IMAGE_REPO_NAME='t6-partner-ratelimiter'; fi
  script:
    - |
      ( echo "cat <<EOF > charts/${SERVICE_NAME}/.values.dev.yaml";
        cat cicd/common/t6-values.tmpl.yml;
        echo "";
        echo "EOF";
      ) > values.sh
    - git fetch --all --unshallow --no-tag || git fetch --all --no-tag
    - git checkout main
    - source values.sh
    - cat charts/${SERVICE_NAME}/.values.dev.yaml
    - git add charts/${SERVICE_NAME}/.values.dev.yaml
    - 'git commit -m "ci: deploy ${SERVICE_NAME} to dev (t6-envoy ${T6_IMAGE_TAG}, t6-rate-limiter ${T6_RATE_LIMITER_IMAGE_TAG})"'
    - cat .git/config
    - git remote set-url origin https://${DBMY_CI_USER}:${DBMY_CI_TOKEN}@gitlab.com/gx-regional/dbmy/deploy/helm.git
    - cat .git/config
    - git push origin main

tm_deploy:
  stage: deploy
  tags:
    - dbmy-infra-01
  image: ${ECR_REPO}/build/deploy:********
  rules:
    - if: $TM_ENV == 'dev' && $SERVICE_NAME == 'tm-installer'
    - if: $TM_ENV == 'stg' && $SERVICE_NAME == 'tm-installer'
    - if: $TM_ENV == 'prd' && $SERVICE_NAME == 'tm-installer'
  before_script:
    - echo TM_ENV=$TM_ENV
    - echo TM_REGION_SHORT=$TM_REGION_SHORT
    - echo AWS_S3_ROLE=$AWS_S3_ROLE
    - echo S3_ARTEFACT_PATH=$S3_ARTEFACT_PATH
    - echo TM_VERSION=$TM_VERSION
    - echo SERVICE_NAME=$SERVICE_NAME
    - echo ACTION=$ACTION
    - echo DRYRUN=$DRYRUN
    - echo LOAD_S3=$LOAD_S3
    - echo TM_DEPLOY_TOKEN=$TM_DEPLOY_TOKEN
    - TM_REGION_SHORT_AND_ENV="${TM_REGION_SHORT:+$TM_REGION_SHORT.}${TM_ENV}"
    - echo TM_REGION_SHORT_AND_ENV=$TM_REGION_SHORT_AND_ENV
  script:
    - |
      ( echo "cat <<EOF > charts/${SERVICE_NAME}/.values.${TM_REGION_SHORT_AND_ENV}.yaml";
        cat cicd/tm/values.tmpl.yml;
        echo "";
        echo "EOF";
      ) > values.sh
    - git fetch --all --unshallow --no-tag || git fetch --all --no-tag
    - git checkout main
    - source values.sh
    - cat charts/${SERVICE_NAME}/.values.${TM_REGION_SHORT_AND_ENV}.yaml
    - git add charts/${SERVICE_NAME}/.values.${TM_REGION_SHORT_AND_ENV}.yaml
    - 'git commit -m "ci: deploy ${SERVICE_NAME} to ${TM_REGION_SHORT_AND_ENV} for version ${TM_VERSION}"'
    - cat .git/config
    - git remote set-url origin https://${DBMY_CI_USER}:${DBMY_CI_TOKEN}@gitlab.com/gx-regional/dbmy/deploy/helm.git
    - cat .git/config
    - git push origin main
