{"appConfig": {"mode": "staging", "structuredlogger": {"workerCount": 5, "bufferSize": 1000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "cgls", "development": true}, "statsd": {"host": "", "port": 8125, "appName": "feature-bank"}, "mysqlConfigs": [{"dbName": "guardian", "hystrixConfig": {"commandGroup": "feature-bank-rds", "errorPercentThreshold": 50, "maxConcurrentReq": 2000, "maxQueueSize": 2000, "sleepWindowInMs": 5000, "tag": "feature-bank", "timeoutInMs": 2000, "volumePercentThreshold": 20}, "mysqlConfig": {"master": {"dsn": "<vault.feature-bank-mysql>", "maxIdle": 2, "maxOpen": 20}, "slaves": [{"dsn": "<vault.feature-bank-mysql-replica>", "maxIdle": 2, "maxOpen": 20}]}}, {"dbName": "second-client", "hystrixConfig": {"commandGroup": "feature-bank-second-client-rds", "errorPercentThreshold": 50, "maxConcurrentReq": 2000, "maxQueueSize": 2000, "sleepWindowInMs": 5000, "tag": "feature-bank-second-client", "timeoutInMs": 2000, "volumePercentThreshold": 20}, "mysqlConfig": {"master": {"dsn": "<vault.feature-bank-second-client-mysql>", "maxIdle": 2, "maxOpen": 20}, "slaves": [{"dsn": "<vault.feature-bank-second-client-mysql-replica>", "maxIdle": 2, "maxOpen": 20}]}}, {"dbName": "grabgifts", "hystrixConfig": {"commandGroup": "feature-bank-grabgifts-rds", "errorPercentThreshold": 50, "maxConcurrentReq": 2000, "maxQueueSize": 2000, "sleepWindowInMs": 5000, "tag": "feature-bank-grabgifts", "timeoutInMs": 2000, "volumePercentThreshold": 20}, "mysqlConfig": {"master": {"dsn": "<vault.feature-bank-grabgifts-mysql>", "maxIdle": 2, "maxOpen": 20}, "slaves": [{"dsn": "<vault.feature-bank-grabgifts-mysql>", "maxIdle": 2, "maxOpen": 20}]}}], "s3Config": {"download_concurrency": 5, "leave_parts_on_error": false, "max_retry_attempt": 3, "max_upload_parts": 10000, "part_size": ********, "region": "ap-southeast-1", "upload_concurrency": 5}, "amphawaMetaConfig": {"bucket": "stg-market-view-meta", "interval": 60, "key": "stg/latest.tar.gz", "region": "ap-southeast-1"}, "amphawaHttpConfig": {"endpoint": "amphawa:8088", "prefix": "/marketstore", "timeoutInMs": 5000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}, "scratHttpConfig": {"endpoint": "http://scrat:8081", "prefix": "/gateway/scrat", "timeoutInMs": 5000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}, "scratMetaConfig": {"interval": 5, "table_name": "<PERSON>rat_Meta_Manual"}, "counterServiceMetaConfig": {"interval": 5}, "dynamoDBConfig": {"region": "ap-southeast-1", "table": "feature-bank-ddb-placeholder", "tag": "featurebank.ddb", "timeoutMs": 2000, "errorPercent": 75, "maxConcurrentRequest": 5000, "maxVolPercentThreshold": 20, "sleepWindowMs": 5000, "maxQueueSize": 6000, "commandGroup": "featurebank.ddb"}, "counterServiceHttpConfig": {"endpoint": "http://counter-service-lite:8081", "prefix": "/gateway/counterservicelite", "timeoutInMs": 5000, "maxConcurrentRequests": 500, "errorThresholdPercent": 75}, "ipGeoS3FileWatcherConfig": {"bucket": "nstg-feature-bank", "key": "geo/iptocountry/GeoLite2-Country-latest.mmdb", "region": "ap-southeast-1", "interval": 600}}, "dynamicConfig": {"amphawa.meta.cache.rollout": 0, "scrat.meta.cron.rollout": 100, "scrat.meta.tenants": "guardian,second-client,grabgifts", "scrat.featurebank.tenant.map": {"guardian": "guardian", "second-client": "second-client", "grabgifts": "grabgifts"}, "feature.scrat.entity_state_value.map": {"DenyList": "false", "AllowList": "true"}, "feature.scrat.entity_type_id.map": {"Email": "email", "email": "email", "consumer_id": "consumer_id", "merchant_id": "merchant_id", "phone_number": "phone_number", "device_id": "device_id", "account_id": "account_id", "email_domain": "email_domain", "customer_id": "customer_id"}, "counter_service.meta.cron.rollout": 100, "counter_service.sdk.rollout": 100, "counter_service.sdk.upgrade.rollout": 0, "counter.read.debug.log.rollout": 100, "counter.read.add.enable_cur_payload_val": true, "handler.postpredict.update.table": "nstg-feature-bank-user-info", "handler.tenant.feature-bank.postpredict.update.table": "nstg-feature-bank-user-info", "handler.tenant.guardian.postpredict.update.table": "nstg-feature-bank-user-info", "handler.tenant.second-client.postpredict.update.table": "nstg-feature-bank-second-client-user-info", "handler.tenant.grabgifts.postpredict.update.table": "nstg-feature-bank-grabgifts-user-info", "handler.debug.log.rollout": 100, "handler.datasetname.rollout": 100, "org.name.list": "guardian,second-client,grabgifts", "org.name.default": "guardian", "metadata.table.refresh.interval": 5, "metadata.feature.refresh.interval": 5, "metadata.dataset.refresh.interval": 5, "metadata.payloadCfg.refresh.interval": 60, "geo.ip.cacher.rollout": 100, "giestream.treatments.maxlength": 3}}