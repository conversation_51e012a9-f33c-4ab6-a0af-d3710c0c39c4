apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "featurebank.fullname" . }}
  labels:
    {{- include "featurebank.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
      {{- include "featurebank.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "featurebank.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "featurebank.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: FEATURE_BANK_BASE_CONF
            value: /var/config.json
          - name: DOGSTATSD_ADDR
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          - name: DATA_CONF
            value: /go/src/gitlab.myteksi.net/trust/grab-defence-base/commons/data/config-ci.json
          - name: FEATURE_BANK_VAULT_FOLDER
            value: /vault/secrets
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
          - name: POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: POD_SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                fieldPath: spec.serviceAccountName
          volumeMounts:
            - name: featurebank-conf
              mountPath: /var/config.json
              subPath: config.json
            - name: featurebank-secret-vol
              mountPath: /vault/secrets
          ports:
            - name: http
              containerPort: 8081
              protocol: TCP
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 20
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 6
            tcpSocket:
              port: 8081
          readinessProbe:
            initialDelaySeconds: 60
            periodSeconds: 20
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 6
            tcpSocket:
              port: 8081
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      volumes:
        - name: featurebank-conf
          configMap:
            name: featurebank-conf
        - name: featurebank-secret-vol
          secret:
            secretName: featurebank-secret
            items:
              - key: config-secret.json
                path: config-secret.json
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
