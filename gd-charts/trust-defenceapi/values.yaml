# Default values for defence_api.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/defence-api
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v_8eecf7eb"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "defence-read"

podAnnotations:
  co.elastic.logs/json.add_error_key: "true"
  co.elastic.logs/json.keys_under_root: "true"
  co.elastic.logs/json.message_key: message
  co.elastic.logs/json.overwrite_keys: "true"
  # https://www.elastic.co/guide/en/beats/filebeat/current/multiline-examples.html
  co.elastic.logs/multiline.pattern: '^(([a-zA-z\-_0-9]+_(INFO|WARN|ERROR|DEBUG|FATAL))|(\d{4}[\/-]\d{2}[\/-]\d{1,2})|(panic:))'
  co.elastic.logs/multiline.negate: "true"
  co.elastic.logs/multiline.match: "after"

configSecretDatadog: ****************************************************************************************************************************************************

configSecret: eyJkZWZlbmNlLWFwaS1pYW0tbXlzcWwiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKG5zdGctZGVmZW5jZS1hcGktaWFtLW15c3FsLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL2RlZmVuY2VfYXBpX2RlZmVuY2VfaWFtP3BhcnNlVGltZT10cnVlJmxvYz1VVEMiLCAiZGVmZW5jZS1hcGktbXlzcWwiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKG5zdGctZGVmZW5jZS1hcGktbXlzcWwubnBydi5zdGcuZ2RlZmVuY2UuaW86MzMwNikvZGVmZW5jZV9hcGk/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIsICJkZWZlbmNlLWFwaS1teXNxbC1zZWNvbmQtY2xpZW50IjogImdkYWRtaW46TmpSNEh5dmFHZE1HQHRjcChuc3RnLWRlZmVuY2UtYXBpLW15c3FsLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL2RlZmVuY2VfYXBpX3NlY29uZF9jbGllbnQ/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIsICJkZWZlbmNlLWFwaS1teXNxbC1ncmFiZ2lmdHMiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKG5zdGctZGVmZW5jZS1hcGktbXlzcWwtZ3JhYmdpZnRzLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL2RlZmVuY2VfYXBpP3BhcnNlVGltZT10cnVlJmxvYz1VVEMiLCAiZGVmZW5jZS1hcGktbXlzcWwtcmVwbGljYSI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1kZWZlbmNlLWFwaS1teXNxbC5ucHJ2LnN0Zy5nZGVmZW5jZS5pbzozMzA2KS9kZWZlbmNlX2FwaT9wYXJzZVRpbWU9dHJ1ZSZsb2M9VVRDIiwgImRlZmVuY2UtYXBpLW15c3FsLXJlcGxpY2Etc2Vjb25kLWNsaWVudCI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1kZWZlbmNlLWFwaS1teXNxbC5ucHJ2LnN0Zy5nZGVmZW5jZS5pbzozMzA2KS9kZWZlbmNlX2FwaV9zZWNvbmRfY2xpZW50P3BhcnNlVGltZT10cnVlJmxvYz1VVEMiLCAiZGVmZW5jZS1hcGktbXlzcWwtcmVwbGljYS1ncmFiZ2lmdHMiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKG5zdGctZGVmZW5jZS1hcGktbXlzcWwtZ3JhYmdpZnRzLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL2RlZmVuY2VfYXBpP3BhcnNlVGltZT10cnVlJmxvYz1VVEMifQ==
configSecretGiestream: eyJrYWZrYS11c2VyIjogIkxVRDRCVUdaRjZKS09ZNkIiLCJrYWZrYS1wYXNzd29yZCI6ICJXNng0bDBPeE5vNHFKUGlnRmE3aFpBMlBuRzJIUWFWRU80L2RLMk1CUlkvRmVPTm1VUjJNamVweGNxQjZhN3I4In0=

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 8081

ingress:
  enabled: false
  #  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    # hosts:
    # - host: chart-example.local
    # paths: []
    #tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: 
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
   limits:
     cpu: 1000m
     memory: 512Mi
   requests:
     cpu: 150m
     memory: 512Mi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 8
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector:
  stack: general

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    - labelSelector:
        matchExpressions:
        - key: app
          operator: In
          values:
          - defence-api
      topologyKey: "kubernetes.io/hostname"
