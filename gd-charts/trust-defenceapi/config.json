{"appConfig": {"mode": "staging", "allowedOrigins": "", "useAuth": false, "validEmailDomains": ["@grab.com", "@grabtaxi.com", "@gmail.com", "@usc.edu", "@ovo.id"], "structuredlogger": {"workerCount": 5, "bufferSize": 1000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "cgls", "development": true}, "statsd": {"host": "", "port": 8125, "appName": "defence-api"}, "mlOrchestratorClientConfig": {"endpoint": "http://mlorchestrator.ml-engineering-gd.svc.cluster.local:9000", "prefix": "/gateway/mlorchestrator", "timeoutInMs": 60000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75}, "data": {"iamData": {"mysql": {"master": {"dsn": "<vault.defence-api-iam-mysql>", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "<vault.defence-api-iam-mysql>", "maxIdle": 2, "maxOpen": 10}}}}, "dbStrategyMap": {"guardian": {"mysql": {"master": {"dsn": "<vault.defence-api-mysql>", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "<vault.defence-api-mysql-replica>", "maxIdle": 2, "maxOpen": 10}}}, "second-client": {"mysql": {"master": {"dsn": "<vault.defence-api-mysql-second-client>", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "<vault.defence-api-mysql-replica-second-client>", "maxIdle": 2, "maxOpen": 10}}}, "grab": {"mysql": {"master": {"dsn": "<vault.defence-api-mysql>", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "<vault.defence-api-mysql-replica>", "maxIdle": 2, "maxOpen": 10}}}, "grabgifts": {"mysql": {"master": {"dsn": "<vault.defence-api-mysql-grabgifts>", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "<vault.defence-api-mysql-grabgifts>", "maxIdle": 2, "maxOpen": 10}}}}, "guardianTenantInfoData": [{"defence_org_name": "guardian", "tenant_id": "guardian", "tenant_info": [{"app_id": "jxDgCPEphc", "entity_type": "ET_GRAB_DAX", "defence_account_type": "dax"}, {"app_id": "_need_to_add_", "entity_type": "ET_GUARDIAN_HYDRA", "defence_account_type": "customer"}]}, {"defence_org_name": "grab", "tenant_id": "grab", "tenant_info": [{"app_id": "jxDgCPEphc", "entity_type": "ET_GRAB_DAX", "defence_account_type": "dax"}, {"app_id": "jxDgCPEphc", "entity_type": "ET_GUARDIAN_HYDRA", "defence_account_type": "customer"}]}, {"defence_org_name": "grabgifts", "tenant_id": "grabgifts", "tenant_info": [{"app_id": "_need_to_change_", "entity_type": "PASSENGER", "defence_account_type": "PASSENGER"}, {"app_id": "_need_to_add_", "entity_type": "PARTNER", "defence_account_type": "PARTNER"}, {"app_id": "_need_to_add_", "entity_type": "SYSTEM", "defence_account_type": "SYSTEM"}]}], "king_pin_http_config": {"endpoint": "http://kingpinx.guardians.svc.cluster.local:8088", "prefix": "", "timeoutInMs": 5000, "maxConcurrentRequests": 200, "errorThresholdPercent": 50}, "magnetoxClientConfig": {"endpoint": "http://magnetox.guardians.svc.cluster.local:8088", "prefix": "", "timeoutInMs": 5000, "maxConcurrentRequests": 200, "errorThresholdPercent": 50}, "shieldxClientConfig": {"endpoint": "http://shieldx.guardians.svc.cluster.local:8088", "prefix": "", "timeoutInMs": 5000, "maxConcurrentRequests": 200, "errorThresholdPercent": 50}, "grabPlatformOAuthConfig": {"endpoint": "https://api.stg-myteksi.com", "prefix": "", "timeoutInMs": 5000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}, "poiServiceConfig": {"oauthServerEndpoint": "https://api.stg-myteksi.com/grabid/v1/oauth2/token", "clientID": "565564ff5eed470abc0d3e091cc28df1", "clientSecret": "V1ojLvV3i9av0X54", "grantType": "client_credentials", "scope": "poi.get_places", "endpoint": "https://partner-api.stg-myteksi.com", "prefix": "/maps/place/v1", "timeoutInMs": 5000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}, "callerConfigMap": {"dvd.v2": "defence-api-v2", "dvd.v4": "defence-api-v4", "dvd.v7": "defence-api-v7", "did.v7": "defence-api-v7", "did.v8": "defence-api-v8", "did.v9": "defence-api-v9"}, "tfServing": {"endpoint": "http://tfmodelserving-defence:8501", "prefix": "/v1/models", "timeoutInMs": 5000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}, "torchServeConfigs": [{"modelName": "ph-quality-check", "modelVersion": "25", "httpConfig": {"endpoint": "http://torchserve-ph-quality-check-service:8088", "prefix": "/predictions/", "timeoutInMs": 30000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}}, {"modelName": "ph-ocr", "modelVersion": "13", "httpConfig": {"endpoint": "http://torchserve-ph-ocr-service:8088", "prefix": "/predictions/", "timeoutInMs": 30000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}}, {"modelName": "th-face-match", "modelVersion": "6", "httpConfig": {"endpoint": "http://torchserve-th-face-match-service:8088", "prefix": "/predictions/", "timeoutInMs": 30000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}}, {"modelName": "th-selfie-spoofing", "modelVersion": "4", "httpConfig": {"endpoint": "http://torchserve-th-selfie-spoofing-service:8088", "prefix": "/predictions/", "timeoutInMs": 30000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}}, {"modelName": "th-id-spoofing", "modelVersion": "2", "httpConfig": {"endpoint": "http://torchserve-th-id-spoofing-service:8088", "prefix": "/predictions/", "timeoutInMs": 30000, "maxConcurrentRequests": 200, "errorThresholdPercent": 75}}], "spoReaderConfig": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-defence-api", "topic": "spostream", "appName": "defence-api", "dtoName": "ShieldxPrimaryOutput", "packageName": "streams.guardians.spostream", "offset": "newest", "workers": 2, "enableSASL": false, "enableTLS": false}, "redisClusterConfig": {"clusterNodeAddrs": ["nstg-defence-api-redis.nprv.stg.gdefence.io:6379"], "poolSize": 6, "readTimeoutInMs": 200, "writeTimeoutInMs": 200, "readOnlyFromSlaves": false}, "s3Config": {"region": "ap-southeast-1"}, "gvStreamReaderConfig": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-defence-api", "topic": "gvstream", "appName": "defence-api", "dtoName": "GuardianVerdict", "packageName": "streams.guardians.gvstream", "offset": "newest", "workers": 2, "enableSASL": false, "enableTLS": false}, "sqsConfigMap": {"grab.2RPEnyio3E": {"disable": true}, "grab.prediction_verdicts.2RPEnyio3E": {"disable": true}, "guardian.xq3kiycw80": {"awsRegion": "ap-southeast-1", "waitSec": 0, "delaySec": 0, "queueURL": "https://sqs.ap-southeast-1.amazonaws.com/128701485547/nstg-defence-api-sqs-guardian", "maxNumberOfMessageToRead": 5, "visibilityTimeoutSec": 60}, "guardian.prediction_verdicts.xq3kiycw80": {"awsRegion": "ap-southeast-1", "waitSec": 0, "delaySec": 0, "queueURL": "https://sqs.ap-southeast-1.amazonaws.com/128701485547/nstg-defence-api-sqs-verdict-guardian", "maxNumberOfMessageToRead": 5, "visibilityTimeoutSec": 60}, "grabgifts._need_to_change_": {"awsRegion": "ap-southeast-1", "waitSec": 0, "delaySec": 0, "queueURL": "https://sqs.ap-southeast-1.amazonaws.com/128701485547/nstg-defence-api-sqs-grabgifts", "maxNumberOfMessageToRead": 5, "visibilityTimeoutSec": 60}, "grabgifts.prediction_verdicts._need_to_change_": {"awsRegion": "ap-southeast-1", "waitSec": 0, "delaySec": 0, "queueURL": "https://sqs.ap-southeast-1.amazonaws.com/128701485547/nstg-defence-api-sqs-verdict-grabgifts", "maxNumberOfMessageToRead": 5, "visibilityTimeoutSec": 60}}, "griffinClientConfigs": {"guardian": {"endpoint": "http://griffin-editor-grab:9000", "engineEndpoint": "http://griffin-engine-grab.defence.svc.cluster.local:9001", "editorEndpoint": "http://griffin-editor-grab:9000", "prefix": "", "timeoutInMs": 15000, "maxIdleConnsPerHost": 50, "dialTimeoutInMs": 50, "useDNSCache": true, "dialKeepAliveInMs": 5000, "dialTLSHandshakeTimeoutInMs": 10000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75, "x-tenant-headers": {"Global": "78711709383e7595bb25cd36c5291d18b627ad82a8c426492a6ffcc17078745c"}}, "grab": {"endpoint": "http://griffin-editor-grab:9000", "engineEndpoint": "http://griffin-engine-grab.defence.svc.cluster.local:9001", "editorEndpoint": "http://griffin-editor-grab:9000", "prefix": "", "timeoutInMs": 15000, "maxIdleConnsPerHost": 50, "dialTimeoutInMs": 50, "useDNSCache": true, "dialKeepAliveInMs": 5000, "dialTLSHandshakeTimeoutInMs": 10000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75, "x-tenant-headers": {"Global": "78711709383e7595bb25cd36c5291d18b627ad82a8c426492a6ffcc17078745c"}}, "grabgifts": {"endpoint": "http://griffin-editor-grabgifts:9000", "engineEndpoint": "http://griffin-engine-grabgifts.defence.svc.cluster.local:9001", "editorEndpoint": "http://griffin-editor-grabgifts:9000", "prefix": "", "timeoutInMs": 15000, "maxIdleConnsPerHost": 50, "dialTimeoutInMs": 50, "useDNSCache": true, "dialKeepAliveInMs": 5000, "dialTLSHandshakeTimeoutInMs": 10000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75, "x-tenant-headers": {"Global": "78711709383e7595bb25cd36c5291d18b627ad82a8c426492a6ffcc17078745c"}}}, "httpclientRetryErrorPatternsMap": {"griffin-rule-engine": ["retryableErr:", " EOF$", "i/o timeout$", "read: connection reset by peer$", "http: server closed idle connection$", " no such host$"]}, "httpCBNonThreatErrPatternsMap": {"griffin-rule-engine": [" EOF$", "i/o timeout$", "read: connection reset by peer$", "http: server closed idle connection$", " no such host$"]}, "scratClientConfigs": {"_Default_": {"endpoint": "http://scrat:8081", "prefix": "/gateway/scrat", "timeoutInMs": 15000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75}, "guardian": {"endpoint": "http://scrat:8081", "prefix": "/gateway/scrat", "timeoutInMs": 15000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75}, "second-client": {"endpoint": "http://scrat:8081", "prefix": "/gateway/scrat", "timeoutInMs": 15000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75}, "grabgifts": {"endpoint": "http://scrat:8081", "prefix": "/gateway/scrat", "timeoutInMs": 15000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75}}, "archivistClientConfig": {"endpoint": "http://archivist:8081", "timeoutInMs": 15000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75}, "tdeStreamConfig": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-defenceapi", "topic": "tdestream", "appName": "archivist", "dtoName": "TisDecisionEvent", "packageName": "streams.tis.trust.tdestream", "offset": "newest", "workers": 2, "MaxInFlightPerConn": 4, "enableSASL": false, "enableTLS": false}, "gieStreamConfig": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-defence-api", "topic": "giestream", "appName": "defence-api", "dtoName": "GdInferenceEvent", "packageName": "streams.tis.trust.giestream", "offset": "newest", "workers": 2, "enableSASL": false, "enableTLS": false}, "endpointToAccessibleRolesMap": {"^/gateway/defenceapi/searchAuditLog": ["Admin$", "Lead$"], "^/gateway/defenceapi/getTenantUsersAndRoles": ["Admin$"], "^/gateway/defenceapi/updateUserRole": ["Admin$"], "^/gateway/defenceapi/toggleUserStatus": ["Admin$"]}, "featurebankClientConfig": {"endpoint": "http://featurebank.defence.svc.cluster.local:8081", "prefix": "/gateway/featurebank", "timeoutInMs": 15000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75}, "counterserviceClientConfig": {"endpoint": "http://counter-service-lite:8081", "prefix": "/gateway/counterservicelite", "timeoutInMs": 15000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75}, "defenceRoleToGriffinRoleMap": {"Admin": "admin", "Lead": "team_lead", "Member": "team_member"}, "dataClientDogConfig": {"endpoint": "https://api.datadoghq.com", "prefix": "", "timeoutInMs": 15000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75, "dd_api_key": "<vault.dd_api_key>", "dd_app_key": "<vault.dd_app_key>"}, "defenceapiInternalConfig": {"endpoint": "http://defenceapi:8081", "prefix": "/gateway/defenceapi", "timeoutInMs": 5000, "maxConcurrentRequests": 400, "errorThresholdPercent": 75}, "graphClientConfig": {"endpoint": "http://graph-service.graph-engineering-grab.svc.cluster.local:8081", "prefix": "", "timeoutInMs": 15000, "maxConcurrentRequests": 4, "errorThresholdPercent": 75}, "ldapLoginClientConfig": {"endpoint": "ldaps://ldap.jumpcloud.com:636", "baseDN": "ou=Users,o=57577a3c2cae37ea788e09e6,dc=jumpcloud,dc=com"}}, "dynamicConfig": {"spo.stream.rollout": 100, "multi_tenant.redis.rollout": true, "rollout.ucm_way_config": true, "predict_event:prefetch_data:disable_put_into_event_features": true, "predictEvent.fetchEnrichedFeatures.enableKingpin": true, "predictEvent.fetchEnrichedFeatures.enableML": true, "predictEvent.fetchEnrichedFeatures.enableDeviceCounter": true, "predictEvent.fetchEnrichedFeatures.enableSyncHydraPayload": true, "predictEvent.fetchDeviceRelatedCounterForEntities.DatasetName": "GDInternalDeviceData", "predictEvent.fetchDeviceRelatedCounterForEntities.EnableMockData": false, "predictEvent.fetchDeviceRelatedCounterForEntities.MockDataForHydraScanInfo": "[{\"has_hydra_info\": true, \"device_id\":\"undefined\"}]", "cookie.domain": "grab.com", "magnetox:getVerdictListForEntity:lookupTimeSec": 7776000, "magnetox:getVerdictListForEntity:maxRecordsCount": 100, "magnetox:getVerdictListForDevice:lookupTimeSec": 7776000, "magnetox:getVerdictListForDevice:maxRecordsCount": 100, "magnetox:getVerdictsForGuardianEventIDTask:lookupTimeSec": 600, "distributiondata.typeToFeatureNameConfigMap": {"event": ["counter_feature_daily_agg_prediction_landing"], "treatment": ["counter_feature_daily_add_treatment_prediction_1st_homepage", "counter_feature_daily_add_treatment_prediction_2nd_homepage", "counter_feature_daily_add_treatment_prediction_3rd_homepage"], "user_event": ["counter_feature_daily_agg_entity_prediction_user", "counter_feature_daily_agg_entity_2nd_prediction_user", "counter_feature_daily_agg_entity_3rd_prediction_user"], "user_treatment": ["user_treatment_feature1", "user_treatment_feature2"]}, "distributiondata.featureNameToAliasMap": {"counter_feature_daily_agg_prediction_landing": "daily_agg_prediction", "counter_feature_daily_agg_entity_prediction_user": "daily_agg_entity_prediction", "counter_feature_daily_agg_entity_2nd_prediction_user": "daily_agg_entity_2nd_prediction", "counter_feature_daily_agg_entity_3rd_prediction_user": "daily_agg_entity_3rd_prediction", "counter_feature_daily_add_treatment_prediction_1st_homepage": "daily_agg_treatment_prediction_1st", "counter_feature_daily_add_treatment_prediction_2nd_homepage": "daily_agg_treatment_prediction_2nd", "counter_feature_daily_add_treatment_prediction_3rd_homepage": "daily_agg_treatment_prediction_3rd"}, "datapointmgmt.latestinfo.table": {"guardian": "nstg-feature-bank-user-info", "second-client": "nstg-feature-bank-second-client-user-info", "grabgifts": "nstg-feature-bank-grabgifts-user-info"}, "write_entity_states_to_scrat_max_size": 100, "googleOAuthClientId": "************-5ngcj48o3kopila0hpj9fijd5ka6p418.apps.googleusercontent.com", "googleOAuthClientSecret": "yH9zwcbmBJrgm5GrYs2cn2kf", "iam.support_multiple_org": true, "spostream.retry_kingpin_client": {"roll_out_percentage": 100, "max_attempts": 5, "retry_base_delay_in_ms": 10000, "retry_max_delay_in_ms": 15000, "disabled_tenant_list": ["grab"]}, "datapoint_enriched_features.retry_config": {"max_attempts": 10, "retry_base_delay_in_ms": 2000, "retry_max_delay_in_ms": 3000}, "verdict_predict_enabled": true, "ldap.secret": "9QBjNVE7FZV4udQekeAw2KAo", "ldap.expiryInMinutes": 60, "predictEvent.noGuardianData.orgList": ["grabgifts"]}}