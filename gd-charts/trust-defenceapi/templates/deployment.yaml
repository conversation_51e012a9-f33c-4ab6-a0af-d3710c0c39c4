apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "defenceapi.fullname" . }}
  labels:
    {{- include "defenceapi.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
      {{- include "defenceapi.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "defenceapi.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "defenceapi.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: DEFENCE_API_BASE_CONF
            value: /var/config.json
          - name: DATA_CONF
            value: /go/src/gitlab.myteksi.net/trust/grab-defence-base/commons/data/config-ci.json
          - name: DEFENCE_API_VAULT_FOLDER
            value: /vault/secrets
          - name: ENABLE_PANIC_WRAP
            value: "YES"
          - name: DOGSTATSD_ADDR
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          volumeMounts:
            - name: defenceapi-conf
              mountPath: /var/config.json
              subPath: config.json
            - name: defenceapi-secret-vol
              mountPath: /vault/secrets
          ports:
            - name: http
              containerPort: 8081
              protocol: TCP
          livenessProbe:
            periodSeconds: 20
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 6
            tcpSocket:
              port: 8081
          readinessProbe:
            periodSeconds: 20
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 6
            tcpSocket:
              port: 8081
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      volumes:
        - name: defenceapi-conf
          configMap:
            name: defenceapi-conf
        - name: defenceapi-secret-vol
          secret:
            secretName: defenceapi-secret
            items:
              - key: config-secret-datadog.json
                path: config-secret-datadog.json
              - key: config-secret.json
                path: config-secret.json
              - key: config-secret-giestream.json
                path: config-secret-giestream.json
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
