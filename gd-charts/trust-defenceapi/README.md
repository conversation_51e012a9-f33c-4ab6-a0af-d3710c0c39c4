# helm

helm charts for Defence API tech stack

Helm for Service Initial Deployment:
1. Install helm with the kube config file from https://gdkube.gd.stg-myteksi.com/ (STG). For nprd and nstg, put the config
   into different local folder, for example, ~/.kube-nstg/config and ~/.kube-nprd/config

3. Pull the helm config from https://gitlab.myteksi.net/grabdefence/helm and locate to defence/stg.
   ***Make sure to update the image tag from the values.yaml***

4. Try dry run first locally to check if there're any errors:
````
helm install defenceapi --dry-run . --namespace defence --kubeconfig ~/.kube-nstg/config
````

Then perform the initial deployment:
````
helm install defenceapi . --namespace defence --kubeconfig ~/.kube-nstg/config
````

## Service Update

Helm for Service Update:

***Make sure to update the image tag from the values.yaml***

````
helm upgrade defenceapi . --namespace defence --kubeconfig ~/.kube-nstg/config
````

