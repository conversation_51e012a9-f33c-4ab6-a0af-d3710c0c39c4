{"name": "Thanosx Service", "serviceID": "thanosx", "configVersion": "v1.0.0.thanosx", "mode": "staging", "logger": {"level": 5, "tag": "thanosx", "yallMode": true, "callerEnabled": true, "callerSkip": 1}, "statsd": {"host": "localhost", "port": 8125, "appName": "thanosx"}, "grabkit": {"features": ["tracelog", "newstats", "structuredlogs"], "maxrecvmsgsize": 4194304}, "structuredlogger": {"syslogTag": "structuredlog.thanosx", "workerCount": 10, "bufferSize": 10000, "logLevel": 5, "stacktraceLevel": 4, "logFormat": "cglsdebug", "development": false}, "storageConfig": {"redisCluster": {"circuitBreakerErrorPercent": 80, "circuitBreakerMaxConcurrentRequest": 1000000, "circuitBreakerMaxQueueSize": 20, "circuitBreakerMaxVolPercentThreshold": 20, "circuitBreakerSleepWindowMS": 5000, "circuitBreakerTimeoutMs": 7000, "idleTimeoutMs": 30000, "masterHost": "nstg-thanosx-redis.nprv.stg.gdefence.io", "maxActive": 1000, "name": "thanosx-redis-cluster", "port": 6379, "readFromMasterOnly": true, "readFromSlaveOnly": false, "readTimeoutMs": 5000, "writeTimeoutMs": 5000}}, "intimateEventQueueConfig": {"queueID": "intimate.event.queue.1", "queueType": "CHANNEL", "queueName": "intimate:event:queue", "delayMS": 100, "receiverCount": 5, "channelConfig": {"jobChannelSize": 32768}}, "intimateEventProcessorConfig": {"jobChannelSize": 32768}, "heartBeatQueueConfig": {"queueID": "heart.beat.queue.1", "queueType": "CHANNEL", "queueName": "heart:beat:queue", "delayMS": 100, "receiverCount": 5, "channelConfig": {"jobChannelSize": 32768}}, "heartBeatProcessorConfig": {"jobChannelSize": 32768}, "hydraAnalysisQueueConfig": {"queueID": "hydra.analysis.queue.1", "queueType": "CHANNEL", "queueName": "hydra:analysis:queue", "delayMS": 100, "receiverCount": 5, "channelConfig": {"jobChannelSize": 32768}}, "hydraAnalysisProcessorConfig": {"jobChannelSize": 32768}, "quickHydraAnalysisQueueConfig": {"queueID": "quick.hydra.analysis.queue.1", "queueType": "CHANNEL", "queueName": "quick:hydra:analysis:queue", "delayMS": 100, "receiverCount": 5, "channelConfig": {"jobChannelSize": 32768}}, "quickHydraAnalysisProcessorConfig": {"jobChannelSize": 32768}, "externalAPIConfig": {"shieldx": {"clientConfig": {"isMocked": false, "serverAddress": "http://shieldx:8088"}, "intimateEventCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.intimateEvent", "sleepWindowMs": 5000, "timeoutMs": 60000}, "hydraAnalyseCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.hydraAnalysis", "sleepWindowMs": 5000, "timeoutMs": 60000}, "intimateHydraAbsenceCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.intimateHydraAbsence", "sleepWindowMs": 5000, "timeoutMs": 60000}, "heartbeatCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.heartbeat", "sleepWindowMs": 5000, "timeoutMs": 60000}, "applicationRegistrationListCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.applicationRegistrationList", "sleepWindowMs": 5000, "timeoutMs": 60000}, "applicationRegistrationHistoryCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.applicationRegistrationHistory", "sleepWindowMs": 5000, "timeoutMs": 60000}, "applicationRegistrationDeActivateCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.applicationRegistrationDeActivate", "sleepWindowMs": 5000, "timeoutMs": 60000}, "hydraRegistrationCommitCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.hydraRegistrationCommit", "sleepWindowMs": 5000, "timeoutMs": 60000}, "hydraRegistrationListCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.hydraRegistrationList", "sleepWindowMs": 5000, "timeoutMs": 60000}, "hydraRegistrationHistoryCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.hydraRegistrationHistory", "sleepWindowMs": 5000, "timeoutMs": 60000}, "hydraRegistrationDeActivateCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "shieldx.shieldx.hydraRegistrationDeActivate", "sleepWindowMs": 5000, "timeoutMs": 60000}}, "electrox": {"clientConfig": {"isMocked": false, "serverAddress": "http://electrox:8088"}, "quickHydraAnalyseCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "electrox.electrox.quickHydraAnalysis", "sleepWindowMs": 5000, "timeoutMs": 60000}}, "magnetox": {"clientConfig": {"isMocked": false, "serverAddress": "http://magnetox:8088"}, "getHydraPayloadCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "magnetox.magnetox.getHydraPayload", "sleepWindowMs": 5000, "timeoutMs": 60000}, "getVerdictsCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "magnetox.magnetox.getVerdicts", "sleepWindowMs": 5000, "timeoutMs": 60000}, "getCheckpointsCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "magnetox.magnetox.getCheckpoints", "sleepWindowMs": 5000, "timeoutMs": 60000}}, "redskullx": {"clientConfig": {"isMocked": false, "serverAddress": "http://redskullx:8088"}, "decryptHydraPayloadCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "redskullx.redskullx.decryptHydraPayload", "sleepWindowMs": 5000, "timeoutMs": 60000}}, "kingpinx": {"clientConfig": {"isMocked": false, "serverAddress": "http://kingpinx:8088"}, "getAssociatedDevicesCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "kingpinx.kingpinx.getAssociatedDevices", "sleepWindowMs": 5000, "timeoutMs": 60000}, "getAssociatedUsersCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "kingpinx.kingpinx.getAssociatedUsers", "sleepWindowMs": 5000, "timeoutMs": 60000}}}, "eventJWTKey": {"privateKey": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "publicKey": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQ0lqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FnOEFNSUlDQ2dLQ0FnRUF4WDNlTEFLSFV1RGZtd3hhYU5oaApGZGdTZ0d1RnVpV2tjU0trZy9TZ2R5aGlkUysxVFg1VWJGSklvd01td1BYQnF0d2JZajFQOHpWOFVrNTRRdDZYCmRvOTZKL21xWDBkd1NwOFA5d1RiNzdqWlFZNlJOb2EyNXUvejgyeTBCZmt0eklpM3JldEF4V2tXMVNWZmtrZEIKcXBBVExEelZPQkNwV3NqQnM3RzFnSjN5TjVON2RUSlpXLzM1bG5XRXFIZytCTnpiODRqeWM1dXJITEFVZDNRTwpCSmtsckxpZDZ5QmZHRVBRTTkrT1JDYXZBU3hUWHhTaCtjdVYyUlhLeWc2MDRzYUJCQzQ2eFRNeEJLU1RGU3d1CkpTQ1F2bEU3Vmg2UExCdE5YdDRudDk4NjZIRUpEODNZNFhsRjNSeGRTdFN1NFJRazQ0c0w0WTEzcHFES0g2NlIKalZycllneUtZcEZkTUFmSm9NZVR4dm5wY3NxZ1lJdm42eHY5YkNabVUzS0tCQjZNL0dIUWI3L21QTjhvTS9FRQp0Vm9tYlFTbmF5QWtZNTlhVnRkM2ljc25vdGJuZGhEUmxkcUd5OXNvQ0hFZ3ZJT3F2NjY4WHBCNzEwaVZ3cHZVClNrREJoVXkyclVKMTJtZEtUcGxZd1dwdzl0VXJxMWxRbS9YVTJUdnMwaUlaU3hsSURPaW9waTh4OXJ5ck1scUQKSndKMVlhVWhwajJKWk1CM1VxMHFCSGNlYllGR1ZIdFpVeHovUGZnQkpnR0RvckpsOHBaTElscWdhdk5EcWsvVgpJY1dXY2N2TTBDbmFsNFVuelVJd0pNUWE2bXFBYk1iMk5xTmlFMHZYZ1F2c3pmMzZyS0FKVVc2VmkrUjRCcmRKCmplRnJXYzg3cUZwOTFMamVqa25kVkRjQ0F3RUFBUT09Ci0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQo="}, "generalConfig": {"entityRolloutPercent": 100, "eventTokenTTLSec": 300, "hydraMetaDataRedisPrimaryTTLSec": 1800, "hydraMetaDataRedisBackupTTLSec": 60, "entityDistributedLockTTLSec": 10, "entityDistributedLockWaitTimeMilliSec": 100, "entityDistributedLockWaitCount": 1, "entityHydraPayloadReceivedDeleteScheduleTTLSec": 1, "enableCheckTooFreq": false, "entityHydraPayloadReceivedTTLSec": 1}, "apiIDConfig": {"2RPEnyio3E": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_DAX", "registeredEntityIDs": ["11629445", "10005640", "1", "11", "111", "1111", "111111"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"], "UET_DAX_SESSION_CONNECTED": ["HHG_ALL"], "UET_DAX_BOOKING_CONFIRMED": ["HHG_ALL"], "UET_DAX_BOOKING_ADVANCE_AWARDED": ["HHG_ALL"], "UET_DAX_BOOKING_PICKING_UP": ["HHG_ALL"], "UET_DAX_BOOKING_DROPPING_OFF": ["HHG_ALL"], "UET_DAX_BOOKING_CANCELLED_PASSENGER": ["HHG_ALL"], "UET_DAX_BOOKING_CANCELLED_DRIVER": ["HHG_ALL"], "UET_DAX_BOOKING_CANCELLED_OPERATOR": ["HHG_ALL"], "UET_DAX_BOOKING_COMPLETED": ["HHG_ALL"], "UET_DAX_BOOKING_CANDIDATE": ["HHG_ALL"], "UET_DAX_DRIVER_STATE_CHANGE": ["HHG_ALL"], "UET_DAX_SPEED_ANOMALY_DETECTED": ["HHG_ALL"]}}, "mMwAS4i5r4": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_PAX", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"], "UET_PAX_APP_START": ["HHG_ALL"]}}, "ZHJInd4MmM": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_MEX", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "gHsPCnWeG9": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_GID_SSO", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "EKtpfvG29r": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_TROY_FOOD_MAX", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "xXovuLeDkq": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_TROY_FOOD_MAX_GP", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "38DWqityxS": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_TROY_MEX_USERS", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "D2v4gF3c6r": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_TROY_UNIVERSAL", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "slKHcqwJ0O": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_KIOSK", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "y6ro1354qa": {"gatewayTenantID": "grab", "tenantID": "grab", "entityType": "ET_GRAB_FOOD_WEB_UI", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "xq3kiycw80": {"gatewayTenantID": "guardian", "tenantID": "guardian", "entityType": "ET_GUARDIAN_HYDRA", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}, "kingpinxCallerConfigID": "thanosx_guardian"}, "IFV3ivtiIs": {"gatewayTenantID": "ovo", "tenantID": "ovo", "entityType": "ET_OVO_PAY", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "MRUK8yuGAH": {"gatewayTenantID": "moca", "tenantID": "moca", "entityType": "ET_MOCA_PAY", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "hh5h3gu7ak": {"gatewayTenantID": "careem", "tenantID": "careem", "entityType": "ET_CAREEM_DAX", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "vcgmv57zwl": {"gatewayTenantID": "ninja<PERSON>", "tenantID": "ninja<PERSON>", "entityType": "ET_NV_DAX", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}, "jxDgCPEphc": {"gatewayTenantID": "client2", "tenantID": "client2", "entityType": "ET_CLIENT2_TEST", "registeredEntityIDs": ["1"], "registeredNotificationIDs": [], "registeredNotificationIDsCC": [], "registeredNotificationIDsBCC": [], "activeKeyIDs": [1], "eventHydraGroupsMap": {"UET_CUSTOM_EVENT": ["HHG_ALL"]}}}}