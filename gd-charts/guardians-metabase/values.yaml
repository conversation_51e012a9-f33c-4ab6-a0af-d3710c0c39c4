# Currently Metabase is not horizontly scalable. See
# https://github.com/metabase/metabase/issues/1446 and
# https://github.com/metabase/metabase/issues/2754
# NOTE: Should remain 1
replicaCount: 1

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "guardians-read"

podAnnotations: {}
podLabels: {}
image:
  repository: metabase/metabase
  tag: v0.44.4
  pullPolicy: IfNotPresent

## String to fully override metabase.fullname template
##
# fullnameOverride:

# Config Jetty web server
listen:
  host: "0.0.0.0"
  port: 3000
ssl:
  # If you have an ssl certificate and would prefer to have Metabase run over HTTPS
  enabled: false
  # port: 8443
  # keyStore: |-
  #   << JKS KEY STORE >>
  # keyStorePassword: storepass
jetty:
#  maxThreads: 254
#  minThreads: 8
#  maxQueued: -1
#  maxIdleTime: 60000

# Backend database
database:
  # Database type (h2 / mysql / postgres), default: h2
  type: mysql
  # k: << YOUR ENCRYPTION KEY >>
  ## Alternatively, use a connection URI for full configurability. Example for SSL enabled Postgres.
  # connectionURI: postgres://user:password@host:port/database?ssl=true&sslmode=require&sslfactory=org.postgresql.ssl.NonValidatingFactory"
  ## If a secret with the database credentials already exists, use the following values:
  # existingSecret:
  # existingSecretUsernameKey:
  # existingSecretPasswordKey:
  # existingSecretConnectionURIKey:

password:
  # Changing Metabase password complexity:
  # weak: no character constraints
  # normal: at least 1 digit (default)
  # strong: minimum 8 characters w/ 2 lowercase, 2 uppercase, 1 digit, and 1 special character
  complexity: normal
  length: 6

timeZone: UTC
emojiLogging: true
# javaOpts:
# pluginsDirectory:
# siteUrl:

session: {}
  # maxSessionAge:
  # sessionCookies:

livenessProbe:
  initialDelaySeconds: 120
  timeoutSeconds: 30
  failureThreshold: 6

readinessProbe:
  initialDelaySeconds: 30
  timeoutSeconds: 3
  periodSeconds: 5

service:
  name: metabase
  type: NodePort
  externalPort: 80
  internalPort: 3000
  # Used to fix NodePort when service.type: NodePort.
  nodePort:
  annotations: {}
    # Used to add custom annotations to the Service.
    # service.beta.kubernetes.io/aws-load-balancer-internal: "0.0.0.0/0"
ingress:
  enabled: true
  ingressClassName: internal-nginx
  # Used to create Ingress record (should used with service.type: ClusterIP).
  hosts:
    - metabase.stg.gdefence.io
    # - metabase.domain.com
  # The ingress path. Useful to host metabase on a subpath, such as `/metabase`.
  path: /
  labels:
    traffic-type: general
    # Used to add custom labels to the Ingress
    # Useful if for example you have multiple Ingress controllers and want your Ingress controllers to bind to specific Ingresses
    # traffic: internal
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  tls:
    # Secrets must be manually created in the namespace.
    # - secretName: metabase-tls
    #   hosts:
    #     - metabase.domain.com

# A custom log4j.properties file can be provided using a multiline YAML string.
# See https://github.com/metabase/metabase/blob/master/resources/log4j.properties
#
# log4jProperties:

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #  cpu: 100m
  #  memory: 128Mi
  # requests:
  #  cpu: 100m
  #  memory: 128Mi

## Node labels for pod assignment
## ref: https://kubernetes.io/docs/user-guide/node-selection/
#
nodeSelector:
  stack: general

## Tolerations for pod assignment
## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
##
tolerations: []

## Affinity for pod assignment
## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
##
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app
              operator: In
              values:
                - metabase
        topologyKey: "kubernetes.io/hostname"

configSecret:
  ZXhwb3J0IE1CX0RCX0hPU1Q9Im5zdGctbWV0YWJhc2UtbXlzcWwubnBydi5zdGcuZ2RlZmVuY2UuaW8iCiAgZXhwb3J0IE1CX0RCX0RCTkFNRT0ibWV0YWJhc2UiCiAgZXhwb3J0IE1CX0RCX1VTRVI9ImdkYWRtaW4iCiAgZXhwb3J0IE1CX0RCX1BBU1M9Ik5qUjRIeXZhR2RNRyIKICBleHBvcnQgTUJfREJfUE9SVD0zMzA2Cg==