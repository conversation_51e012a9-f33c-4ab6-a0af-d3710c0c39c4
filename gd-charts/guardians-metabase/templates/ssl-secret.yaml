{{- if .Values.ssl.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "metabase.fullname" . }}-ssl
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "metabase.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
type: Opaque
data:
  keystore: {{ .Values.ssl.keystore | b64enc | quote }}
  password: {{ .Values.ssl.keyStorePassword | b64enc | quote }}
{{- end }}
