apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "metabase.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "metabase.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  selector:
    matchLabels:
      app: {{ template "metabase.name" . }}
  replicas: {{ .Values.replicaCount }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/config.yaml") . | sha256sum }}
        {{- if .Values.podAnnotations }}
{{ toYaml .Values.podAnnotations | trim | indent 8 }}
        {{- end }}
      labels:
        app: {{ template "metabase.name" . }}
        release: {{ .Release.Name }}
        {{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | trim | indent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ include "metabase.serviceAccountName" . }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh"]
          args: ["-c", "source /vault/secrets/config && /app/run_metabase.sh"]
          env:
          - name: MB_JETTY_HOST
            value: {{ .Values.listen.host | quote }}
          - name: MB_JETTY_PORT
            value: {{ .Values.listen.port | quote }}
          {{- if .Values.ssl.enabled }}
          - name: MB_JETTY_SSL
            value: true
          - name: MB_JETTY_SSL_Port
            value: {{ .Values.ssl.port | quote }}
          - name: MB_JETTY_SSL_Keystore
            valueFrom:
              secretKeyRef:
                name: {{ template "metabase.fullname" . }}-ssl
                key: keystore
          - name: MB_JETTY_SSL_Keystore_Password
            valueFrom:
              secretKeyRef:
                name: {{ template "metabase.fullname" . }}-ssl
                key: password
          {{- end }}
          {{- if .Values.jetty }}
            {{- range $key, $value := .Values.jetty }}
          - name: MB_JETTY_{{ $key | upper }}
            value: {{ $value | quote }}
            {{- end }}
          {{- end }}
          - name: MB_DB_TYPE
            value: {{ .Values.database.type | lower }}
          {{- if .Values.database.encryptionKey }}
          - name: MB_ENCRYPTION_SECRET_KEY
            valueFrom:
              secretKeyRef:
                name: {{ template "metabase.fullname" . }}-database
                key: encryptionKey
          {{- end }}
          {{- if ne (.Values.database.type | lower) "h2" }}
            {{- if or .Values.database.existingSecretConnectionURIKey .Values.database.connectionURI }}
          - name: MB_DB_CONNECTION_URI
            valueFrom:
              secretKeyRef:
                name: {{ or .Values.database.existingSecret (printf "%s-database" (include "metabase.fullname" .)) }}
                key: {{ or .Values.database.existingSecretConnectionURIKey "connectionURI" }}
            {{- end }}
          {{- end }}
          - name: MB_PASSWORD_COMPLEXITY
            value: {{ .Values.password.complexity }}
          - name: MB_PASSWORD_LENGTH
            value: {{ .Values.password.length | quote }}
          - name: JAVA_TIMEZONE
            value: {{ .Values.timeZone }}
          {{- if .Values.javaOpts }}
          - name: JAVA_OPTS
            value: {{ .Values.javaOpts | quote }}
          {{- else }}
            {{- if .Values.log4jProperties }}
          - name: JAVA_OPTS
            value: "-Dlog4j.configuration=file:/tmp/conf/log4j.properties"
            {{- end }}
          {{- end }}
          {{- if .Values.pluginsDirectory }}
          - name: MB_PLUGINS_DIR
            value: {{ .Values.pluginsDirectory | quote }}
          {{- end }}
          - name: MB_EMOJI_IN_LOGS
            value: {{ .Values.emojiLogging | quote }}
          {{- if .Values.siteUrl }}
          - name: MB_SITE_URL
            value: {{ .Values.siteUrl | quote }}
          {{- end }}
          {{- if .Values.session.maxSessionAge }}
          - name: MAX_SESSION_AGE
            value: {{ .Values.session.maxSessionAge | quote }}
          {{- end }}
          {{- if .Values.session.sessionCookies }}
          - name: MB_SESSION_COOKIES
            value: {{ .Values.session.sessionCookies | quote }}
          {{- end }}
          ports:
            - containerPort: {{ .Values.service.internalPort }}
          livenessProbe:
            httpGet:
              path: /
              port: {{ .Values.service.internalPort }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          readinessProbe:
            httpGet:
              path: /
              port: {{ .Values.service.internalPort }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
          volumeMounts:
            - name: metabase-secret-vol
              mountPath: /vault/secrets
          {{- if .Values.log4jProperties }}
            - name: config
              mountPath: /tmp/conf/
          {{- end}}
          resources:
{{ toYaml .Values.resources | indent 12 }}
    {{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
      volumes:
        - name: metabase-secret-vol
          secret:
            secretName: metabase-secret
            items:
              - key: config
                path: config
        {{- if .Values.log4jProperties}}
        - name: config
          configMap:
            name: {{ template "metabase.fullname" . }}-config
            items:
            - key: log4j.properties
              path: log4j.properties
        {{- end }}
