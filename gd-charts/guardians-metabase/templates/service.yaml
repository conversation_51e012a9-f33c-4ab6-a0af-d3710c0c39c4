apiVersion: v1
kind: Service
metadata:
  name: {{ template "metabase.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "metabase.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
{{- if .Values.service.annotations }}
  annotations:
{{ toYaml .Values.service.annotations | indent 4 }}
{{- end }}
spec:
  type: {{ .Values.service.type }}
{{- if .Values.service.loadBalancerSourceRanges}}
  loadBalancerSourceRanges:
{{toYaml .Values.service.loadBalancerSourceRanges | indent 4 }}
{{- end}}
  ports:
    - port: {{ .Values.service.externalPort }}
      targetPort: {{ .Values.service.internalPort }}
{{- if .Values.service.nodePort }}
      nodePort: {{ .Values.service.nodePort }}
{{- end}}
      protocol: TCP
      name: {{ .Values.service.name }}
  selector:
    app: {{ template "metabase.name" . }}
    release: {{ .Release.Name }}
