{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "metabase.serviceAccountName" . }}
  labels:
    app: { { template "metabase.name" . } }
    chart: { { .Chart.Name } }-{{ .Chart.Version | replace "+" "_" }}
    release: { { .Release.Name } }
    heritage: { { .Release.Service } }
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
