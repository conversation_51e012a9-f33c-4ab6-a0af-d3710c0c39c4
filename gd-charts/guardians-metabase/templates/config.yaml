apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "metabase.fullname" . }}-config
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "metabase.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  {{- if .Values.log4jProperties }}
  log4j.properties:
{{ toYaml .Values.log4jProperties | indent 4}}
  {{- end}}
