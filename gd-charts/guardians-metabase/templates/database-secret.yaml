{{- if and (ne (.Values.database.type | lower) "h2") (not .Values.database.existingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "metabase.fullname" . }}-database
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "metabase.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
type: Opaque
data:
  {{- if .Values.database.encryptionKey }}
  encryptionKey: {{ .Values.database.encryptionKey | b64enc | quote }}
  {{- end }}
  {{- if .Values.database.connectionURI }}
  connectionURI: {{ .Values.database.connectionURI | b64enc | quote }}
  {{- end }}
{{- end }}
