{"name": "Shieldx Service", "serviceID": "shieldx", "configVersion": "v1.0.0.shieldx", "mode": "staging", "logger": {"level": 5, "tag": "shieldx", "yallMode": true, "callerEnabled": true, "callerSkip": 1}, "statsd": {"host": "localhost", "port": 8125, "appName": "shieldx"}, "grabkit": {"features": ["tracelog", "newstats", "structuredlogs"], "maxrecvmsgsize": 4194304}, "structuredlogger": {"syslogTag": "structuredlog.shieldx", "workerCount": 10, "bufferSize": 10000, "logLevel": 5, "stacktraceLevel": 4, "logFormat": "cglsdebug", "development": false}, "storageConfig": {"redisCluster": {"circuitBreakerErrorPercent": 80, "circuitBreakerMaxConcurrentRequest": 1000000, "circuitBreakerMaxQueueSize": 20, "circuitBreakerMaxVolPercentThreshold": 20, "circuitBreakerSleepWindowMS": 5000, "circuitBreakerTimeoutMs": 7000, "idleTimeoutMs": 30000, "masterHost": "nstg-shieldx-redis.nprv.stg.gdefence.io", "maxActive": 1000, "name": "shieldx-redis-cluster", "port": 6379, "readFromMasterOnly": true, "readFromSlaveOnly": false, "readTimeoutMs": 5000, "writeTimeoutMs": 5000}, "mysqlCircuitBreaker": {"readFromSlave": false, "timeoutMs": 10000, "errorPercent": 50, "maxConcurrentRequest": 90000, "maxVolPercentThreshold": 20, "sleepWindowMs": 5000, "maxQueueSize": 5000}, "mysql": {"default": {"mysql": {"master": {"dsn": "vault.default.master", "maxIdle": 20, "maxOpen": 300}, "slave": {"dsn": "vault.default.replica", "maxIdle": 20, "maxOpen": 300}}}}}, "streamWriter": {"SPOStream": {"writerEnabled": true, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "writerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "shieldx-spostream-producer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "stream": "spostream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "shieldx", "packageName": "streams.guardians.spostream", "dtoName": "ShieldxPrimaryOutput"}}, "SPVStream": {"writerEnabled": true, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "writerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "shieldx-spvstream-producer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "shortName": "spvstream", "stream": "spvstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "shieldx", "packageName": "streams.guardians.spvstream", "dtoName": "ShieldxPrimaryVerdict"}}, "NHPStream": {"writerEnabled": true, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "writerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "shieldx-nhpstream-producer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "shortName": "nhpstream", "stream": "nhpstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "shieldx", "packageName": "streams.guardians.nhpstream", "dtoName": "NoHydraPayload"}}, "SCStream": {"writerEnabled": true, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "writerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "shieldx-scstream-producer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "shortName": "scstream", "stream": "scstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "shieldx", "packageName": "streams.guardians.scstream", "dtoName": "ShieldxCheckpoint"}}}, "externalAPIConfig": {"thanosx": {"clientConfig": {"isMocked": false, "serverAddress": "http://thanosx:8088"}, "sendApplicationRegistrationNotificationCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "thanosx.thanosx.sendApplicationRegistrationNotification", "sleepWindowMs": 5000, "timeoutMs": 60000}}, "redskullx": {"clientConfig": {"isMocked": false, "serverAddress": "http://redskullx:8088"}, "decryptHydraPayloadCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "redskullx.redskullx.decryptHydraPayload", "sleepWindowMs": 5000, "timeoutMs": 60000}}, "patronusx": {"clientConfig": {"isMocked": false, "serverAddress": "http://patronusx:8088"}, "deviceInformationCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "patronusx.patronusx.deviceInformation", "sleepWindowMs": 5000, "timeoutMs": 60000}}, "venomx": {"clientConfig": {"isMocked": false, "serverAddress": "http://venomx:8088"}, "validateLocationCircuitConfig": {"errorPercent": 50, "maxConcurrentRequest": 120000, "maxQueueSize": 10000, "maxVolPercentThreshold": 20, "name": "venomx.venomx.validateLocation", "sleepWindowMs": 5000, "timeoutMs": 60000}}}, "hydraPrimaryQueueConfig": {"queueID": "async:hydra.queue.1", "queueType": "SQS", "queueName": "async:hydra:queue:primary", "delayMS": 100, "receiverCount": 5, "sqsConfig": {"awsRegion": "ap-southeast-1", "delay": 0, "maxNumberOfMessageToRead": 1, "queueURL": "https://sqs.ap-southeast-1.amazonaws.com/128701485547/nstg-shieldx-sqs-hydra", "visibility_timeout_sec": 60}}, "hydraPrimaryProcessorConfig": {"jobChannelSize": 32768}, "hydraSecondaryQueueConfig": {"queueID": "async:hydra.queue.2", "queueType": "REDIS", "queueName": "async:hydra:queue:secondary", "delayMS": 1000, "receiverCount": 5, "redisConfig": {"host": "nstg-shieldx-redis-queue.nprv.stg.gdefence.io", "port": 6379, "db": 0, "maxConn": 100, "maxIdle": 10, "connectTimeoutMS": 2000, "readTimeoutMS": 2000, "writeTimeoutMS": 2000, "circuitBreakerTimeoutMs": 3000, "circuitBreakerErrorPercent": 80, "circuitBreakerMaxConcurrentRequest": 1000000, "circuitBreakerMaxVolPercentThreshold": 20, "circuitBreakerSleepWindowMS": 5000, "circuitBreakerMaxQueueSize": 20}}, "hydraSecondaryProcessorConfig": {"jobChannelSize": 32768}, "hydraTertiaryQueueConfig": {"queueID": "async:hydra.queue.3", "queueType": "CHANNEL", "queueName": "async:hydra:queue:tertiary", "delayMS": 100, "receiverCount": 5, "channelConfig": {"jobChannelSize": 32768}}, "hydraTertiaryProcessorConfig": {"jobChannelSize": 32768}, "intimateEventQueueConfig": {"queueID": "intimate.event.queue.1", "queueType": "CHANNEL", "queueName": "intimate:event:queue", "delayMS": 100, "receiverCount": 5, "channelConfig": {"jobChannelSize": 32768}}, "intimateEventProcessorConfig": {"jobChannelSize": 32768}, "nhpTrackerQueueConfig": {"queueID": "nhp.tracker.queue.1", "queueType": "SQS", "queueName": "nhp:tracker:queue:primary", "delayMS": 100, "receiverCount": 5, "sqsConfig": {"awsRegion": "ap-southeast-1", "delay": 300, "maxNumberOfMessageToRead": 1, "queueURL": "https://sqs.ap-southeast-1.amazonaws.com/128701485547/nstg-shieldx-sqs-nhp", "visibility_timeout_sec": 60}}, "nhpTrackerProcessorConfig": {"jobChannelSize": 32768}, "featureFlag": {"patronusxGetDeviceInformationEnabled": true, "fakeGPSAnalysisEnabled": false}, "featureEnabledEntity": {"fakeGPSAnalysisEntitiesRollOut": {"ET_GRAB_DAX": 0, "ET_GUARDIAN_HYDRA": 0}}, "generalConfig": {"hydraPayloadPrimaryQueueMaxSizeKb": 50, "processHydraPayloadEventIDDeDuplicatorRedisTTLSec": 1800, "hydraPayloadReceivedRedisTTLSec": 1800, "queueDeDuplicationRedisTTLSec": 1800, "hydraAbsenceReceivedRedisTTLSec": 1800, "hydraPayloadCounterLockTTLSec": 5, "hydraPayloadCounterLockWaitMilliSec": 1000, "hydraPayloadCounterLockRetryCount": 10, "hydraPayloadCounterTTLSec": 9000, "hydraPayloadCounterMinIntimateCount": 3, "hydraPayloadCounterMinimumHydraPercent": 0, "preferredDeviceIDVersion": "did.v7", "registrationApplicationLockTTLSec": 3, "registrationApplicationLockWaitMilliSec": 1000, "registrationApplicationLockRetryCount": 3, "applicationRegistrationRedisTTLSec": 2}, "preProcessConfig": {"pre_process_enabled": true, "app_registration_check_enabled": true}, "heartBeatProcessConfig": {"heartbeat_process_enabled": true, "app_registration_check_enabled": true, "hydra_registration_check_enabled": true, "event_token_signature_mismatch_check_enabled": true, "intimate_heartbeat_mismatch_check_enabled": true}, "postProcessConfig": {"common": {"enabled": true, "heartbeat_hydra_mismatch_check_enabled": true}, "hydra": {"enabled": true, "common": {"enabled": true, "perform_elapsed_time_too_high_check": true, "perform_elapsed_time_too_high_value_second": 240, "perform_evidence_integrity_check": true, "perform_evidence_integrity_check_allowed_field_name_string": ["ApplicationPackageHash"], "perform_platform_impostor_check": true, "perform_server_challenge_mismatch_check": true, "payload_integrity_check": true, "payload_integrity_check_exception_list": ["CommandBusyBox", "ParallelApps", "CommandNetstatGrep", "CommandLs", "FileExistsOTACertificate"], "perform_event_token_signature_mismatch_check": true}, "android": {"enabled": true, "application_package_hash_check": true, "art_symbol_scan_check_xposed_markers_lookup_string_list": ["xposed", "system"], "blacklisted_app_checks": true, "command_root_detection_check_non_root_markers_lookup_string_list": [], "command_root_detection_check_root_markers_lookup_string_list": ["uid=0", "magisk", "timer expired", "timed out", "unallowed", "permission", "not allowed"], "emulator_detection_check": true, "emulator_detection_check_by_device_info_by_brand_string_list": ["Android", "samsung", "OnePlus", "oppo", "vivo", "google", "Galaxy"], "emulator_detection_check_by_device_info_by_fingerprint_string_list": ["android_x86", "emulator", "google-sdk_gphone"], "emulator_detection_check_by_wx_codesegment_binary_lookup_string_list": ["libhellfire"], "emulator_detection_check_by_wx_codesegment_brand_exception_string_list": ["asus"], "emulator_detection_check_must_have_binary_abi": ["x86"], "file_exists_root_detection_check_root_markers_lookup_string_list": ["su"], "emulator_detection_check_by_process_info_lookup_string_list": ["VMOS"], "emulator_detection_check_by_odex_lookup_string_list": ["x86_64"], "emulator_detection_check_by_odex_model_exception_string_list": [], "emulator_detection_check_by_installer_package_name_lookup_string_list": ["com.bluestacks.BstCommandProcessor"], "location_mocker_checks": true, "location_mocker_checks_by_app": true, "location_mocker_checks_by_app_string_map": {".fakeloc": false, "GPS_Faker.dsm_029.pokemonicstate": false, "aldytoi": false, "ameerhamza6733.snapmapfakeloction": false, "android.z": true, "anggoro.dev": false, "antutu.melek": false, "app.coffee.ovo.id": true, "appstonix.taxigps": false, "au.com.ovo.android": true, "bhoiwala.locationmockerpro": false, "blogspot.newapphorizons.fakegps": false, "blogspot.newapphorizons.fakegpt": false, "blogspot.newapphorizons.fakegpu": false, "br.com.itexpertconsult.changemygps": false, "brandonnalls.mockmocklocations": false, "chortorlab.fakeloc": false, "com.gpscontroller": false, "com.grab.activegpsdonate": true, "com.grab.chat.test": true, "com.grab.driver": true, "com.grab.food.dat": true, "com.grab.food.dav": true, "com.grab.food.day": true, "com.grab.food.pao": true, "com.grab.gacordriverbanjir": true, "com.grab.gps": true, "com.grab.gps1": true, "com.grab.gps2": true, "com.grab.grabrider": true, "com.grab.hydra.hydra_analytics": true, "com.grab.indoordemo": true, "com.grab.jancok": true, "com.grab.jembut": true, "com.grab.love": true, "com.grab.module": true, "com.grab.waterfall": true, "com.grab1.android.rootPermissions": true, "com.grab1.android.rootpermissions": true, "com.grab56bike29promo.pesan8ojek3online1": true, "com.grab8bike8diskon.tarif4diskongrabbike": true, "com.grabah.collecttayo": true, "com.grabah.sandkinetic": true, "com.grabb.id": true, "com.grabbike.passenger": true, "com.grabbike78promo56terbaru45.order78ojek90online": true, "com.grabbikeguide.panduanindobaru": true, "com.grabbikemotor.guideojekonline": true, "com.grabbinggames.men.tshirt.photomaker": true, "com.grabbinggames.menpolicedress.photosuit": true, "com.grabbinggames.mensweatshirt.photomontage": true, "com.grabcar69promo.grabguidestarifordergrabikegrabcartaxi": true, "com.grabcarmobil.ojekonlineindonesiadownload": true, "com.grabcarpromo.diskontaxionlineindonesia": true, "com.grabcars.androidapp": true, "com.grabdriver.bike": true, "com.grabersby": true, "com.grabgas": true, "com.grabguide.carapesangrab": true, "com.grabguide.carapesangrabfulanapp": true, "com.grabhotel": true, "com.grabkonsumencarbike": true, "com.grablocation": true, "com.grabmenu.merchant": true, "com.grabmobilT2grab.Location": true, "com.grabmobilguide.ordertaksionlineindonesiapromo": true, "com.grabmobilt2grab.location": true, "com.grabone.goapprn": true, "com.grabpromocode.coupon.asian": true, "com.grabrewards.lockerpoints": true, "com.grabsejahtera": true, "com.grabt3domesgrab.Location2": true, "com.grabt3domesgrab.location2": true, "com.grabt3intergrab.Location2": true, "com.grabt3intergrab.location2": true, "com.grabtaxi.driver": true, "com.grabtaxi.driver.dodol": true, "com.grabtaxi.driver21": true, "com.grabtaxi.driver22": true, "com.grabtaxi.driver3": true, "com.grabtaxi.driver8": true, "com.grabtaxi.driverC": true, "com.grabtaxi.driverc": true, "com.grabtaxi.drivet2": true, "com.grabtaxi.passengab": true, "com.grabtaxi.passengac": true, "com.grabtaxi.passengad": true, "com.grabtaxi.passengae": true, "com.grabtaxi.passengaf": true, "com.grabtaxi.passengag": true, "com.grabtaxi.passengah": true, "com.grabtaxi.passengai": true, "com.grabtaxi.passengaj": true, "com.grabtaxi.passengar": true, "com.grabtaxi.passengas": true, "com.grabtaxi.passengba": true, "com.grabtaxi.passengbb": true, "com.grabtaxi.passengbc": true, "com.grabtaxi.passengbd": true, "com.grabtaxi.passengbe": true, "com.grabtaxi.passengbf": true, "com.grabtaxi.passengbg": true, "com.grabtaxi.passengbh": true, "com.grabtaxi.passengbi": true, "com.grabtaxi.passengbj": true, "com.grabtaxi.passengbk": true, "com.grabtaxi.passengbl": true, "com.grabtaxi.passengbm": true, "com.grabtaxi.passengbn": true, "com.grabtaxi.passengbo": true, "com.grabtaxi.passengbp": true, "com.grabtaxi.passengbq": true, "com.grabtaxi.passengbr": true, "com.grabtaxi.passengbs": true, "com.grabtaxi.passengbt": true, "com.grabtaxi.passengbu": true, "com.grabtaxi.passengbv": true, "com.grabtaxi.passengbw": true, "com.grabtaxi.passengbx": true, "com.grabtaxi.passengby": true, "com.grabtaxi.passengbz": true, "com.grabtaxi.passengca": true, "com.grabtaxi.passengcb": true, "com.grabtaxi.passengcc": true, "com.grabtaxi.passengcd": true, "com.grabtaxi.passengce": true, "com.grabtaxi.passengcf": true, "com.grabtaxi.passengcg": true, "com.grabtaxi.passengch": true, "com.grabtaxi.passengci": true, "com.grabtaxi.passengcj": true, "com.grabtaxi.passengck": true, "com.grabtaxi.passengcl": true, "com.grabtaxi.passengcm": true, "com.grabtaxi.passengcn": true, "com.grabtaxi.passengco": true, "com.grabtaxi.passengcp": true, "com.grabtaxi.passengcq": true, "com.grabtaxi.passengcr": true, "com.grabtaxi.passengcs": true, "com.grabtaxi.passengct": true, "com.grabtaxi.passengcu": true, "com.grabtaxi.passengcv": true, "com.grabtaxi.passengcw": true, "com.grabtaxi.passengcx": true, "com.grabtaxi.passengcy": true, "com.grabtaxi.passengcz": true, "com.grabtaxi.passengda": true, "com.grabtaxi.passengdb": true, "com.grabtaxi.passengdc": true, "com.grabtaxi.passengdd": true, "com.grabtaxi.passengde": true, "com.grabtaxi.passengdf": true, "com.grabtaxi.passengdg": true, "com.grabtaxi.passengdh": true, "com.grabtaxi.passengdi": true, "com.grabtaxi.passengdj": true, "com.grabtaxi.passengdk": true, "com.grabtaxi.passengdl": true, "com.grabtaxi.passengdm": true, "com.grabtaxi.passengdn": true, "com.grabtaxi.passengdo": true, "com.grabtaxi.passengdp": true, "com.grabtaxi.passengdq": true, "com.grabtaxi.passengdr": true, "com.grabtaxi.passengds": true, "com.grabtaxi.passengdt": true, "com.grabtaxi.passengdu": true, "com.grabtaxi.passengdv": true, "com.grabtaxi.passengdw": true, "com.grabtaxi.passengdx": true, "com.grabtaxi.passengdy": true, "com.grabtaxi.passengdz": true, "com.grabtaxi.passengea": true, "com.grabtaxi.passengeb": true, "com.grabtaxi.passengec": true, "com.grabtaxi.passenged": true, "com.grabtaxi.passengee": true, "com.grabtaxi.passengef": true, "com.grabtaxi.passengeg": true, "com.grabtaxi.passengeh": true, "com.grabtaxi.passengei": true, "com.grabtaxi.passengej": true, "com.grabtaxi.passengek": true, "com.grabtaxi.passengel": true, "com.grabtaxi.passengem": true, "com.grabtaxi.passengen": true, "com.grabtaxi.passengeo": true, "com.grabtaxi.passengep": true, "com.grabtaxi.passengeq": true, "com.grabtaxi.passenger.lanrun1": true, "com.grabtaxi.passenger.lanrun2": true, "com.grabtaxi.passenges": true, "com.grabtaxi.passenget": true, "com.grabtaxi.passengeu": true, "com.grabtaxi.passengev": true, "com.grabtaxi.passengew": true, "com.grabtaxi.passengex": true, "com.grabtaxi.passengey": true, "com.grabtaxi.passengez": true, "com.grabtaxi.passengfa": true, "com.grabtaxi.passengfb": true, "com.grabtaxi.passengfc": true, "com.grabtaxi.passengfd": true, "com.grabtaxi.passengfe": true, "com.grabtaxi.passengff": true, "com.grabtaxi.passengfg": true, "com.grabtaxi.passengfh": true, "com.grabtaxi.passengfi": true, "com.grabtaxi.passengfj": true, "com.grabtaxi.passengfk": true, "com.grabtaxi.passengfl": true, "com.grabtaxi.passengfm": true, "com.grabtaxi.passengfn": true, "com.grabtaxi.passengfo": true, "com.grabtaxi.passengfp": true, "com.grabtaxi.passengfq": true, "com.grabtaxi.passengfr": true, "com.grabtaxi.passengfs": true, "com.grabtaxi.passengft": true, "com.grabtaxi.passengfu": true, "com.grabtaxi.passengfv": true, "com.grabtaxi.passengfw": true, "com.grabtaxi.passengfx": true, "com.grabtaxi.passengfy": true, "com.grabtaxi.passengfz": true, "com.grabtaxi.passengga": true, "com.grabtaxi.passenggb": true, "com.grabtaxi.passenggc": true, "com.grabtaxi.passenggd": true, "com.grabtaxi.passengge": true, "com.grabtaxi.passenggf": true, "com.grabtaxi.passenggg": true, "com.grabtaxi.passenggh": true, "com.grabtaxi.passenggi": true, "com.grabtaxi.passenggj": true, "com.grabtaxi.passenggk": true, "com.grabtaxi.passenggl": true, "com.grabtaxi.passenggm": true, "com.grabtaxi.passenggn": true, "com.grabtaxi.passenggo": true, "com.grabtaxi.passenggp": true, "com.grabtaxi.passenggq": true, "com.grabtaxi.passenggr": true, "com.grabtaxi.passenggs": true, "com.grabtaxi.passenggt": true, "com.grabtaxi.passenggu": true, "com.grabtaxi.passenggv": true, "com.grabtaxi.passenggw": true, "com.grabtaxi.passenggx": true, "com.grabtaxi.passenggy": true, "com.grabtaxi.passenggz": true, "com.grabtaxi.passengha": true, "com.grabtaxi.passenghb": true, "com.grabtaxi.passenghc": true, "com.grabtaxi.passenghd": true, "com.grabtaxi.passenghe": true, "com.grabtaxi.passenghf": true, "com.grabtaxi.passenghg": true, "com.grabtaxi.passenghh": true, "com.grabtaxi.passenghi": true, "com.grabtaxi.passenghj": true, "com.grabtaxi.passenghk": true, "com.grabtaxi.passenghl": true, "com.grabtaxi.passenghm": true, "com.grabtaxi.passenghn": true, "com.grabtaxi.passengho": true, "com.grabtaxi.passenghp": true, "com.grabtaxi.passenghq": true, "com.grabtaxi.passenghr": true, "com.grabtaxi.passenghs": true, "com.grabtaxi.passenght": true, "com.grabtaxi.passenghu": true, "com.grabtaxi.passenghv": true, "com.grabtaxi.passenghw": true, "com.grabtaxi.passenghx": true, "com.grabtaxi.passenghy": true, "com.grabtaxi.passenghz": true, "com.grabtaxi.passengia": true, "com.grabtaxi.passengib": true, "com.grabtaxi.passengic": true, "com.grabtaxi.passengid": true, "com.grabtaxi.passengie": true, "com.grabtaxi.passengif": true, "com.grabtaxi.passengig": true, "com.grabtaxi.passengih": true, "com.grabtaxi.passengii": true, "com.grabtaxi.passengij": true, "com.grabtaxi.passengik": true, "com.grabtaxi.passengil": true, "com.grabtaxi.passengim": true, "com.grabtaxi.passengin": true, "com.grabtaxi.passengio": true, "com.grabtaxi.passengip": true, "com.grabtaxi.passengiq": true, "com.grabtaxi.passengir": true, "com.grabtaxi.passengis": true, "com.grabtaxi.passengit": true, "com.grabtaxi.passengiu": true, "com.grabtaxi.passengiv": true, "com.grabtaxi.passengiw": true, "com.grabtaxi.passengix": true, "com.grabtaxi.passengiy": true, "com.grabtaxi.passengiz": true, "com.grabtaxi.passengja": true, "com.grabtaxi.passengjb": true, "com.grabtaxi.passengjc": true, "com.grabtaxi.passengjd": true, "com.grabtaxi.passengje": true, "com.grabtaxi.passengjf": true, "com.grabtaxi.passengjg": true, "com.grabtaxi.passengjh": true, "com.grabtaxi.passengji": true, "com.grabtaxi.passengjj": true, "com.grabtaxi.passengjk": true, "com.grabtaxi.passengjl": true, "com.grabtaxi.passengjm": true, "com.grabtaxi.passengjn": true, "com.grabtaxi.passengjo": true, "com.grabtaxi.passengjp": true, "com.grabtaxi.passengjq": true, "com.grabtaxi.passengjr": true, "com.grabtaxi.passengjs": true, "com.grabtaxi.passengjt": true, "com.grabtaxi.passengju": true, "com.grabtaxi.passengjv": true, "com.grabtaxi.passengjw": true, "com.grabtaxi.passengjx": true, "com.grabtaxi.passengjy": true, "com.grabtaxi.passengjz": true, "com.grabtaxi.passengka": true, "com.grabtaxi.passengkb": true, "com.grabtaxi.passengkc": true, "com.grabtaxi.passengkd": true, "com.grabtaxi.passengke": true, "com.grabtaxi.passengkf": true, "com.grabtaxi.passengkg": true, "com.grabtaxi.passengkh": true, "com.grabtaxi.passengki": true, "com.grabtaxi.passengkj": true, "com.grabtaxi.passengkk": true, "com.grabtaxi.passengkl": true, "com.grabtaxi.passengkm": true, "com.grabtaxi.passengkn": true, "com.grabtaxi.passengko": true, "com.grabtaxi.passengkp": true, "com.grabtaxi.passengkq": true, "com.grabtaxi.passengkr": true, "com.grabtaxi.passengks": true, "com.grabtaxi.passengkt": true, "com.grabtaxi.passengku": true, "com.grabtaxi.passengkv": true, "com.grabtaxi.passengkw": true, "com.grabtaxi.passengkx": true, "com.grabtaxi.passengky": true, "com.grabtaxi.passengkz": true, "com.grabtaxi.passengla": true, "com.grabtaxi.passenglb": true, "com.grabtaxi.passenglc": true, "com.grabtaxi.passengld": true, "com.grabtaxi.passengle": true, "com.grabtaxi.passenglf": true, "com.grabtaxi.passenglg": true, "com.grabtaxi.passenglh": true, "com.grabtaxi.passengli": true, "com.grabtaxi.passenglj": true, "com.grabtaxi.passenglk": true, "com.grabtaxi.passengll": true, "com.grabtaxi.passenglm": true, "com.grabtaxi.passengln": true, "com.grabtaxi.passenglo": true, "com.grabtaxi.passenglp": true, "com.grabtaxi.passenglq": true, "com.grabtaxi.passenglr": true, "com.grabtaxi.passengls": true, "com.grabtaxi.passenglt": true, "com.grabtaxi.passenglu": true, "com.grabtaxi.passenglv": true, "com.grabtaxi.passenglw": true, "com.grabtaxi.passenglx": true, "com.grabtaxi.passengly": true, "com.grabtaxi.passenglz": true, "com.grabtaxi.passengma": true, "com.grabtaxi.passengmb": true, "com.grabtaxi.passengmc": true, "com.grabtaxi.passengmd": true, "com.grabtaxi.passengme": true, "com.grabtaxi.passengmf": true, "com.grabtaxi.passengmg": true, "com.grabtaxi.passengmh": true, "com.grabtaxi.passengmi": true, "com.grabtaxi.passengmj": true, "com.grabtaxi.passengmk": true, "com.grabtaxi.passengml": true, "com.grabtaxi.passengmm": true, "com.grabtaxi.passengmn": true, "com.grabtaxi.passengmo": true, "com.grabtaxi.passengmp": true, "com.grabtaxi.passengmq": true, "com.grabtaxi.passengmr": true, "com.grabtaxi.passengms": true, "com.grabtaxi.passengmt": true, "com.grabtaxi.passengmu": true, "com.grabtaxi.passengmv": true, "com.grabtaxi.passengmw": true, "com.grabtaxi.passengmx": true, "com.grabtaxi.passengmy": true, "com.grabtaxi.passengmz": true, "com.grabtaxi.passengnf": true, "com.grabtaxi.passengnj": true, "com.grabtaxi.passengnk": true, "com.grabtaxi.passengnl": true, "com.grabtaxi.passengnm": true, "com.grabtaxi.passengnn": true, "com.grabtaxi.passengno": true, "com.grabtaxi.passengnp": true, "com.grabtaxi.passengnq": true, "com.grabtaxi.passengnr": true, "com.grabtaxi.passengns": true, "com.grabtaxi.passengnt": true, "com.grabtaxi.passengnu": true, "com.grabtaxi.passengnv": true, "com.grabtaxi.passengnw": true, "com.grabtaxi.passengnx": true, "com.grabtaxi.passengny": true, "com.grabtaxi.passengnz": true, "com.grabtaxi.passengob": true, "com.grabtaxi.passengoc": true, "com.grabtaxi.passengof": true, "com.grabtaxi.passengoh": true, "com.grabtaxi.passengoi": true, "com.grabtaxi.passengop": true, "com.grabtaxi.passengor": true, "com.grabtaxi.passengoy": true, "com.grabtaxi.passengpa": true, "com.grabtaxi.passengpb": true, "com.grabtaxi.passengpc": true, "com.grabtaxi.passengpd": true, "com.grabtaxi.passengpe": true, "com.grabtaxi.passengpf": true, "com.grabtaxi.passengpg": true, "com.grabtaxi.passengph": true, "com.grabtaxi.passengpi": true, "com.grabtaxi.passengpj": true, "com.grabtaxi.passengpk": true, "com.grabtaxi.passengpl": true, "com.grabtaxi.passengpn": true, "com.grabtaxi.passengpr": true, "com.grabtaxi.passengpt": true, "com.grabtaxi.passengpu": true, "com.grabtaxi.passengpv": true, "com.grabtaxi.passengpx": true, "com.grabtaxi.passengpz": true, "com.grabtaxi.passengqa": true, "com.grabtaxi.passengqb": true, "com.grabtaxi.passengqc": true, "com.grabtaxi.passengqd": true, "com.grabtaxi.passengqe": true, "com.grabtaxi.passengqg": true, "com.grabtaxi.passengqh": true, "com.grabtaxi.passengqi": true, "com.grabtaxi.passengqj": true, "com.grabtaxi.passengqk": true, "com.grabtaxi.passengql": true, "com.grabtaxi.passengqm": true, "com.grabtaxi.passengqn": true, "com.grabtaxi.passengqo": true, "com.grabtaxi.passengqp": true, "com.grabtaxi.passengqq": true, "com.grabtaxi.passengqr": true, "com.grabtaxi.passengqs": true, "com.grabtaxi.passengqt": true, "com.grabtaxi.passengqu": true, "com.grabtaxi.passengqv": true, "com.grabtaxi.passengqw": true, "com.grabtaxi.passengqx": true, "com.grabtaxi.passengqy": true, "com.grabtaxi.passengqz": true, "com.grabtaxi.passengra": true, "com.grabtaxi.passengrb": true, "com.grabtaxi.passengrc": true, "com.grabtaxi.passengrd": true, "com.grabtaxi.passengre": true, "com.grabtaxi.passengrf": true, "com.grabtaxi.passengrg": true, "com.grabtaxi.passengrh": true, "com.grabtaxi.passengri": true, "com.grabtaxi.passengrj": true, "com.grabtaxi.passengrk": true, "com.grabtaxi.passengrl": true, "com.grabtaxi.passengrm": true, "com.grabtaxi.passengrn": true, "com.grabtaxi.passengro": true, "com.grabtaxi.passengrp": true, "com.grabtaxi.passengrq": true, "com.grabtaxi.passengrr": true, "com.grabtaxi.passengrs": true, "com.grabtaxi.passengrt": true, "com.grabtaxi.passengru": true, "com.grabtaxi.passengrv": true, "com.grabtaxi.passengrw": true, "com.grabtaxi.passengrx": true, "com.grabtaxi.passengry": true, "com.grabtaxi.passengrz": true, "com.grabtaxi.passengsa": true, "com.grabtaxi.passengsb": true, "com.grabtaxi.passengsc": true, "com.grabtaxi.passengsd": true, "com.grabtaxi.passengse": true, "com.grabtaxi.passengsf": true, "com.grabtaxi.passengsg": true, "com.grabtaxi.passengsh": true, "com.grabtaxi.passengsi": true, "com.grabtaxi.passengsj": true, "com.grabtaxi.passengsk": true, "com.grabtaxi.passengsl": true, "com.grabtaxi.passengsm": true, "com.grabtaxi.passengsn": true, "com.grabtaxi.passengso": true, "com.grabtaxi.passengsp": true, "com.grabtaxi.passengsq": true, "com.grabtaxi.passengsr": true, "com.grabtaxi.passengss": true, "com.grabtaxi.passengst": true, "com.grabtaxi.passengsu": true, "com.grabtaxi.passengsv": true, "com.grabtaxi.passengsw": true, "com.grabtaxi.passengsx": true, "com.grabtaxi.passengsy": true, "com.grabtaxi.passengsz": true, "com.grabtaxi.passengtp": true, "com.grabtaxi.passengtq": true, "com.grabtaxi.passengtr": true, "com.grabtaxi.passengts": true, "com.grabtaxi.passengtt": true, "com.grabtaxi.passengtu": true, "com.grabtaxi.passengtv": true, "com.grabtaxi.passengtw": true, "com.grabtaxi.passengtx": true, "com.grabtaxi.passengty": true, "com.grabtaxi.passengtz": true, "com.grabtaxi.passengvu": true, "com.grabtaxi.passengxx": true, "com.grabtaxi.passengym": true, "com.grabtaxi.passengyn": true, "com.grabtaxi.passengyo": true, "com.grabtaxi.passengyq": true, "com.grabtaxi.passengyy": true, "com.grabtaxi.passengyz": true, "com.grabtaxi.passengza": true, "com.grabtaxi.passengzb": true, "com.grabtaxi.passengzc": true, "com.grabtaxi.passengzd": true, "com.grabtaxi.passengze": true, "com.grabtaxi.passengzf": true, "com.grabtaxi.passengzg": true, "com.grabtaxi.passengzh": true, "com.grabtaxi.passengzi": true, "com.grabtaxi.passengzj": true, "com.grabtaxi.passengzk": true, "com.grabtaxi.passengzl": true, "com.grabtaxi.passengzm": true, "com.grabtaxi.passengzn": true, "com.grabtaxi.passengzo": true, "com.grabtaxi.passengzp": true, "com.grabtaxi.passengzq": true, "com.grabtaxi.passengzw": true, "com.grabtaxi.passengzx": true, "com.grabtaxi.passengzy": true, "com.grabtaxi.passengzz": true, "com.grabtaxi.passenhea": true, "com.grabtaxi.passenheb": true, "com.grabtaxi.passenhec": true, "com.grabtaxi.passenhed": true, "com.grabtaxi.passenhee": true, "com.grabtaxi.passenhef": true, "com.grabtaxi.passenheg": true, "com.grabtaxi.passenheh": true, "com.grabtaxi.passenhei": true, "com.grabtaxi.passenhej": true, "com.grabtaxi.passenhek": true, "com.grabtaxi.passenhel": true, "com.grabtaxi.passenhem": true, "com.grabtaxi.passenhen": true, "com.grabtaxi.passenheo": true, "com.grabtaxi.passenhep": true, "com.grabtaxi.passenheq": true, "com.grabtaxi.passenhes": true, "com.grabtaxi.passenhet": true, "com.grabtaxi.passenheu": true, "com.grabtaxi.passenhev": true, "com.grabtaxi.passenhew": true, "com.grabtaxi.passenhex": true, "com.grabtaxi.passenhey": true, "com.grabtaxi.passenhez": true, "com.grabtaxi.passenhfa": true, "com.grabtaxi.passenhfb": true, "com.grabtaxi.passenhfc": true, "com.grabtaxi.passenhfd": true, "com.grabtaxi.passenhfe": true, "com.grabtaxi.passenhff": true, "com.grabtaxi.passenhfg": true, "com.grabtaxi.passenhfh": true, "com.grabtaxi.passenhfi": true, "com.grabtaxi.passenhfj": true, "com.grabtaxi.passenhfk": true, "com.grabtaxi.passenhfl": true, "com.grabtaxi.passenhfm": true, "com.grabtaxi.passenhfn": true, "com.grabtaxi.passenhfo": true, "com.grabtaxi.passenhfp": true, "com.grabtaxi.passenhfq": true, "com.grabtaxi.passenhfr": true, "com.grabtaxi.passenhfs": true, "com.grabtaxi.passenhft": true, "com.grabtaxi.passenhfu": true, "com.grabtaxi.passenhfv": true, "com.grabtaxi.passenhfw": true, "com.grabtaxi.passenhfx": true, "com.grabtaxi.passenhfy": true, "com.grabtaxi.passenhfz": true, "com.grabtaxi.passenhgd": true, "com.grabtaxi.passenhge": true, "com.grabtaxi.passenhgf": true, "com.grabtaxi.passenhgg": true, "com.grabtaxi.passenhgh": true, "com.grabtaxi.passenhgi": true, "com.grabtaxi.passenhgj": true, "com.grabtaxi.passenhgk": true, "com.grabtaxi.passenhgl": true, "com.grabtaxi.passenhgm": true, "com.grabtaxi.passenhgn": true, "com.grabtaxi.passenhgo": true, "com.grabtaxi.passenhgp": true, "com.grabtaxi.passenhgq": true, "com.grabtaxi.passenhgr": true, "com.grabtaxi.passenhgs": true, "com.grabtaxi.passenhgt": true, "com.grabtaxi.passenhgu": true, "com.grabtaxi.passenhgv": true, "com.grabtaxi.passenhgw": true, "com.grabtaxi.passenhgx": true, "com.grabtaxi.passenhgy": true, "com.grabtaxi.passenhgz": true, "com.grabtaxi.passenhhc": true, "com.grabtaxi.passenhhd": true, "com.grabtaxi.passenhhe": true, "com.grabtaxi.passenhhh": true, "com.grabtaxi.passenhmf": true, "com.grabtaxi.passenhmv": true, "com.grabtaxi.passenhpa": true, "com.grabtaxi.passenhpb": true, "com.grabtaxi.passenhpc": true, "com.grabtaxi.passenhpd": true, "com.grabtaxi.passenhpe": true, "com.grabtaxi.passenhpf": true, "com.grabtaxi.passenhpg": true, "com.grabtaxi.passenhpq": true, "com.grabtaxi.passenhqn": true, "com.grabtaxi.passenhqo": true, "com.grabtaxi.passenhqp": true, "com.grabtaxi.passenhqq": true, "com.grabtaxi.passenhqr": true, "com.grabtaxi.passenhqs": true, "com.grabtaxi.passenhqt": true, "com.grabtaxi.passenhqu": true, "com.grabtaxi.passenhqv": true, "com.grabtaxi.passenhqw": true, "com.grabtaxi.passenhqx": true, "com.grabtaxi.passenhqy": true, "com.grabtaxi.passenhqz": true, "com.grabtaxi.system": true, "com.grabterminal1agrab.Location": true, "com.grabterminal1agrab.location": true, "com.grabterminal1bgrab.Location": true, "com.grabterminal1bgrab.location": true, "com.grabterminal1cgrab.Location": true, "com.grabterminal1cgrab.location": true, "com.grabterminal2dgrab.Location": true, "com.grabterminal2dgrab.location": true, "com.grabterminal2fgrab.Location": true, "com.grabterminal2fgrab.location": true, "com.grabtuitionapp2": true, "com.grabtutorial.panduangrabshare": true, "com.grabyuuuk.kizdev": true, "com.hide.me": false, "com.ovo.energy": true, "com.ovo.merchanu": true, "com.ovo.merchanv": true, "com.ovo.merchanw": true, "com.ovo.merchanx": true, "com.ovo.merchany": true, "com.tafwsbkxrpqo.tgizbpeyjtp": false, "com.taixovvpehcl.cwgtgehyfaa": false, "com.tbtsvvrkicbd.zfxzndxisof": false, "com.tdohuobagupu.teidpkqcodm": false, "com.teopnhvaffdn.nyqrppsehit": false, "com.teukbtvpifqk.fdcppzyktrd": false, "com.tfontoimkgym.pmrwnuyorpb": false, "com.tglrbypaszui.ngbofzfxsxz": false, "com.theappninjas.gpsjoystick": false, "com.tiqfbkfugdnl.wlqohngjcza": false, "com.tjrxjmqhykvy.azpocnkjbgw": false, "com.tlbrhoyoxxwj.sllgxvdlaez": false, "com.tldbzmbdwluj.zgnuwpqwhkm": false, "com.tlnbjwgodgvp.sclncnadgxi": false, "com.tlntvjyvowgp.atdwdoovhxg": false, "com.tlqhybkpzugi.plumgmgcxgf": false, "com.tlxcoixrwldr.laxozrroqfc": false, "com.tlzmzjdbieuo.swyojepwjix": false, "com.tmkhhihaqbgt.hdjxjoqfngf": false, "com.tmtkegknzsvb.sowfcxjmzes": false, "com.tnmdoxcaqpfo.kwylziymtdh": false, "com.tnmpqyrehoyp.cxplhicdfji": false, "com.tojmkybaeaje.ezadseqhapq": false, "com.tomegjbfjbpw.xnhmlfyelvz": false, "com.tormuaxoxauv.izzvwfnwgst": false, "com.toutlffwhuyz.iercvezxvfm": false, "com.trggdqewjbdh.irmykpplajo": false, "com.tsbinxuydzfz.mqestikgrmy": false, "com.tshwcwoakcvk.tlhwnrhzgmc": false, "com.ttczzqgrodjj.lzpxdhugzlf": false, "com.ttyeflkwdtsn.lhwkboizghh": false, "com.tugsycjgqdik.drzaxsmsvgq": false, "com.tvdcmmrsislf.xnyknezawju": false, "com.tvikzulqrhqz.yolhdiofelz": false, "com.tvnneqboavmf.wqbirrvdufp": false, "com.tvukxywsfxze.nubgdhalxfp": false, "com.txfdjfaxmnex.mbvotpcknif": false, "com.txqtrihysngh.qlwtmjvfdjb": false, "com.txtyxcvmomfj.yrmfqreqjbu": false, "com.txyexwoktknp.zceijrazzfy": false, "com.tyaxmfgxfpba.kephjeozkdt": false, "com.tyykfaxklicy.xgxbhirfmey": false, "com.tyzunrmfpadz.somntcpnaer": false, "com.tzndwifukimu.rrmfjgdxqnb": false, "com.tzzqfhaxmycx.usmfhmrcbrc": false, "cxdeberry.geotag": false, "de.p72b.mocklation": false, "disumulatorsApps.flygpsgo": false, "divi.fakeGPS": false, "diwa.detector.mock": false, "diwa.detector.mocl": false, "diwa.sogps": false, "dotcreation.gpsanywhere": false, "dreams.studio.apps.fake.gps.loaction.changer": false, "driver.gms.burnout": false, "driver.mapx": false, "dvilleneuve.lockito": false, "erkie.zuper": false, "erkie.zupes": false, "fake.gps": false, "fake.gps.location": false, "fake.location": false, "fakeallocation": false, "fakeforinstagram": false, "fakegps": false, "fakegps.location.il": false, "fakegps.mock": false, "fakelocation": false, "fakelocatioo": false, "fakemygps": false, "fakemygps.android": false, "fgps": false, "fixlokasi": false, "fly.gps": false, "flygps": false, "flygpspro": false, "fr.dvilleneuve.lockitu": false, "frastan.fakegps": false, "garryzon.myloc": false, "ggarciaapps.gps.pokemongo": false, "github.marloww.moremocklocationapps": false, "github.marloww.moremooationapps": false, "github.marlowww.hidemocklocation": false, "gojiro.xposed.amenkkfused.location": false, "gps.diwa": false, "gps.systemless": false, "gpsadvance": false, "gpsemulator": false, "gpsfake": false, "gpsjoystick": false, "gpspatcher": false, "gpsspoof": false, "gpsspoofer": false, "gsmartstudio.fakegps": false, "gstavrinos.fake_gps_movement": false, "hamba.allah.swt": false, "hide.mock": false, "hidemocklocations": false, "hola.gpslocation": false, "idans.locationfaker": false, "in.testskill.anilkumarbhardwaj.gps": false, "incorporateapps.fakegps": false, "incorporateapps.fakegps.fre": false, "incorporateapps.fakegps_route": false, "info.locatizator.v3": false, "jamu.keliling.antiracun": false, "jamu.keliling.no.racun": false, "jappsoft.pocketjoystick": false, "jimyu.locationmap": false, "js.proteam.lokasipalsu.js": false, "kfn.fakegpsfree": false, "kudo.mobile.apa": true, "kudo.mobile.apb": true, "kudo.mobile.apc": true, "kudo.mobile.apd": true, "kudo.mobile.ape": true, "kudo.mobile.apf": true, "kudo.mobile.apg": true, "kudo.mobile.aph": true, "kudo.mobile.api": true, "kudo.mobile.apj": true, "kudo.mobile.apk": true, "kudo.mobile.apl": true, "kudo.mobile.apm": true, "kudo.mobile.apn": true, "kudo.mobile.apo": true, "kudo.mobile.app.infomudik": true, "kudo.mobile.apq": true, "kudo.mobile.apr": true, "kudo.mobile.aps": true, "kudo.mobile.apt": true, "kudo.mobile.apu": true, "kudo.mobile.apv": true, "kudo.mobile.apw": true, "kudo.mobile.apx": true, "kudo.mobile.apy": true, "kudo.mobile.apz": true, "kudo.mobile.aqb": true, "kudo.mobile.aql": true, "kudo.mobile.aqm": true, "kudo.mobile.aqn": true, "kudo.mobile.aqp": true, "kudo.mobile.aqq": true, "kudo.mobile.aqr": true, "kudo.mobile.aqs": true, "kudo.mobile.aqt": true, "kudo.mobile.aqu": true, "kudo.mobile.aqv": true, "kudo.mobile.aqw": true, "kudo.mobile.aqz": true, "kudo.mobile.ara": true, "kudo.mobile.arc": true, "kudo.mobile.ard": true, "kudo.mobile.are": true, "kudo.mobile.arf": true, "kudo.mobile.arg": true, "kudo.mobile.arh": true, "kudo.mobile.arp": true, "kudo.mobile.arq": true, "kudo.mobile.arr": true, "kudo.mobile.ars": true, "kudo.mobile.ary": true, "kudo.mobile.asd": true, "kudo.mobile.asp": true, "kudo.mobile.asq": true, "kudo.mobile.asr": true, "lame.game.fakeGPS": false, "lexa.fakegps": false, "lexa.fakegpsdonatg": false, "lkr.fakelocation": false, "locationchanger": false, "locationchangerpro.pwdesign.mdo": false, "locationfake": false, "lovestyle.mapwalker.android": false, "ltp.pro.fakelocation": false, "lunaapps.hide": false, "mobi.coolapps.locationsimulator": false, "mock.location": false, "mockgeofix": false, "mockgps": false, "mocklocation": false, "mockme.evolvan.com.mockme": false, "net.drkappa.app.gpscheater": false, "net.marlove.mockgps": false, "org.hola.gpslocation": false, "ovo.dotid": true, "ovo.ia": true, "ovo.ib": true, "ovo.ic": true, "ovo.id.banker.kyc": true, "ovo.id.lanrun1": true, "ovo.id.lanrun10": true, "ovo.id.lanrun2": true, "ovo.id.lanrun3": true, "ovo.id.lanrun4": true, "ovo.id.lanrun5": true, "ovo.id.lanrun6": true, "ovo.id.lanrun7": true, "ovo.id.lanrun8": true, "ovo.id.lanrun9": true, "ovo.id.stageUat": true, "ovo.id1": true, "ovo.id10": true, "ovo.id1001": true, "ovo.id1002": true, "ovo.id1003": true, "ovo.id101": true, "ovo.id11137": true, "ovo.id2": true, "ovo.id3": true, "ovo.id4": true, "ovo.id5": true, "ovo.id6": true, "ovo.id7": true, "ovo.id8": true, "ovo.id9": true, "ovo.id99": true, "ovo.idH11": true, "ovo.idH12": true, "ovo.idH133": true, "ovo.id_1": true, "ovo.id_2": true, "ovo.id_3": true, "ovo.id_4": true, "ovo.id_5": true, "ovo.id_6": true, "ovo.id_7": true, "ovo.id_8": true, "ovo.id_9": true, "ovo.id_A": true, "ovo.id_B": true, "ovo.id_C": true, "ovo.id_D": true, "ovo.id_E": true, "ovo.id_F": true, "ovo.id_G": true, "ovo.id_H": true, "ovo.id_I": true, "ovo.id_J": true, "ovo.id_K": true, "ovo.id_L": true, "ovo.id_M": true, "ovo.id_N": true, "ovo.id_O": true, "ovo.id_P": true, "ovo.id_Q": true, "ovo.id_a": true, "ovo.id_b": true, "ovo.id_c": true, "ovo.id_d": true, "ovo.id_e": true, "ovo.id_f": true, "ovo.id_g": true, "ovo.id_h": true, "ovo.id_i": true, "ovo.id_j": true, "ovo.id_k": true, "ovo.idi": true, "ovo.idk": true, "ovo.idk2": true, "ovo.idk5": true, "ovo.idk6": true, "ovo.idnkllvy": true, "ovo.idx": true, "ovo.idz": true, "ovo.ie": true, "ovo.if": true, "ovo.ig": true, "ovo.ih": true, "ovo.ii": true, "ovo.iiflvp": true, "ovo.iilkupp": true, "ovo.ij": true, "ovo.ik": true, "ovo.ikmnd": true, "ovo.ikmndf": true, "ovo.ikmndfa": true, "ovo.ikmnlo": true, "ovo.il": true, "ovo.im": true, "ovo.imdkh": true, "ovo.imdkhy": true, "ovo.in": true, "ovo.io": true, "ovo.ip": true, "ovo.iq": true, "ovo.ir": true, "ovo.is": true, "ovo.it": true, "ovo.iu": true, "ovo.iv": true, "ovo.iw": true, "ovo.ix": true, "ovo.iy": true, "ovo.iz": true, "ovo.ja": true, "ovo.jb": true, "ovo.jc": true, "ovo.jd": true, "ovo.je": true, "ovo.jf": true, "ovo.jg": true, "ovo.jh": true, "ovo.ji": true, "ovo.jj": true, "ovo.jk": true, "ovo.jl": true, "ovo.jm": true, "ovo.jn": true, "ovo.jo": true, "ovo.jp": true, "ovo.jq": true, "ovo.jr": true, "ovo.js": true, "ovo.jt": true, "ovo.ju": true, "ovo.jv": true, "ovo.jw": true, "ovo.jx": true, "ovo.jy": true, "ovo.jz": true, "ovo.kd": true, "ovo.ke": true, "ovo.kf": true, "ovo.kg": true, "ovo.kh": true, "ovo.ki": true, "ovo.kj": true, "ovo.kk": true, "ovo.kl": true, "ovo.ly": true, "ovo.v2.com": true, "parallelaxiom.fyl": false, "pe.alive": false, "pe.fakegps": false, "pe.fakegpsrun": false, "pegasus1.apk": false, "pegasus3.apk": false, "pegasus4.apk": false, "promob.changelocation": false, "promob.mocklocation": false, "rosteam.gpsemulator": false, "ru.gavrikov": false, "ru.gavrikov.mocklocations": false, "ru.gavrikov.mocklocationt": false, "ru.gavrikov.mocklocationu": false, "ru.ovo.film": true, "ru.ovo.mockovo": false, "ru.xbxteam.xbxmockt": false, "shankarlabs.teleport": false, "spoofme": false, "str.mor.mockloc": false, "t.j.k.moc": false, "t.j.k.mock": false, "ta.My.Fake.Location.Fake.GPS": false, "taasbrseuuot.rnhqmzyvvwc": false, "taazeuivznjg.oicztbgrbpg": false, "tabdxhypudmz.zevnispvobs": false, "taborqfhdvgc.cmtkibjrcnp": false, "tacjyeztbdyj.vtatcuyxaqv": false, "tacvgsqpbulp.ymmbirwawaw": false, "tadmrjgymxug.yxucveijcgi": false, "tadtmxscfppn.qdmvspdsnta": false, "tafiqjjmagmy.elnwwampvmb": false, "tafwsbkxrpqo.tgizbpeyjtp": false, "tafzutgarytw.rnwzelcymjd": false, "tagsnbkcmxtf.vsiiyafrnop": false, "taixovvpehcl.cwgtgehyfaa": false, "tajfughnirvg.rbzodfqnxrg": false, "tajkzdqepyjr.tvuyjohjcli": false, "tajqhzjvjxjz.efcgpjoexwi": false, "tajueosoahhk.ocgpjnwbltd": false, "taoqlbervsgh.zyptqxisdkg": false, "taplutvufmfv.cbcucjlnnnr": false, "taruptqdtzfa.bpslhkrhtis": false, "tasqkzhoyvkd.wybmtvwsziu": false, "taurtgftzmry.ormzxuqvcmb": false, "tausnlihvsjr.vjnwmflkppw": false, "tavfjatwwdxn.vxniidixqqu": false, "tavqxahsupyj.svgznekpcze": false, "tavsagnkrgmx.bqvzjsftgba": false, "taxxykocepdc.kkyqhobnlzo": false, "tayghxrpteml.myfchrmdule": false, "tayrmbmhpzkg.wmaklvgpprm": false, "tazdsxkclkwk.umzyhnwbhib": false, "tazvixxnasok.ojtcjswwyxt": false, "tbbicmelmezt.vrpaytphdnt": false, "tbcgbhytrcpx.rizllwbclaj": false, "tbdfopabvpqv.avqidakmdap": false, "tbdpqhbzisxi.mcbhyowimxa": false, "tbecufkricvs.pjrprcxgqdp": false, "tbejkpidofht.ncusrbkmwyz": false, "tbfqbkcuxuib.hajivqfmepg": false, "tbgvhtrkcveu.mboswggjrmp": false, "tbicdlvefpsk.yojeuzjbavc": false, "tbkjmnfomeib.iblibkadixe": false, "tboausvhvwxv.wiachbpgygn": false, "tbojtbhwjuee.nfgnsvopglt": false, "tbqiqscvdmov.lnlyshzsbfb": false, "tbqsdwddltoc.ljttlgbxusc": false, "tbrltllawsdl.lvbtnsbymie": false, "tbrtkjmbkmwl.znwgxltlhvq": false, "tbtdwigtvnge.mcxbsekguqn": false, "tbtsvvrkicbd.zfxzndxisof": false, "tburlbyshnuc.qvvfbdwwjfs": false, "tbvngpwvndbu.yzxhlbkebkw": false, "tbxnyxkyzqmh.ysgzwtdjswq": false, "tbyztfcdxwlr.mvvwekhstea": false, "tcbsrcjcjvew.rrnmvaziaox": false, "tccpwvevscwl.qfchwetavhx": false, "tccybbbbvriz.nwztypfffxt": false, "tcdwkhhjslej.xupomthgdml": false, "tcgrwbyjaxux.qeelnnmzssy": false, "tcgsmxpwiagb.rjkorcwgvho": false, "tcgtihyjuzsl.iqfhjzbcrmg": false, "tcgyrfccgxby.fqiwmtqnoaz": false, "tchdrxtddxdn.ewasiknbkyo": false, "tcisxyjpowcg.qttwqefzoiw": false, "tckteqfvzwki.nqogefqaxsn": false, "tcojbilsogsh.jrgezwqdmju": false, "tcppbzirjqmr.ugeotujahvp": false, "tcrnvhjsqnoq.hgkkisfzrvz": false, "tcrprcxjjmud.bkgkgrcblzp": false, "tcsxkwpuuhpy.pugtjvckkva": false, "tctyuzcfndjb.imtzaqjaeyn": false, "tcuksdkpascl.wmauptfjpkm": false, "tcutmfepgmbr.rabqowuogft": false, "tcutsjrvonxm.svmtmevidof": false, "tcvqtdqnysgl.ndwqyzrxxgw": false, "tcvqtdqnysgl.ndwqyzrxxgx": false, "tcwkszmobcgl.huerexiwzot": false, "tcwsgtcthuwd.olwfvofkhbm": false, "tcymnapafpww.esxcgnqooic": false, "tcyrmuclbnic.ihwjfugrfni": false, "tczgxdixpiwx.inayflelyka": false, "tczpnalevlgf.vlpgxqslydk": false, "tdaljzpbxpfk.vnelajpucpx": false, "tdarbqhzmror.defidcmeygo": false, "tdbdnyrutthh.jgcvsgmvjfi": false, "tdbofxuqdqxu.yxmmsfanyre": false, "tdcsvwaneyvg.wdxegaanmnb": false, "tddntvduqlbh.ncddmmafnem": false, "tdffdhupvzyu.cahtyjrfndb": false, "tdfimkkafbfm.wsevyxvupuz": false, "tdfrdlpqewwb.jrrgjlypgoz": false, "tdfwrndrzwso.iwkyupcnlbt": false, "tdkyvhvysvon.lqcnmlvybht": false, "tdmmwgrjyzob.dfncwsuxslw": false, "tdohuobagupu.teidpkqcodm": false, "tdohzxqyawyl.qxuzlfwnndv": false, "tdpennkjqxbd.iyhduufrwtj": false, "tdplnkdmyotz.rjzqvavhtgf": false, "tdppyrcswbug.vwmaabkmcxg": false, "tdqvolmrfvpy.lcfvkptzevo": false, "tdsntvdclbih.uqcmkmubdvt": false, "tdtvmglcppnq.ulhdujvkwqs": false, "tdtxamjsyabf.ujnenwyrayi": false, "tdurjbofdgjx.ycgksbjfjtq": false, "tduswowswqty.pzkwccltxvs": false, "tdvjmyprkfzr.osjvzvvuxzr": false, "tdvkcvehyoed.onndehdfegp": false, "tdxcksblnqaz.lrovjydibne": false, "tdxkuuahypya.mhmsrinabjr": false, "tdxncfwpgclu.cioqroiqmmp": false, "tdxrzofdrcwh.edzzghlyenr": false, "tdybbmsrzhdy.xhkzpolvphr": false, "tdzpyhymfeoq.ilnuwgtmmah": false, "tdztirwpjeiq.roqwidskkus": false, "team.fgps": false, "teaobfseikzu.akbcxmembft": false, "teaumqokbiqn.acilpbdnrnp": false, "tedyjjvsxsbv.nxomsuxqtjq": false, "tefgqjrftqff.bybfwwdqgms": false, "temgxhzpdoyx.ncndluocfzf": false, "tenbqiiqvlfe.wzrxwzefknw": false, "tenowyctpcsz.fzpxphqcdso": false, "teopnhvaffdn.nyqrppsehit": false, "teowahpvhqtr.cvhyilhjtoe": false, "tepyzegkcgsm.sguvynjsszp": false, "teqoqezpcxku.dvawyeknppb": false, "tesrmvpfjgfu.qwgaerlnauf": false, "tetctikkccyw.xlfyxjaauih": false, "teukbtvpifqk.fdcppzyktrd": false, "teukowikwoli.cjuqafdxlty": false, "teukyzqwtymu.ngpnreldnai": false, "teupaomuegrp.qdlxxprdyfw": false, "tevfrhyxrgxd.urybkuxednv": false, "teymshlfftma.abhzsufecpg": false, "tezfuutouiri.xvjzjggujdt": false, "tezheiyzrzni.kmldjgvzwdm": false, "tfaulypbfuif.abegtxuasgw": false, "tfbdzydejilm.yguvulkbojw": false, "tfbfrbcecohg.czlmssohlka": false, "tfbnfmcdqldg.xmrendwuqfu": false, "tfcqxrgjwgtb.cmheshjnclm": false, "tffkwvnaxotm.lznqjvrftvi": false, "tfiarvsratia.paubyglwuiy": false, "tfjdsnvcwkii.nzyapvhalgt": false, "tfkkgsibrxod.shjbakjnrlp": false, "tfkpnqxuwlqu.jsixznjaggt": false, "tfkvkcotofue.zrdsyjdnsro": false, "tfljleuhwcud.vohrwrkzwvd": false, "tfmibnxsvhsy.mjtlrbibgga": false, "tfmsxjoxldnj.hsgpekdcpfu": false, "tfmzlsembuev.zqnbqilmneq": false, "tfncqbxrugnc.mciocbfcymg": false, "tfoizcdtyekz.xdmrqmmhsah": false, "tfokhzvdvgov.yldtdtxfzyw": false, "tfontoimkgym.pmrwnuyorpb": false, "tfpxvbayjxmj.lybeunljtwg": false, "tfpzxthjkutr.lzhsnfyelud": false, "tfqspbhhqtyh.ubzfmnsucbr": false, "tfrhaosobhkw.xvxigthyovd": false, "tfsmxujopfyu.jeymfmbbvlb": false, "tftfowzwtyoe.dmjwclqxrxc": false, "tftqohujydvn.wtciqlgrtpm": false, "tftxvkrjzafl.obltckvimdq": false, "tfwfflrhyioa.afznrbmwrfg": false, "tfwqrxuylueu.cbnacdonlxe": false, "tfxyktqgucyp.nwxgxwlsauu": false, "tfxzirozkvyb.xkqgapratrg": false, "tfyokzemujgc.drpamqghlbv": false, "tfyukahtspxb.rvyheqyyujt": false, "tfznywsldwxx.kvtayamhqjl": false, "tgaqpawwowfq.uuzlcrcrdeg": false, "tgbdnjrhfrhi.mctugguihwl": false, "tgbktrkecnyc.livvlfkwqcd": false, "tgdozxbewzeu.hsultjhjxup": false, "tgfdgaufgdco.ifdsvpxnmjq": false, "tghnvsfhjysm.foudaivoqrz": false, "tglrbypaszui.ngbofzfxsxz": false, "tgncdzcshhgr.ntxknvbrvoq": false, "tgnoenhxcvkv.spzlkxoihwi": false, "tgonuuoadicy.lolmcegyovq": false, "tgpecfjpqfyv.veiorhwrhud": false, "tgtziuhvsbyv.scosnagrrtz": false, "tguhfpxkmotu.gwvljlbatrz": false, "tgvexbmcflju.ewzquznpqez": false, "tgwiybxcadpd.yyalcqxjcwb": false, "tgzmknjgpudy.jehwvvunhqg": false, "tgzxrfsqvsmn.mvbmcrrwsne": false, "theappninjas.gpsjoystick": false, "thhyilzsfwnl.qzboxwqjryc": false, "thinkskygame.itoolstwd": false, "thnobhlptsfp.jjmygmdhtcg": false, "thokbnlrzunn.vmbfghzxjgy": false, "thpifeegejbg.fqervvdlqje": false, "thrnisaysqhc.dpnnhzdhwnb": false, "thtjnayyudeu.ekzigporjec": false, "thuvifrakzca.bpovqikxpyu": false, "thx.to.tjk": false, "tictcrtlglqf.ubweskfdpln": false, "tidpsqylktbw.tjhzpimzzpr": false, "tigxyhrjqlco.etnzmmemfoa": false, "tiifkcczlaoc.yspepeyrviw": false, "tijhyljljhdt.xupdktmkcpe": false, "tijkqvxogibr.byxwdqqgqsd": false, "tikojzyuxaop.fsqodwnmujp": false, "tilawahquranlengkapzayee": false, "tiotmyffwdxi.xqlyihmaxgy": false, "tiowuktnecfg.ksxqfnxlycl": false, "tipssnfheljp.hzzuunkqbsf": false, "tiqfbkfugdnl.wlqohngjcza": false, "tiqnqcdvgpxc.wuyexqyegmz": false, "tiroxzkuikqf.fzalnlhghhw": false, "tiskzqphodxh.lflckoupnnc": false, "tistory.maxxgreen.app.virtuallocation": false, "titdtnydlwyj.hurduiecfka": false, "tivlhwsyocgy.pftmsgiajlx": false, "tivmedaqatlv.vrdwwyoawjh": false, "tiwfevrsozva.wyibhspjmgb": false, "tiwuivdmieil.qxcflphfphm": false, "tixnsxrelrde.vluuwdubulr": false, "tixogsrhxpdz.afnddraxkfh": false, "tixqdeiucewg.lrewurdkmqf": false, "tizwerhecpqy.jbtyfxjieqj": false, "tjaiqtlyyedh.aimxlqafkut": false, "tjbeyfcgjxxo.smsmqkvzjem": false, "tjbfudyifwxn.wrqojwmfqdz": false, "tjdnhscymgqu.hmynedgnluu": false, "tjfzmrzubspn.ulfqankdhrh": false, "tjfzsjjkqong.wwxikjvpdxo": false, "tjgnmoygowoz.qrwdnurulac": false, "tjlpgovywute.umvruilhznn": false, "tjonmzfphyio.htjssufauqq": false, "tjonmzfphyio.htjssufauqr": false, "tjqbfvlwusgj.txuzamzjqmo": false, "tjqncqfwvbrp.uopozbamkje": false, "tjqtzfbehsil.idjbuokkzad": false, "tjriudpvxisj.uertalxeqtx": false, "tjrjeksbowqg.xjhmstxzcbl": false, "tjrxjmqhykvy.azpocnkjbgw": false, "tjsfgrmftivl.stekkocowus": false, "tjszmvmkjoml.jhkgbqlfbhc": false, "tjthklqzjgyq.nkkwsibraix": false, "tjuixltcizbg.laetrbpzjxf": false, "tjvpmdsowqwo.mowvrxnmfpb": false, "tjwmbffrpelq.cpmbnijwfyf": false, "tjwrxdbuppuw.cyumqhougjz": false, "tjwsyybhxmgh.ykzebbamekt": false, "tjxzhjtuxiio.qfjvsagjqfe": false, "tjypimwkoeir.rncfzlhrhvd": false, "tjyqiwpuhtka.vnrznqalobu": false, "tkacqbgbbsqo.vvflxjxobjm": false, "tkaubssqarqy.bdgqumoeloq": false, "tkbjyuqzkvkv.kfqaugoopwj": false, "tkdpqehgmsvh.oacwuwllkwp": false, "tkdqcfzauhiw.ubxqwphzrlg": false, "tkdrkwohrkyg.reecehycbrq": false, "tkfcsorkymjk.oyhedsplesa": false, "tkg.jamu.keliling.antiracun.v2": false, "tkg.jamu.keliling.antiracun.v3": false, "tkharrriries.frttsopiics": false, "tkhbvvkfwtww.olashhcezag": false, "tkiduyxsyvey.nxaaqwgebeo": false, "tkiqfokhwadk.jdosnkagcew": false, "tkjxgifxpake.ybqyntorbwe": false, "tkmhlrjcdgva.nzacetdappm": false, "tkogxvzwuwkq.uweuztieoau": false, "tkpqvlymqalv.hspqtoclcqd": false, "tkqbvcawxufv.kddfkilswvi": false, "tkrldkvdyxbm.mlytciugmpl": false, "tkrrtvdgvcex.jczqgthrrat": false, "tkrsovufmrcw.ynrqtcnhyfh": false, "tkrsyldywxmp.ohcsqwjpbbm": false, "tksdooyfiald.aetkfmelpcp": false, "tktcjwomrzuh.ymhypigvpyr": false, "tktpxtlqhvok.hqfxqcpsxof": false, "tktvoksgrpae.zuxdheiuprg": false, "tkuhegdrokxa.smbbmlsaycv": false, "tkulmkyfmene.dvpldvcgadn": false, "tkyoimuhkrzd.uxxhibkszud": false, "tkzisekjpfvd.sgrdzugfypf": false, "tlbgjwulsulr.efcbhtvlana": false, "tlbrhoyoxxwj.sllgxvdlaez": false, "tlcrfuioshvu.stgtajwoxgl": false, "tlcwkutnrxuu.bvpusrdhgdh": false, "tlcwujgtosgy.kxxlvsxgdqb": false, "tldbzmbdwluj.zgnuwpqwhkm": false, "tlflylzhiwca.wfewsbcxljs": false, "tlfzcyslpylo.haivajdmnfn": false, "tlgfjmhlpcwr.hhbhsdjltho": false, "tlhjonfdckgz.wlontlnbkeq": false, "tlhootmkzdfs.azdftwfoevr": false, "tlingnztvklu.iyqftlqjtem": false, "tljctskwmaya.fzodcgxpmyw": false, "tljgcabapenl.virtfckjzut": false, "tljxozefbcna.znwesfoyzaq": false, "tlkfdluhvwnm.xgxeumizmtf": false, "tlkqhkcsveuc.ilzrpsmlbzj": false, "tllxodkyufhw.vxpboswmyai": false, "tlmtlfsntktj.sqamwmvjokf": false, "tlnbjwgodgvp.sclncnadgxi": false, "tlnerbqfcxfc.dbwgahruhan": false, "tlntvjyvowgp.atdwdoovhxg": false, "tlqhybkpzugi.plumgmgcxgf": false, "tlrtmgciwwny.gylsruqprcn": false, "tlxcoixrwldr.laxozrroqfc": false, "tlxiwdbcgzci.sthssfrdhin": false, "tlyhiisrewoe.tjhkdndxoxc": false, "tlyrrwmhjlxe.mtfhnrdaari": false, "tlzcfsdjziwn.avaduecltha": false, "tlzmzjdbieuo.swyojepwjix": false, "tlztdxsshzwr.nqmtysymvjn": false, "tmacfkvjjron.otnsdelontz": false, "tmbgygakmxjf.alvxykifvmf": false, "tmbvtiezhfvm.fyboipakjvj": false, "tmcqnkglnzka.wwiwluilogo": false, "tmctmaetuhzx.imrgjotilvn": false, "tmdfdxkfpwkq.qcxmvqvwyof": false, "tmejtqmicdae.gtreksutbmi": false, "tmftiftajesw.lttvuvununf": false, "tmfwnvxbbdcj.ctqoyhwyaqg": false, "tmghgmjxdeto.ywofmfaehge": false, "tmglwgqpehgg.vrdeftncpzs": false, "tmhchmbwlftn.clkojlwyero": false, "tmhlzgxpuhjc.shcptcjdavg": false, "tmhvcrmsyclo.pvxyeojzpjm": false, "tmhvrkiwpwfz.akbctpdvpuq": false, "tmittuoeiyxh.jlqsqowylhj": false, "tmiwddccccyb.bqaahijghuh": false, "tmjunracfqho.rhhsbirynsf": false, "tmkhhihaqbgt.hdjxjoqfngf": false, "tmkutsbwirlt.lcjetcafbhx": false, "tmlunyvwphrp.ejyrejqsfkd": false, "tmmmyvhnfzfi.ckrayimfavx": false, "tmmzrjepsapl.zzgkmhdbuio": false, "tmncunibzkoi.ofmnfaoodaf": false, "tmnizoznrohr.aqumfedwmhq": false, "tmnpiefzrsfl.ymuvqeqfdxb": false, "tmnulnscholz.ddwofsmsddz": false, "tmobrziqwpvw.ydmqfgyftjn": false, "tmqfurekabxh.eryvbzmvwdl": false, "tmqpvhqelnvx.logalprulws": false, "tmrzwlvsmwkz.ngqcscwdoyc": false, "tmsgrwljctdc.xczmqqcblxo": false, "tmskhwqptnou.pforieoeloy": false, "tmtkegknzsvb.sowfcxjmzes": false, "tmubgjxfslsd.sxiiwdrxqqx": false, "tmuqqtdunvjl.xrreasbtjme": false, "tmvxxrldjbrq.qovvmqnbhhg": false, "tmwssvhjjfvi.mmwfpilznvz": false, "tmxhagltzpyt.jyibzsofcrf": false, "tmxhrkmcprso.jeovxptglqt": false, "tmxmyssgdiiw.syengebqbcq": false, "tmxuyjbqgrin.ttqbtgdizug": false, "tmyjgxaxkntx.bwvbvohfoqq": false, "tnaoysjpdwie.cxstdkfweia": false, "tnatjmayzpqd.sqbdwrejpkb": false, "tnduirdvjvlu.ezvexvtmqsq": false, "tneejddqygdv.gtfrscvgxav": false, "tnfrxqkmziwx.jbenqkvkwmf": false, "tnfukmuzmwkz.mijuyeffsph": false, "tnjhbmotmyym.wjgdyjeijds": false, "tnjukgwtpkux.zlittzcqjmm": false, "tnkgvdddokbt.oxrrnnnnrmf": false, "tnkonfigwdvf.nkpgwrlcduj": false, "tnlyjzpqpmvl.aslsvbpanxp": false, "tnmdoxcaqpfo.kwylziymtdh": false, "tnmpqyrehoyp.cxplhicdfji": false, "tnpiuhdmdqwi.ydebevvuzwi": false, "tnpocqbrdqym.dgktuswimbn": false, "tnppbcilxprj.xhqtdoomqda": false, "tnszxilyyxpw.dclxgdbfmvp": false, "tntlmbxknpyh.pddiktmhroi": false, "tnupvumxlrzk.nuhtwxvkyeb": false, "tnuzcfyqevcf.xvottkkvvzh": false, "tnvixcpzetiw.vcfvaigonqm": false, "tnwowixqwmzo.aefwyrgjept": false, "toaorhwximdw.jvhlhfflhtv": false, "toaqzrcyvmrw.agddgfmwsip": false, "tobqytzejaer.ielfqjgmawa": false, "tobwisbqumoq.khbwzimsepy": false, "tocqaelarwsk.lfqokknqdwn": false, "tofpuyqfaiqk.ilxorsogndm": false, "togbwempyaog.zsjifqhfkdn": false, "tohgnxoxgtwm.ixiddnpomxf": false, "toitvkvdilgg.zwwinvdxxvn": false, "tojabyhjkbqh.fmtqxbzyenz": false, "tojfgfubrewn.mwaahodjlwt": false, "tojmkybaeaje.ezadseqhapq": false, "tojtjhekynbo.iwtndjhozxt": false, "tojylwcjgwif.tfuxqjcuduq": false, "tokvbjtzhnlo.mimmqjlzgzd": false, "tollyfxsuqsi.mvomlfmsjhl": false, "tolxwksmfsjm.vnfyrbzxftj": false, "tomegjbfjbpw.xnhmlfyelvz": false, "tonbkabkzrbi.jbkskmwpoue": false, "tonorfdjzhkd.zmdioqkujcu": false, "tonvkjeycyhy.longzvtkknh": false, "toodbhwkehuz.jenvrvehnah": false, "tooenmsldhbt.bkmstrsksir": false, "toolspro.gps": false, "topzskhssfdq.czjtgjbohqn": false, "toqezietfzqt.jdrbnqeokyt": false, "tormuaxoxauv.izzvwfnwgst": false, "tosgnqdnfbqo.qxrfogzfhrs": false, "tosxqfwxmqub.aspvuulrjbb": false, "toutlffwhuyz.iercvezxvfm": false, "tovbbdjhpcur.igfcmhjvlbc": false, "tovugrcvdnrt.asdarwdrtbh": false, "towhuuawewrc.fvphgmwydlt": false, "towmrqacfeqv.lznvyqtgzfc": false, "toxzmjhfmjis.etraxkncerz": false, "toycogtqdzrc.usobqpnxmnf": false, "tozydagsylgd.zcwscbolphu": false, "tpazazxsxule.ezpwmcccbkt": false, "tpbdrobcwhhv.dpwvfvugpul": false, "tpcbhywyzyxf.gaiwgcojslo": false, "tpcpqsmmhrdm.kftrqfdlmmc": false, "tpcvtrkuykuz.dufymtofcub": false, "tpcxbuscvhjm.vbmnptbruvk": false, "tpdxmcdqxxjv.uqjyjqqyegx": false, "tpfhqxuolruh.bronfudxrih": false, "tpfomjlbjfzf.emurvhqrvxg": false, "tpfzdqtdcujf.czatadggxqn": false, "tpgksitrnzxp.xdjfykvqfyu": false, "tphoytiwharh.ucktmsgdddo": false, "tpmyqklotcoq.kcksqkwbbrn": false, "tpncjhjcqfwi.sxyzdgwsnhe": false, "tpndedvsohsv.jkwgwnzftan": false, "tpoctsrndbqr.pzoyjgonvqp": false, "tpplkrgmxzcy.yctfhoinzbj": false, "tpprkdgbbyyw.uffeikdpgkm": false, "tprvqsehzmvg.htbxyxpqsdz": false, "tpschyhgdttp.fjyqhpnopga": false, "tpstrtczvhol.wfbplxmekbp": false, "tptdwvteoszo.qnnmlpmcqia": false, "tpvjyaaougjk.rtgruuqnkzp": false, "tpvomsbjmuqu.yiipwvklwvg": false, "tpwgncecmunn.jzxzzbmwnyt": false, "tpwxemydefrx.cemwucjabar": false, "tpxtuamggpwf.brgnaareyvx": false, "tpyitrbgakam.hgbmlvrynzf": false, "tpyjssioryoz.fkutglwacnf": false, "tpysootbimlw.etfxypdbsdq": false, "tpyurkubltpk.lvzybwpvcdi": false, "tpzrjwtgzlzb.qdqngqevdgh": false, "tpzvhurufchf.vxgdhncwyla": false, "tqanvmepkpko.qsruntquovd": false, "tqaobhltxlui.pqtrqzhmouy": false, "tqceuljvbnfn.mdmyajpkuov": false, "tqcgjkoqyrpq.cixppignulr": false, "tqcrayakvmzs.daabifoegxs": false, "tqdkixnvqfho.ghtxefzfqss": false, "tqhjzuprjojk.uiprymysjse": false, "tqhwejwoezkb.pvfhlxdnouu": false, "tqiifcghttgr.rnuitjofufd": false, "tqjiyyywguhc.hnjrmcaebgf": false, "tqknmblkemjt.bxtoxpqdsei": false, "tqkthjfmpksg.ktfswqdktbc": false, "tqmizajmygjx.mxjajivwsuy": false, "tqmzpdcxznao.zwspikmfoin": false, "tqnbkqfkmemi.jxhqjbmcbju": false, "tqnvfijlwcxr.lcmbfaqwqli": false, "tqotdhyrhdsp.jraxmofhyjn": false, "tqoyxeoymfpj.ekilbjdcrqf": false, "tqpsogxirftq.twbcnphmaqx": false, "tqqsvnhxrisq.lmvunvtnfbg": false, "tqrftqourmzi.rckpqwkpnrv": false, "tqsdorpludpd.zsxdoyreodd": false, "tqsgztkbvtdg.dejcbgoatlp": false, "tqteoxzxmzjy.eqipzqmjudw": false, "tqtghwpfpfjw.dghtwnuujve": false, "tqtjxaapqnvm.lrgktpwayfw": false, "tqudtdgqcipx.egvojurewzn": false, "tqvjkdpwsgle.itablpnrvko": false, "tqwxtmyqhzny.erbydepvgos": false, "trbenqxufhiv.tqpvdkmdwyv": false, "trbgciausuek.soqfjxwyjru": false, "trbksktukmkt.rfkzbkdmrtl": false, "trbvyttprdoa.zjjtqcoxcgz": false, "trfinrdayaek.zdaulqhqzbr": false, "trggdqewjbdh.irmykpplajo": false, "trgnixjjhudz.xomgcikfwwh": false, "trhipwkjnefc.mrhcoowbjnb": false, "trhqeqllulpa.ncgwbkjxqmd": false, "trhuiztafvza.rsivbscyhne": false, "trhxinvmrcix.qdhohehqbda": false, "trimlzvyjhbq.nhcyhqvylaf": false, "trinus.faketrack": false, "trisrmvpbpti.vunlrfmwylv": false, "trklchhmrcrk.jefvvalrari": false, "trltexqmzkkp.ijxhjbillav": false, "trlxgzqbexrl.vqjyyedqksl": false, "trmbylpfuwtk.brwdhkbvxja": false, "trnfqwutoviw.puxzkxvwlxn": false, "trnfrcpzylzi.oardnzhhqtv": false, "trociyeajymg.cmxcteayttt": false, "troollhhhhwd.drunsvwvkec": false, "trqmdtfekorv.lrfidzooybu": false, "trrlkjolffpq.ldkiwbsdzjy": false, "trrmccfgovew.wueanirlpqy": false, "trrtlibpxepp.uwogmqwrtst": false, "trzhbiylkmvb.uybkvkarpkg": false, "tsayqibvbmcc.qmrzlxkjdia": false, "tsbdshrnruot.cqnbnchzamg": false, "tsbhxdiijrzm.vmrkeeembdu": false, "tsbinxuydzfz.mqestikgrmy": false, "tsblnggdofed.ueqcxfpchjq": false, "tsbvenqyjxuy.ahhhwpvfuex": false, "tsbwmnrjcwhk.tnwrpduldjl": false, "tsfelsypapkd.vggzgvxxpnt": false, "tsfrxlvliqih.dmkhrqlihfs": false, "tsgyfzuudxpk.aywomjboujc": false, "tshiyykhxkqi.kxxxroivvmc": false, "tshwcwoakcvk.tlhwnrhzgmc": false, "tsickvolhlvd.hiykckvxyra": false, "tskjqohuisdq.wehttpzxmst": false, "tskrnhfyxavi.bkcelvgxmqg": false, "tslxbeuhmubc.hstskjdzgpf": false, "tsmvibryoyjg.lshmubnletn": false, "tsngmrhvmuau.wejifulhwwb": false, "tsolyzzvjziq.ygzbaclkrfa": false, "tspxmqazycpe.mgcaaonzipu": false, "tsqdaiyllpwr.iwanpicijue": false, "tsrgmyhphwlv.iihcxqfxqtj": false, "tsvvoyfnprwk.tbemqbrngky": false, "tswdcfvlywik.jiybahqnsom": false, "tsxlnoawbyod.ebxabhjdzxm": false, "tsypmjdraedn.rfjoefihwjr": false, "tszgoamkfbqy.kmybglrzvsg": false, "ttahqqitgokv.sshxjkxvddt": false, "ttbhrldrhnlj.rtrnaghyada": false, "ttcfogfyvyzi.ukfxbklvung": false, "ttcowgdeeazd.veeapvqkwtp": false, "ttczzqgrodjj.lzpxdhugzlf": false, "ttdpyzeopfvz.afyyhbkkauz": false, "ttduguyrudiq.bhlosdlsxlh": false, "ttgrsiurizlt.kbcwjjgjhuh": false, "ttgsqxblttue.vlnwcglcmoz": false, "ttikheiiedbz.tmsknmxbqnk": false, "ttjibjssarzu.ftyxbwgscqz": false, "ttjlyfyxvszg.wnbnwqvvqsi": false, "ttlouqkesiyn.qfqyanugxwt": false, "ttozaiomlopc.mwakzpfdeik": false, "ttpbswfyaiju.lcbzrrzgjxz": false, "ttprnvolvebe.ahdgmsmjjkl": false, "ttpvmzeoyqzp.ypoeqkvfkpk": false, "ttsrhqunuehz.rzkabexmstv": false, "tttvbhzbkudb.klpqelnqqkz": false, "ttudcvacwabs.sdcqapnbuzi": false, "ttxcujglfmpy.janifkhwpha": false, "ttyeflkwdtsn.lhwkboizghh": false, "ttzsufwbvsbt.sxthmtkslry": false, "tuafndjlmafj.dexxnjosxsh": false, "tucvjwsmbwma.dehylxgftil": false, "tufxmmydffhw.ledvnbhwekb": false, "tugsycjgqdik.drzaxsmsvgq": false, "tuieffflqmgy.xvyzjjiidil": false, "tujdovssrbvj.aokptqbpopb": false, "tujqirmxttzh.xxgivforrjf": false, "tujyizsfgvgj.jjiqqepcejr": false, "tulfaocesmvp.nsfprjxuddy": false, "tulolqqvmsms.zffbbbbqpzc": false, "tumhgollqaip.feoirpczkpm": false, "tumscnlnvdzc.dylvrfpzrxx": false, "tumzdaxleqhd.cdgzrvqhojz": false, "tunfbvtbhxhw.hrmwqbeftyi": false, "tuqklekashqw.psdkdiupbne": false, "tuqzxdzkmbbf.vtgwnjiuhpy": false, "turmumudlcou.zlvtxuiewjh": false, "turnmccccdwd.aaaayppoqoc": false, "tusfulugvdjv.xuoxvzgeywb": false, "tusrrxrepgdy.gvlmmbilzrq": false, "tutokwwswudw.lwcbazzrram": false, "tuvirgmxvtce.rlpmbdfskdo": false, "tuvmvdrxcrnd.dupzxhrjalh": false, "tuwecmaapxjv.orkmgctrpao": false, "tuxfnvktbiue.nrpkxznxqsh": false, "tuyfvlfqqier.jxmnewxnrqg": false, "tuyqzrxqoafc.aeaimnafqae": false, "tuzevmhwqmza.ugktbtiymbb": false, "tvcfozygsqka.eennsjkagfn": false, "tvdcmmrsislf.xnyknezawju": false, "tvgylgoyayqy.yzqlixipbep": false, "tvikzdzrtqal.qkemxoyctxl": false, "tvikzulqrhqz.yolhdiofelz": false, "tvkiclhqfrkr.rqexhqozcvg": false, "tvkvcqcxzodk.hfpwvbkbxix": false, "tvlcyhgrbzil.uomtehwissp": false, "tvlmtvfthsmj.uclwewezlgx": false, "tvmmtcebxwjt.foimbxufrfo": false, "tvmpknzrkujz.nvknptfizsn": false, "tvncljxhannh.lkdxygpafvc": false, "tvnftrxgunzf.ntfeyiiccyr": false, "tvnneqboavmf.wqbirrvdufp": false, "tvodqvwhfcmg.daokcbdyqlb": false, "tvpiegixqamj.pltadrwisdp": false, "tvqrycsxewpi.ysxsigaogmz": false, "tvsygmbflrnu.qbaimmqjhrp": false, "tvuirhdtkiiu.ldtytecmamj": false, "tvukqtmkffhj.bfzqfixccbx": false, "tvukxywsfxze.nubgdhalxfp": false, "tvzevgosxgna.avrevxlkckx": false, "twbezrgnfgji.qoiyfpoinew": false, "twbhwkpohfby.zodehidfegh": false, "twbsjwvwqntq.scrrlarfpwf": false, "twczmbvuegck.lkcwukzdres": false, "twfgyspqpinh.ialclaqeczl": false, "twhscbgwuitd.zdkhgcowsfl": false, "twimaadlirsh.acbhcjmgsxt": false, "twjhbxajirgr.fhmqwxbmrli": false, "twjxzzmbzxev.omfxhvupgzi": false, "twkjhkvusbri.pnxlqbqrfxh": false, "twkkrogjkqct.meoryewlvyd": false, "twlbfflobblc.exvmnavdtwj": false, "twldwdjtscze.vnqficzycax": false, "twlqbuekrgdn.afaccsbvwll": false, "twmpswudwylj.oyfscbvumck": false, "twowqjedzkot.cwavcfnobbp": false, "twpvidtttavf.xktcslpqrxr": false, "twqfrfwabdpr.pqpnkxfmpxw": false, "twrtdiwhkbcj.qpxrzyjcpau": false, "twskfhcsvgva.rzbcslmqqep": false, "twslywczgrme.pqnmwqoimfa": false, "twszxivwadgb.pxjvhxirbqt": false, "twtbgjmoccuo.lsadwmfalwu": false, "twukaeiexbyv.ynmywsbstri": false, "twukvszzdtjv.ettpzbdairm": false, "twumbcwkqkei.xbcvtzlpmum": false, "twunrkikhxwe.kzmiphchedt": false, "twvmxqjeumvz.vopndfhblhl": false, "twvxgbidfhmu.nmaadqtuttg": false, "twxxjqbjkgtj.wrhbpdlrdfo": false, "twyjsqtdehjp.makrdicursg": false, "txawwbzvvvsv.wodsskwiyzu": false, "txcgrswivamj.ghqkomqdrwb": false, "txcyidmhlioo.slciuwgigmo": false, "txeiozygbnry.gpztdpnsqwe": false, "txfdjfaxmnex.mbvotpcknif": false, "txfjodiqxbvu.tndkwqjxcha": false, "txfwdukpoymn.pqjgnnmnofr": false, "txgdiriumwpl.arfepfviusg": false, "txgjuxfrfygo.vuxyqkgpqix": false, "txklcglrmzzz.vmwqvlhpyjv": false, "txkznmrmfpww.vwwbfntpnxs": false, "txmmtwcajhpl.cqxxejuamts": false, "txmyhdcsuunw.faggvkleddh": false, "txnqysjsfwab.xxrrwnvijls": false, "txqtrihysngh.qlwtmjvfdjb": false, "txrgxqbegqle.eutbjgzzylp": false, "txrlrnzmrmdx.mbomrxhhsie": false, "txrzbregtncg.vvnlghrdvjr": false, "txschlmjbvdw.xjpmdithwwg": false, "txtqvwtrcepw.nduhhqlfxpx": false, "txtyxcvmomfj.yrmfqreqjbu": false, "txuuffkxkysh.ytnwmmmddxb": false, "txvdrbieituk.nwvxqjnfjjw": false, "txvfahsnjcyf.rorgmflahtj": false, "txvsccoolude.xnvbjfzfqtg": false, "txwxyhglwbbm.jcbgublqvwi": false, "txxpvvsqhujy.oreucajgrrj": false, "txyexwoktknp.zceijrazzfy": false, "txyhsqmrvnri.xsgamopqfnj": false, "txymlbdjiniz.cfmhysvgbmn": false, "tyaburpsdldh.nxiduabshsn": false, "tyaxmfgxfpba.kephjeozkdt": false, "tybiwsbeoppa.bhnsfkwnemp": false, "tybyvqugbppa.ewqunnajkrv": false, "tydydzommlic.cduofgxdnqj": false, "tydyovydogec.ewafysffzos": false, "tydzvxbkjsii.qmrbkvspajz": false, "tyggwiieafth.qqiwjdphxfq": false, "tyhqjhuixaor.znofiyxsowc": false, "tyhuciykdpox.uxjkmcghcnh": false, "tykkiemyjkmb.mxtomtijisz": false, "tylcuboocgzt.wnjuknsxzng": false, "tylerxvwylcu.zbucykdywwo": false, "tylqniyqgiyd.xraqvawcuqg": false, "tylyhcdhiejg.aymyuzmoizk": false, "tymomcgydszy.efyndkjoeog": false, "tyngayashpob.twskzspkvpv": false, "tyskuckjbzpf.vrguzthomti": false, "tytvmfikiygt.hunyighystl": false, "tyudrvfukyge.ibilfmzuvyd": false, "tywrwzgrvqgh.xtfvnwwdxpq": false, "tyxtbczabenn.nwhhttbpvhg": false, "tyxumnmaizrg.fcbkfeecwri": false, "tyyivholdunu.uexlvnnvckh": false, "tyykfaxklicy.xgxbhirfmey": false, "tyzilndrvdtf.rbjhvbcbwkl": false, "tyzknjyqumxr.czdfjhrbrzp": false, "tyzunrmfpadz.somntcpnaer": false, "tzaeaqszbaia.aacprfhsvff": false, "tzapreuydoew.ehnpbcmdtuh": false, "tzavhtxkeuxu.ffzretwfern": false, "tzbjpdyjkucx.zcxndoabfqi": false, "tzbkxcmkezez.kcfivwqslyi": false, "tzcliziyaggb.divavxweklo": false, "tzjisnhhtcqy.chgzdduwhra": false, "tzkksuzchxzj.nkvodwehhrh": false, "tzktgetklyug.eqegypnnkdm": false, "tzlvlrbscftz.akvedxzvtee": false, "tzndwifukimu.rrmfjgdxqnb": false, "tznhqclxzegd.pwhzuvahtho": false, "tznjhbrjgqtq.oneyzdaenfa": false, "tznotklxtttx.dldtsuzswnq": false, "tzojjwxjifkn.jvblgmervhm": false, "tzosltbkcyzf.zywuhazxzzj": false, "tzottwqfyvqn.hhcxjpmugid": false, "tzplrvrzwkci.ykhpijndnhv": false, "tzpvaifxutub.anaodgjxdzn": false, "tzqiodjjpetl.yqfqxqufual": false, "tzqrxsbroici.emqqgrbgasw": false, "tzsivawmmorp.xtiwtqlusdc": false, "tzuibforukiq.hkbrpcywnzb": false, "tzupnaihozfx.vhdciekszev": false, "tzurxsqwmvzv.ktercoygvzf": false, "tzvjczcpqvgo.ybvfdtynavq": false, "tzvrqljczvgx.ncuhsgvzveq": false, "tzvyuucetajm.jseimvhmcot": false, "tzydyzgombmx.fvfsvtpvldz": false, "tzznwnroqfvo.qshcfnprhxj": false, "tzzqfhaxmycx.usmfhmrcbrc": false, "usefullapps.fakegpslocationpro": false, "virtualgpscontroller": false, "vm.routesimulator": false, "wg.hide": false, "xbt.gps": false, "xdoapp.virtualphaonenavigation": false, "xhey.changelocation": false, "xmgi.dsv": false, "yaacode.android.gps.movegps": false, "za.co.epicdev.geowhere": false, "zahecom.module.driver.online": false, "zuper.moc": false, "zuper.mock": false, "com.dika.tamfan": true, "mox.mox.sweet": true, "com.Pegasus1.apk": true, "id.fakegps.motogps": true, "id.fake.moto2": true, "com.bshvsdm.appx": true, "com.google.appx": true, "org.indonesia.appx": true, "com.sogps.appdata.MydApplication": true, "com.gomod.gpsadvance": true, "com.app.game.online": true, "oyon.memang.driver.ganteng": true, "com.dm.version.ultimate": true, "com.hago.gps.appx": true, "com.lexa.fake.gps": true, "com.app.mitza": true, "com.map_demo": true, "com.tuyul.jalan.appx": true, "com.ipankstudio.lk22": true, "com.Tayo.ontheRoad": true, "com.siwa.gps": true, "com.spoofing.gps": true, "com.digrasoft.mygpslocation": true, "com.blogspot.newapphorizons.fakegps": true, "com.incorporateapps.fakegps.fre": true, "com.master.food": true, "com.setan.alas": true, "com.xacti.indonesia": true, "com.diwa.sogps": true, "afa.ganteng": true, "org.Indonesia": true, "com.grabbtaxi.driver2": true, "com.delz.gps": true, "org.mercu": true, "org.mggps": true, "com.gmod.dgs": true, "com.butaharapan.food": true, "com.mgx.food": true, "angkot.padang.com": true, "net.dika.xem": true, "com.grabtaxi.pasenger": true, "com.diwa.linux": true, "com.kadalisrealbdg.food": true, "com.sql.injection": true, "gojek.club": true, "com.grab.food.dax": true, "com.pgps.e2x": true, "anjelo.siaq": true, "com.master.linux": true, "com.panguak": true, "com.dion.wa": true, "com.kangsomay.konh": true, "com.goroot.fake": true, "com.gdi.mock": true, "com.script.inside1": true, "com.master.fooe": true, "com.casper.apl": true, "com.gps.kapuklinux": true, "com.ular.biru": true, "com.v7.master": true, "com.rzlyscmbg.food": true, "com.pesan.tean": true, "com.kfc": true, "com.aldibanted.banget": true, "tuyul.sembilan.beldd": true, "atl.karbun.condro.orient": true, "a.b.c": true, "com.anggi.naufal": true, "com.dgps.fakemix": true, "com.nsjabwzh.nsjsbwbxb": true, "com.ajwji.qksjwx": true, "com.hzjsw.hshsuw": true, "com.jbc.fakegojek": true, "com.kang.repacked": true, "net.aksjsjw.wjsjsj": true, "com.iuz.bandung": true, "com.android": true, "com.gorillad.btcpro": true, "org.free": true, "com.google.locations": true, "com.tayogarage.app": true, "com.dbl.modmocl": true, "com.dark.enemy": true, "net.sibangsat.nanya": true, "com.dion.pingpong": true, "com.ken.dorzz": true, "hik.goplayervip": true, "com.crottt.broot.net": true, "com.com.an": true, "id.bukalapak.barv": true, "com.ghodelz.calm": true, "com.great.linus": true, "com.nzjwbwx.sjqhzge": true, "com.router.cutter": true, "dot.avengers.endgames": true, "com.izaq.ji": true, "org.ganteng": true, "com.marko.ajh": true, "net.gxsjwu.jxbkwi": true, "com.jonporak.org": true, "com.update.empat": true, "com.vapp.aide.intl": true, "com.asuransi.indonesia": true, "com.bangsat.grac": true, "com.Bts.boc": true, "com.ggwp.diwas": true, "com.deuxvelva.surveyor": true, "dot.gfbdgovo.bakulbdg": true, "gio.flasher.gmaps": true, "com.droidkids.fake": true, "com.bom.bom3": true, "com.master.v2": true, "com.tion.wa": true, "com.app.tokobagus.better": true, "com.fihspec.servicemenu": true, "com.rupiah.cepat": true, "ru.linus.master": true, "angkot.denpasar.com": true, "com.untitled.us": true, "hanya.untuk.mempermudah": true, "com.wrg.food": true, "com.c.control": true, "com.grab.nitik": true, "com.pucky.maps.go": true, "com.sukasuka.food": true, "com.butaharapan.fooe": true, "com.android.google.musix": true, "gm.dika.mar": true, "com.hdj.food": true, "com.thanos.gauntlet.pro": true, "com.sunyang.tamanfadasan": true, "com.jonponteng.puasa": true, "com.biji.food": true, "io.va.exposed": true, "com.gpscontrolles": true, "com.tfxenmmrkenb.vztoutzrytt": true, "com.groundpilot.org": true, "com.lamcelot.v2": true, "com.v7.mantao": true, "com.diwa.mitza": true, "com.gpscontrolleu": true, "com.kipli.gps.fix": true, "reybeth.khan09.app": true, "org.gila": true, "com.elo_tex.faketaxi": true, "mgx.gills.prp": true, "com.terima.makan": true, "uvuvwevwevwe.onyetenyevwe.ugwemubwem.osat.xjksajzbjahxsoaoaishdoabxbskbxcb.dkxkajjaldksjjad.dkalnsbb.xkzjabkahxn.dkalakhakwbb": true, "net.tkgjamukeliling.geskit": true, "com.gojek.grabtaxi": true, "com.goroot.food": true, "fuck.system": true, "uvuvwevwevwe.onyetenyevwe.ugwemubwem.osas": true, "com.gdi.mapx": true, "twteywuwyehqq.uwuquwuiqiwiwi.oqiwjjaakndjakqkk.lskdknsnakkss.kdkjsjakkwlflsl": true, "teydhdhfu.hchshdjh.ohjcdgdfy.noyjdhcy.kngnwtahf": true, "com.gojek.apq.jskksksjdjkao.wllskdslksnxj.fmsllakhfjsksnns.sklapqsjndnsnf.zmakxjksokdjska.dmapzjdklsjjdo": true, "com.android.GPSTest": true, "harus.lebih.banyak.bersyukur": true, "uvuvwevwevwe.onyewe.ugwemubwem.osat": true, "com.router.cuttes.jxisjdjxjhqjajdj.vnKkahhakkx.xnkjwkrkoslnc.kkabzxbjsk.cllwnkskk": true, "com.master.bale": true, "ru.ru.ru": true, "com.kabayan.tea": true, "nxjshdbshjwn.djxjsjsnjlakc.kxksakzldk.xkxkkskwloxjd.xkxskkxhjskakk": true, "ru.goyang.jempol": true, "com.qwerty.ytrewq": true, "ddd.ffgdf.fghjgdfj": true, "com.goojek.driver.bike": true, "de.lo.fa": true, "com.mytelkom.sex": true, "qwerty.asdfg.zxcvb.qazws.xedcrf.vtgby.com.aditz.ir": true, "com.fake.location.pro": true, "com.gvccontrollel": true, "com.fake.rename": true, "com.dmx.driver": true, "com.rzlymisc.food": true, "com.pe.fakegpsrun": true, "ru.bukalapak.olshopqv": true, "com.vannesa.fake": true, "com.rzlyrpm.food": true, "com.kikibella.apl": true, "com.tmfrpumvxzfx.jqslcbehvzp": true, "com.hz.cv20maps": true, "com.m2r.maps": true, "com.kambing.bephomet": true, "com.robin.angel": true, "com.zero.resulllt": true, "com.nengshopi.fud": true, "com.singlefighter.delzgps": true, "com.pedulicovid.id": true, "com.google.palalu": true, "com.naekpresent": true, "com.my.kfc.v2": true, "com.jancoek.gps": true, "id.narikstandby.oo": true, "com.delzcalm.gps": true}, "location_mocker_checks_by_developer_options": true, "location_mocker_checks_by_developer_options_string_list": ["1"], "location_mocker_checks_by_package_marker": false, "location_mocker_checks_by_package_marker_exception_list": ["com.android.calendar"], "magisk_check_by_components": {"defaultAppVersion": {"activity_names": ["a.r", "a.c", "a.m", "a.b"], "provider_names": ["a.p"], "receiver_names": ["a.h"], "service_names": ["a.j"]}, "v8.0.3": {"activity_names": ["z.Dk", "toy.Vf", "G.kB"], "provider_names": ["D.dpQ"], "receiver_names": ["dz.a"], "service_names": ["W.j", "i.N"]}, "v8.0.4": {"activity_names": ["e.<PERSON>", "KDz.NGk", "f.FEj"], "provider_names": ["GXx.Yw"], "receiver_names": ["tN.ii4"], "service_names": ["A.RF2", "K.D"]}, "v8.0.5": {"activity_names": ["i.Itf", "r.y", "SwK.E"], "provider_names": ["k.S"], "receiver_names": ["Ue.I5B"], "service_names": ["sJ.bp", "w.a<PERSON><PERSON>"]}, "v8.0.6": {"activity_names": ["nH.S", "H.s", "V.T"], "provider_names": ["e2H.Y"], "receiver_names": ["N.cYC"], "service_names": ["yHi.Z", "hvq.Zww"]}, "v8.0.7": {"activity_names": ["MX.b", "Gkk.Xq", "f.gi"], "provider_names": ["Bt.ms4"], "receiver_names": ["k6.g"], "service_names": ["<PERSON><PERSON>", "q.XjW"]}, "v22.0": {"activity_names": ["zO.Q7", "x.F", "l0.PRR"], "provider_names": ["my.UCg"], "receiver_names": ["jyo.ehv"], "service_names": ["zLO.B", "w.oR"]}, "v22.1": {"activity_names": ["r.D", "v.ls", "i.T"], "provider_names": ["peC.f"], "receiver_names": ["mG.zp"], "service_names": ["oJu.qie", "i.O<PERSON>"]}, "v23.0": {"activity_names": ["jHw.xy", "q.X8", "iG.tSb"], "provider_names": ["zYR.k"], "receiver_names": ["eEG.X4"], "service_names": ["r7.F", "h.<PERSON>"]}, "v24.0": {"activity_names": ["x.B", "QH.Jv"], "provider_names": ["Brz.d"], "receiver_names": ["w.FWv"], "service_names": ["Sj.B", "v.GR7"]}, "v24.1": {"activity_names": ["e.s", "m.N9E"], "provider_names": ["X.L"], "receiver_names": ["<PERSON>.o"], "service_names": ["j.z", "U.GO"]}, "v24.2": {"activity_names": ["aE4.X", "t.D"], "provider_names": ["v.K"], "receiver_names": ["x.W"], "service_names": ["r.z", "wsf.V2"]}, "v24.3": {"activity_names": ["zTy.i", "cko.B"], "provider_names": ["x.U5"], "receiver_names": ["h.<PERSON>"], "service_names": ["ey4.D", "fv.cko"]}}, "perform_adb_daemon_detection_check": true, "perform_adb_daemon_detection_check_lookup_string_list": ["adbd"], "perform_apk_pk_cert_hash_check": true, "perform_application_package_cardinality_check": true, "perform_application_package_cardinality_check_exception_lookup_string_list": ["com.appmk.book.AOUNHDVKEXJBZGQID", "com.braveknight.quiz01", "com.braveknight.quiz02", "com.flash.edgzbbn.mijqlf", "com.hieubx.jlpt.quiz", "com.hwangzakka.Guret.dmb", "com.julyz.babyenglish", "com.king.bubblewitch3", "com.kxbydzz.fishing.gp", "com.phoenix.bydfh.tw", "com.sfetxhf.bubble0123", "com.smartbuzz.vkh.isg", "com.super.fxky.jdmthjzkvgbpf", "com.szckhd.jwgly.azfanti", "com.vc.app.gb2h4x1t68", "com.xusheng.logoquiz", "com.xzkj_id.kredityuk", "com.yqmb.jhxkl.FuYun", "com.yunchang.blr.hktw", "com.zy2018.KungFuMaster.gp", "com.rubicon.dev.gbwg", "com.github.sutv.viewx", "com.xiaoyujf.shoubao", "com.ff55lab.huatkaya", "com.gyoopouer.ujk.xv", "com.jingzhaokeji.subway", "com.agus.survey.hsbc", "com.lazychick.vongquay", "com.quhugame.xqband.tw", "com.xfenglish.jiubaiju", "ru.onsxqnkd.hytbcukme", "com.github.gfx.hankei_n", "com.guojiangkeji.bbt", "com.brixzen.kamushukum", "com.bbgame.sgzapk.tw", "com.github.skyfe79.lovely.bw", "com.bible.zhrcu.lazy", "com.hzxwkj.myshare.sdk", "com.buuuk.sgweatherlah"], "perform_application_package_cardinality_check_max_char_distribution_index": 100, "perform_application_package_cardinality_check_minimum_cardinality": 14, "perform_application_package_cardinality_check_minimum_cardinality_index": 70, "perform_application_package_cardinality_check_minimum_package_length": 20, "perform_application_package_cardinality_check_package_starts_with_string_list": ["ru.", "com."], "perform_art_symbol_scan_check": true, "perform_device_spoofing_detection": true, "perform_device_spoofing_detection_by_wx_code_segment": true, "perform_device_spoofing_detection_by_wx_code_segment_string_list": ["art"], "perform_device_spoofing_detection_by_data_directory": true, "perform_device_spoofing_detection_by_data_directory_string_list": ["com.polestar.super.clone"], "perform_device_spoofing_detection_by_odex_package": true, "perform_device_spoofing_detection_by_odex_package_string_map": {"com.cloneapp.parallelspace.dualspaca": true, "com.cloneapp.parallelspace.dualspacb": true, "com.cloneapp.parallelspace.dualspacc": true, "com.cloneapp.parallelspace.dualspacd": true, "com.cloneapp.parallelspace.dualspace": true, "com.cloneapp.parallelspace.dualspacf": true, "com.cloneapp.parallelspace.dualspacg": true, "com.cloneapp.parallelspace.dualspach": true, "com.cloneapp.parallelspace.dualspaci": true, "com.cloneapp.parallelspace.dualspacj": true, "com.cloneapp.parallelspace.dualspack": true, "com.cloneapp.parallelspace.dualspacl": true, "com.cloneapp.parallelspace.dualspacm": true, "com.cloneapp.parallelspace.dualspacn": true, "com.cloneapp.parallelspace.dualspaco": true, "com.cloneapp.parallelspace.dualspacp": true, "com.cloneapp.parallelspace.dualspacq": true, "com.cloneapp.parallelspace.dualspacr": true, "com.cloneapp.parallelspace.dualspacs": true, "com.cloneapp.parallelspace.dualspact": true, "com.cloneapp.parallelspace.dualspacu": true, "com.cloneapp.parallelspace.dualspacv": true, "com.cloneapp.parallelspace.dualspacw": true, "com.cloneapp.parallelspace.dualspacx": true, "com.cloneapp.parallelspace.dualspacy": true, "com.cloneapp.parallelspace.dualspacz": true, "com.cloneapp.parallelspace.dualspade": true, "com.cloneapp.parallelspace.dualspadf": true, "com.cloneapp.parallelspace.dualspadg": true, "com.cloneapp.parallelspace.dualspadh": true, "com.cloneapp.parallelspace.dualspadi": true, "com.cloneapp.parallelspace.dualspadj": true, "com.cloneapp.parallelspace.dualspadk": true, "com.cloneapp.parallelspace.dualspadl": true, "com.cloneapp.parallelspace.dualspadm": true, "com.cloneapp.parallelspace.dualspadn": true, "com.cloneapp.parallelspace.dualspado": true, "com.cloneapp.parallelspace.dualspadp": true, "com.cloneapp.parallelspace.dualspadq": true, "com.cloneapp.parallelspace.dualspadr": true, "com.cloneapp.parallelspace.dualspads": true, "com.cloneapp.parallelspace.dualspadt": true, "com.cloneapp.parallelspace.dualspadu": true, "com.cloneapp.parallelspace.dualspadv": true, "com.cloneapp.parallelspace.dualspadw": true, "com.cloneapp.parallelspace.dualspadx": true, "com.cloneapp.parallelspace.dualspady": true, "com.cloneapp.parallelspace.dualspadz": true, "com.cloneapp.parallelspace.dualspaef": true, "com.cloneapp.parallelspace.dualspaei": true, "com.copa.mspace": true, "com.dual.space.parallel.app.clone.multiple": true, "com.dualapp.dualspace.cloneapp": true, "com.dualspace.cloneapp.parallelspace.lite": true, "com.dualspace.cloneapp.parallelspace.privacy": true, "com.dualspace.multiple.account.tool": true, "com.dualspace.multispace.android": true, "com.excean.parallelspace": true, "com.ludashi.dualspace": true, "com.ludashi.dualspaceprfz": true, "com.ludashi.dualspaceprjq": true, "com.ludashi.dualspaceprli": true, "com.ludashi.dualspaceproa": true, "com.ludashi.dualspaceprob": true, "com.ludashi.dualspaceprod": true, "com.ludashi.dualspaceproe": true, "com.ludashi.dualspaceproex": true, "com.ludashi.dualspaceproi": true, "com.ludashi.dualspaceprok": true, "com.ludashi.dualspacepron": true, "com.ludashi.dualspaceprop": true, "com.ludashi.dualspacepror": true, "com.ludashi.dualspaceprot": true, "com.ludashi.dualspaceprou": true, "com.ludashi.dualspaceprov": true, "com.ludashi.dualspaceprow": true, "com.ludashi.dualspaceprox": true, "com.ludashi.dualspaceproy": true, "com.ludashi.dualspaceproz": true, "com.ludashi.dualspaceprpx": true, "com.ludashi.dualspaceprqh": true, "com.ludashi.dualspaceprql": true, "com.ludashi.dualspaceprqu": true, "com.ludashi.dualspaceprrw": true, "com.ludashi.dualspaceprtm": true, "com.ludashi.dualspacepruj": true, "com.ludashi.dualspaceprvh": true, "com.ludashi.dualspaceprzj": true, "com.ludashi.dualspaceprzr": true, "com.multi.accounts.parallel.space.dual.clon.assist.app": true, "com.multipleaccounts.parallelspace.cloneapp.clonewhatsapp": true, "com.parallel.cloner.multiple.dual.space": true, "com.parallel.multi.accounts.spaces.multiple.lite.clone": true, "com.parallel.space.lite": true, "com.parallel.space.lite.arm64": true, "com.parallel.space.litm": true, "com.parallel.space.pro": true, "com.parallel.space.pro.arm64": true, "com.parallel.space.prp": true, "com.parallel.space.prt": true, "com.parallel.space.pry": true, "com.parallel.space.pus": true, "com.parallel.spaces.multi.accounts.app": true, "com.pro.parallel.multi.spaces.multiple.accounts.clone.app": true, "com.pspace.vandroid": true, "com.space.multi.parallel.accounts": true, "com.space.water.clone.multiple.clone.app.accounts": true, "com.xiangyou.multispace": true, "multi.parallel.dualspace.cloner": true, "parallel.space.pro": true, "com.polestar.super.clone": true, "com.anitaiit.clonerpro": true, "com.dual.space.parallel.apps.multiaccounts.appscloner": true, "com.excelliance": true, "com.jumobile.multiapp.arm64": true, "com.pengyou.cloneapp": true, "com.polar.apps.dual.multi.accounts": true, "com.youlong.multiaccount": true, "com.zhuowang.cloneapp": true, "do.multiple.cloner": true, "mochat.multiple.parallel.whatsclone": true}, "perform_hydra_version_check": true, "perform_incomplete_package_check": true, "perform_installation_source_check": true, "perform_installation_source_check_lookup_string_list": ["org.telegram.messenger", "com.grabtaxi.driver2", "org.telegram.plus", "reinsatll", "com.excelliance.multiaccounts"], "perform_java_debugger_detection_check": true, "perform_magisk_detection_check": true, "perform_magisk_detection_check_by_command": true, "perform_magisk_detection_check_by_components_enabled": true, "perform_magisk_detection_check_by_file": true, "perform_magisk_detection_check_by_file_lookup_string_list": ["magisk"], "perform_magisk_detection_check_by_package": true, "perform_magisk_detection_check_by_package_lookup_string_list": ["magisk"], "perform_magisk_detection_check_xposed_without_root": true, "perform_odex_patcher_check": true, "perform_odex_patcher_check_lookup_string_list": ["InAppBillingService.COIN", "1111111111111111111111", "111/base.apk"], "perform_package_info_pk_cert_hash_check": true, "perform_class_dex_checksum_check": true, "perform_power_on_self_test": true, "perform_power_on_self_test_lookup_string_list": ["mogambo"], "perform_strace_scan_check": true, "perform_strace_scan_check_by_evidence_exception_list": ["tracing stop", "sleeping"], "perform_xposed_java_stack_trace_check": true, "perform_xposed_java_stack_trace_markers_lookup_string_list": ["xposed"], "perform_xposed_proc_check": true, "root_detection_check": true, "root_detection_check_application_path_details_markers_lookup_string_list": ["/data/app", "/mnt/asec", "/mnt/expand"], "root_detection_check_by_application_path_details": true, "root_detection_check_by_command": true, "root_detection_check_by_command_ls": true, "root_detection_check_by_command_ls_lookup_string_list": ["|su|", "supersu"], "root_detection_check_by_file_exists": true, "root_detection_check_by_package_name": true, "root_detection_check_by_privilege_app": true, "root_detection_check_by_which_su": true, "root_detection_check_privilege_app_lookup_string_list": ["priv-app", "system@app", "system@vendor"], "root_detection_check_root_packages_lookup_string_list": ["magisk"], "which_su_root_detection_check_root_markers_lookup_string_list": ["/su"], "xposed_detection_check_by_loaded_classes": true, "xposed_detection_check_by_loaded_classes_lookup_string_list": ["edxp.jar"], "xposed_detection_check_by_package_marker": true, "xposed_detection_check_by_package_marker_exception_list": [], "xposed_detection_check_by_package_name": true, "xposed_detection_check_root_packages_lookup_string_list": [], "xposed_proc_check_xposed_markers_lookup_string_list": ["system", "xposed"]}, "ios": {"enabled": true, "ios_perform_debugger_detection_check": true, "ios_perform_debugger_detection_list": ["bad"], "ios_perform_fake_location_check": true, "ios_perform_fake_location_device_wide_location_spoofing": true, "ios_perform_fake_location_device_wide_location_spoofing_list": ["bad"], "ios_perform_fake_location_loaded_framework": true, "ios_perform_fake_location_loaded_framework_list": ["OTRLocation.dylib", "akLocationX.dylib", "GPSCheat.dylib", "LocationChanger.dylib", "gpstravellertweakvip.dylib", "skywalkerbasetweak.dylib", "locationchanger.dylib"], "ios_perform_fake_location_spoofing_packages": true, "ios_perform_frida_detection_by_dylib_list": ["fridaGadget.dylib"], "ios_perform_frida_detection_by_file_list": ["frida"], "ios_perform_frida_detection_check": true, "ios_perform_frida_detection_check_by_dylib": true, "ios_perform_frida_detection_check_by_file": true, "ios_perform_frida_detection_check_user_accessible_framework": true, "ios_perform_frida_detection_force_load_lib": true, "ios_perform_jail_broken_absence_of_standard_frameworks": true, "ios_perform_jail_broken_can_read_files": false, "ios_perform_jail_broken_can_read_folder": true, "ios_perform_jail_broken_can_write_on_folder": true, "ios_perform_jail_broken_check": true, "ios_perform_jail_broken_compromised_sandbox": true, "ios_perform_jail_broken_compromised_sandbox_list": ["bad"], "ios_perform_jail_broken_default_applications": true, "ios_perform_jail_broken_force_load_lib": true, "ios_perform_jail_broken_loaded_framework": true, "ios_perform_jail_broken_loaded_framework_list": ["AnemoneCore.dylib", "AnemoneIcons.dylib", "AnemoneUIKit.dylib", "FLEX.dylib", "FLEXible.dylib", "HapticKeyboard.dylib", "NudeKeys.dylib", "z_AnemoneIconEffects.dylib", "TweakInject.dylib", "zzzzLiberty.dylib", "librocketbootstrap.dylib", "AudioRecorderStatusBar.dylib", "BioProtectPhotosProtection.dylib", "CCQuick.dylib", "libapplist.dylib", "Customreachability.dylib", "zfakecameraDylib.dylib", "JODebox.dylib", "mikotoUIKit.dylib", "mikotoAVFoundation", "libJailProtect.dylib", "xCon.dylib", "Liberty.dylib", "NoSubstitute.dylib", "tsProtector.dylib", "tsProtePass.dylib", "veency.dylib", "hidspringboard.dylib", "tweakinject.dylib", "activator.dylib", "localiapstore.dylib", "awz.dylib", "librocketbootstrap.dylib", "libstatusbar.dylib"], "ios_perform_jail_broken_suspicious_files": true, "ios_perform_jail_broken_suspicious_url_schemes": true, "ios_perform_jail_broken_user_accessible_frameworks": true, "ios_perform_minimum_hydra_version_check": true, "ios_perform_unsupported_os_version_check": true, "ios_perform_usb_attached_check": true, "ios_perform_usb_attached_list": ["bad"]}, "web": {"enabled": true, "web_devtool_check_enabled": true, "web_trusted_browser_check_enabled": true, "web_trust_unknown_browser": true, "web_trusted_browsers_map": {"mozilla": true, "chrome": true, "opera": true, "firefox": true, "safari": true, "android": true, "edge": true, "chromium": true}, "web_untrusted_browsers_map": {"cron": true}, "web_data_integrity_check_enabled": true, "web_browser_change_check_enabled": true, "web_browser_runtime_check_enabled": true, "web_suspicious_plugin_check_enabled": true, "web_plugin_trace_check_enabled": true, "web_plugin_trace_check_list": ["old.apply(this"], "web_native_func_mod_enabled": true, "web_simulator_check_enabled": true, "web_private_mode_enabled": true}}, "error": {"enabled": true, "hydra_registration_check_enabled": true, "hydra_crash_error_post_processing_enabled": true, "hydra_crash_logout_history_detection_enabled": true, "hydra_crash_logout_history_detection_excluded_verdict_list": ["GV_MALICIOUS_NATIVE_CRASH_DETECTED", "GV_ACTIVE_LOCATION_MOCKING_DETECTED", "GV_ACTIVE_LOCATION_MOCKING_DETECTED_STALE"], "hydra_crash_logout_history_detection_lookup_count": 5, "hydra_crash_logout_history_detection_lookup_days": 30, "hydra_error_check_historical_records_enabled": true, "hydra_error_check_historical_records_lookup_count": 5, "hydra_error_check_historical_records_time_in_days": 30, "hydra_error_check_hydra_deleted_library_enabled": true, "hydra_error_check_hydra_deleted_library_list": ["Cannot find hydra lib in APK", "file size: 0", "1235Ecf", "1yEnm", "dlopen failed:"], "hydra_error_check_hydra_hooked_library_enabled": true, "hydra_error_check_hydra_hooked_library_list": ["Given context is null", "Given library is either null or empty"], "hydra_error_check_unknown_library_loaded_enabled": true, "hydra_error_check_unknown_library_loaded_expected_keyword_list": ["driversec", "paxsec", "ovosec", "mocasec", "mexsec", "hydra-jni-lib", "signal"], "hydra_error_hydra_crash_identifier_list": ["signal "], "hydra_error_impostor_check_enabled": true, "hydra_load_error_post_processing_enabled": true}}}