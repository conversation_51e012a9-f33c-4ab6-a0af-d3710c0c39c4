# oneportal deployment

The config json is located in `defence/templates/oneportal` folder now. 

you can use this way to upgrade the oneportal:
1. go to defence folder(under the helm/defence), if you are in this folder
```
cd ../..
```
2. use this command to upgrade the change. change the kubeconfig with your kubeconfig path, like removing --kubeconfig or add your kubeconfig.
```
 helm upgrade oneportal ./templates/oneportal/ -f ./nstg/oneportal/values.yaml --namespace defence --kubeconfig ~/.kube-nstg/config
```
or for prod:
```
 helm upgrade oneportal ./templates/oneportal/ -f ./nprd-sg/oneportal/values.yaml --namespace defence --kubeconfig ~/.kube-nprd/config
```
