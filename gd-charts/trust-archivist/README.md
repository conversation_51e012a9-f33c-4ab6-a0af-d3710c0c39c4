# helm

helm charts for archivist tech stack

Helm for Service Initial Deployment:
1. Install helm with the kube config file from https://gdkube.gd.stg-myteksi.com/

2. Pull the helm config from https://gitlab.myteksi.net/grabdefence/helm and locate to defence/prd-sg *** Make sure to update the image tag from the values.yaml

3. Try dry run first locally to check if there are any errors:
```
 helm upgrade archivist --dry-run ./templates/archivist/ -f ./nstg/archivist/values.yaml --namespace defence --kubeconfig ~/.kube-nstg/config
 helm upgrade archivist --dry-run ./templates/archivist/ -f ./nprd-sg/archivist/values.yaml --namespace defence --kubeconfig ~/.kube-nprd/config
```

## how to deployment
you can use this way to upgrade the service:
1. go to defence folder(under the helm/defence), if you are in this folder
```
cd ../..
```
2. use this command to upgrade the change. change the kubeconfig with your kubeconfig path, like removing --kubeconfig or add your kubeconfig.
```
 helm upgrade archivist ./templates/archivist/ -f ./nstg/archivist/values.yaml --namespace defence --kubeconfig ~/.kube-nstg/config
```
or for prod:
```
 helm upgrade archivist ./templates/archivist/ -f ./nprd-sg/archivist/values.yaml --namespace defence --kubeconfig ~/.kube-nprd/config
```
