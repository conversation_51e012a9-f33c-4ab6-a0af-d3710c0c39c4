# Default values for archivist.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/defence/archivist
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v_d5e318f4"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

env: "stg"
### client list
clientList:
  - guardian
  - second-client
  - grabgifts

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "defence-read"

podAnnotations:

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

configSecret: eyJhcmNoaXZpc3QtbXlzcWwiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKG5zdGctYXJjaGl2aXN0LW15c3FsLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL2FyY2hpdmlzdD9wYXJzZVRpbWU9dHJ1ZSZsb2M9VVRDIiwiYXJjaGl2aXN0LW15c3FsLXJlcGxpY2EiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKG5zdGctYXJjaGl2aXN0LW15c3FsLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL2FyY2hpdmlzdD9wYXJzZVRpbWU9dHJ1ZSZsb2M9VVRDIgp9

service:
  type: NodePort
  port: 8081

ingress:
  enabled: false
  # annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  # hosts:
  #   - host: chart-example.local
  #     paths: []
  # tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 8
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector:
  stack: general

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    - labelSelector:
        matchExpressions:
        - key: app
          operator: In
          values:
          - archivist
      topologyKey: "kubernetes.io/hostname"
