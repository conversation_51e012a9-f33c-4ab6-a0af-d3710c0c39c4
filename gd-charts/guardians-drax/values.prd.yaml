# Default values for drax.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

image:
  repository: ************.dkr.ecr.ap-southeast-3.amazonaws.com/new/guardians/drax
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v-**********-3ffca9ba"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

env: "prd-id"

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "guardians-read"

podAnnotations:
  co.elastic.logs/json.add_error_key: "true"
  co.elastic.logs/json.keys_under_root: "true"
  co.elastic.logs/json.message_key: message
  co.elastic.logs/json.overwrite_keys: "true"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 8088

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: 2
    memory: 3Gi
  requests:
    cpu: 1
    memory: 2Gi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 60
  targetCPUUtilizationPercentage: 60
  targetMemoryUtilizationPercentage: 80

nodeSelector:
  stack: guardians

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    - labelSelector:
        matchExpressions:
        - key: app
          operator: In
          values:
          - drax
      topologyKey: "kubernetes.io/hostname"
