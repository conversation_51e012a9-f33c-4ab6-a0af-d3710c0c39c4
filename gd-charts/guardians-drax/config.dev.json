{"name": "Drax Service", "serviceID": "drax", "configVersion": "v1.0.0.drax", "mode": "production", "logger": {"level": 3, "tag": "drax", "yallMode": true, "callerEnabled": true, "callerSkip": 1}, "statsd": {"host": "localhost", "port": 8125, "appName": "drax"}, "grabkit": {"features": ["tracelog", "newstats", "structuredlogs"], "maxrecvmsgsize": 4194304}, "structuredlogger": {"syslogTag": "structuredlog.drax", "workerCount": 10, "bufferSize": 10000, "logLevel": 2, "stacktraceLevel": 4, "logFormat": "cglsdebug", "development": false}, "storageConfig": {"redisCluster": {"circuitBreakerErrorPercent": 80, "circuitBreakerMaxConcurrentRequest": 1000000, "circuitBreakerMaxQueueSize": 20, "circuitBreakerMaxVolPercentThreshold": 20, "circuitBreakerSleepWindowMS": 5000, "circuitBreakerTimeoutMs": 7000, "idleTimeoutMs": 30000, "masterHost": "guardian-drax-redis.fintrust.dev.g-bank.app", "maxActive": 1000, "name": "drax-redis-cluster", "port": 6379, "readFromMasterOnly": true, "readFromSlaveOnly": false, "readTimeoutMs": 5000, "writeTimeoutMs": 5000}}, "streamWriter": {"GVStream": {"writerEnabled": false, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "writerConfig": {"brokers": ["prd-gd-singapore.coban-partner:30100"], "clientID": "drax-gvstream-producer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "shortName": "gvstream", "stream": "gvstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "drax", "packageName": "streams.guardians.gvstream", "dtoName": "GuardianVerdict"}}, "GOStream": {"writerEnabled": false, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "writerConfig": {"brokers": ["prd-gd-singapore.coban-partner:30100"], "clientID": "drax-gostream-producer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "shortName": "gostream", "stream": "gostream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "drax", "packageName": "streams.guardians.gostream", "dtoName": "GuardianOutput"}}, "RGDStream": {"writerEnabled": false, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "writerConfig": {"brokers": ["prd-gd-singapore.coban-partner:30100"], "clientID": "drax-rgdstream-producer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "shortName": "rgdstream", "stream": "rgdstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "drax", "packageName": "streams.guardians.rgdstream", "dtoName": "RawGuardianDataStream"}}}, "streamReader": {"SPVStream": {"readerEnabled": false, "maxWorkerCount": 5, "staleRecordTimeSec": 600, "batchSize": 2, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "readerConfig": {"brokers": ["prd-gd-singapore.coban-partner:30100"], "clientID": "drax-spvstream-consumer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "shortName": "spvstream", "stream": "spvstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "drax", "packageName": "streams.guardians.spvstream", "dtoName": "ShieldxPrimaryVerdict", "consumerGroupID": "drax-spvstream-consumer", "offsetType": "oldest", "consumerVersion": "v2"}}, "SPOStream": {"readerEnabled": false, "maxWorkerCount": 5, "staleRecordTimeSec": 600, "batchSize": 2, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "readerConfig": {"brokers": ["prd-gd-singapore.coban-partner:30100"], "clientID": "drax-spostream-consumer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "shortName": "spostream", "stream": "spostream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "drax", "packageName": "streams.guardians.spostream", "dtoName": "ShieldxPrimaryOutput", "consumerGroupID": "drax-spostream-consumer", "offsetType": "oldest", "consumerVersion": "v2"}}}, "verdictControl": {"verdictFilterRules": {}}, "rateLimit": {"defaultEntityTypeRateLimiterEnabled": true, "defaultEntityTypeRateLimiterThreshold": 50, "defaultEntityTypeRateLimiterWindowSecs": 60, "useHybridRateLimiter": false, "rateLimitRules": {}}, "rawDataAccess": {"sss": {"bucketFileStructure": "tenant", "presignedUrlTTLMins": 10080, "streamname": "rgdstream", "tenantsBucketsConfig": {"alpha": {"bucket": "nprd-datapipeline-alpha-s3", "region": "ap-southeast-1"}}, "tenantsDataBucketsConfig": {"alpha": [{"bucket": "nprd-datapipeline-alpha-s3", "region": "ap-southeast-1", "keyFormat": "tenant", "streamname": "rgdstream", "appID": "hTy0Q4bzo1", "entityType": "ET_ALPHA_DHANI"}, {"bucket": "nprd-datapipeline-alpha-s3", "region": "ap-southeast-1", "keyFormat": "tenant_new", "streamname": "rgdstream", "appID": "hTy0Q4bzo1", "entityType": "ET_ALPHA_DHANI"}]}}}, "rgdStreamControl": {"enabled": true, "allowedListOfEntityType": ["ET_ALPHA_DHANI"]}}