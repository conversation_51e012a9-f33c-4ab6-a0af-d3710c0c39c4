configName: config.dev.json
image:
  tag: v-1674726231-928c15c7

env: "dev"

resources:
  limits:
    cpu: 150m
    memory: 192Mi
  requests:
    cpu: 150m
    memory: 192Mi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 30
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

podAnnotations:
  vault.hashicorp.com/agent-inject: "true"
  vault.hashicorp.com/agent-init-first: "true"
  vault.hashicorp.com/agent-cache-enable: "true"
  vault.hashicorp.com/role: "guardians-read"
  vault.hashicorp.com/agent-inject-secret-redis-creds.sh: "kv/data/security/tf/dev/guardians-drax/ec/auth"
  vault.hashicorp.com/agent-inject-template-redis-creds.sh: |
    {{ with secret "kv/data/security/tf/dev/guardians-drax/ec/auth" -}}
      export REDIS_AUTH={{ .Data.auth_token }}
    {{- end }}
  # https://gxstemp-enterprise.slack.com/archives/C01QDJBFWG6/p1663306859755089?thread_ts=1663234835.397639&cid=C01QDJBFWG6
  # vault.hashicorp.com/agent-inject-secret-service-creds.sh: "kv/data/security/tf/dev/guardians-drax/ec/auth"
  # vault.hashicorp.com/agent-inject-template-service-creds.sh: |
  #   {{ with secret "kv/data/security/tf/dev/guardians-drax/ec/auth" -}}
  #     export API_GATEWAY_CREDS="{{ .Data.auth_token }}"
  #   {{- end }} 
