apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "drax.fullname" . }}
  labels:
    {{- include "drax.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
      {{- include "drax.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "drax.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "drax.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      terminationGracePeriodSeconds: 30
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: BINARY_TAG
            value: {{ .Values.image.tag | default .Chart.AppVersion }}
          - name: DRAX_CONFIG
            value: /code/abuse/drax/config.json
          - name: DRAX_CONFIG_SECRET
            value: /vault/secrets/config-secret.json
          - name: DOGSTATSD_ADDR
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          envFrom:
          - configMapRef:
              name: drax-env
          volumeMounts:
            - name: config
              mountPath: /code/abuse/drax/config.json
              subPath: config.json
          ports:
            - name: http
              containerPort: 8088
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health_check
              port: 8088
          readinessProbe:
            httpGet:
              path: /health_check
              port: 8088
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
        - name: config
          configMap:
            name: drax-configmap
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
