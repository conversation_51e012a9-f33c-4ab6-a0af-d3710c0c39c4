{"app_config": {"mode": "staging", "structured_logger": {"syslogTag": "structuredlog.graph-service", "workerCount": 4, "bufferSize": 1000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "cgls", "development": true}, "statsd": {"host": "", "port": 8125, "appName": "graph-service.grab"}, "dynamo_db_config": {"region": "ap-southeast-1", "table": "stg-graph-service-ddb-placeholder", "maxRetryAttempt": 3, "backoffBaseDelay": 50, "backoffMaxDelay": 200, "tag": "graph_service.grab.ddb", "timeoutMs": 2000, "errorPercent": 50, "maxConcurrentRequest": 8000, "maxVolPercentThreshold": 20, "sleepWindowMs": 5000, "maxQueueSize": 6000, "commandGroup": "graph_service.grab.ddb.cmd"}, "gremlin_http_config": {"client_config": {"prefix": "/gremlin"}, "hystric_config": {"timeoutMs": 5000, "maxConcurrentRequest": 500, "errorPercent": 75}, "retry_config": {"enabled": true, "max_retry_attempt": 3, "backoff_base_delay_ms": 50, "backoff_max_delay_ms": 200}, "skip_http_client_log": false, "tags": ["client:graph-service.grab"]}, "global_table_schema_config": {"tableName": "nstg-graph-service-global-meta"}, "graph_service_metadata_config": {"interval": 60}, "pc_stream_reader_config": {"disable": false, "broker": ["kafka.stg-myteksi.com:9092"], "groupID": "nstg-consumer-app-graph-service", "topic": "stg-paysi-cashout", "appName": "gd.grab.graph-service", "dtoName": "PaysiCashout", "packageName": "streams.grab_pay.pcstream", "offset": "newest", "workers": 6}, "pt_stream_reader_config": {"disable": false, "broker": ["kafka.stg-myteksi.com:9092"], "groupID": "nstg-consumer-app-graph-service", "topic": "stg-paysi-transaction", "appName": "gd.grab.graph-service", "dtoName": "PaysiTransaction", "packageName": "streams.grab_pay.ptstream", "offset": "newest", "workers": 8}, "spo_stream_reader_config": {"disable": false, "broker": ["kafka-ins-store.stg-myteksi.com:9092"], "groupID": "nstg-consumer-app-graph-service", "topic": "stg-shield-primary-output", "appName": "gd.grab.graph-service", "dtoName": "ShieldPrimaryOutput", "packageName": "streams.security.spostream", "offset": "newest", "workers": 2}, "stream_graph_config": {"refresh_interval": 60, "stream_config_table_name": "nstg-graph-service-stream-graph-configs", "stream_config_scripts_table_name": "nstg-graph-service-stream-graph-config-scripts"}}, "dynamic_config": {"neptune.client.health_check.rollout": 100, "graph_service.resource.cache.rollout": 100, "stream_graph.config.cron.rollout": 0, "streams.pcstream.init.rollout": 100, "streams.pcstream.process.rollout": 100, "streams.pcstream.graphs": "gfg", "streams.ptstream.init.rollout": 100, "streams.ptstream.process.rollout": 100, "streams.ptstream.graphs": "gfg", "streams.spostream.init.rollout": 0, "streams.spostream.process.rollout": 0, "streams.spostream.graphs": "gfg"}}