# Default values for graph-service in graph-engineering-grab namespace.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/defence/graph-service
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v_944344db"

nodeSelector:
  stack: graph-engineering-grab

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "graph-user-read"

# empty secret json file, to add in the future
secretPlaceholder: e30=

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 2000m
    memory: 3Gi
  requests:
    cpu: 1000m
    memory: 2Gi

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: stack
              operator: In
              values:
                - graph-engineering-grab

ingress:
  enabled: true
  ingressClassName: internal-nginx
    #  annotations: {}
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    - graph-grab.stg.gdefence.io
  path: /
  labels: {}
  # traffic-type: general
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local