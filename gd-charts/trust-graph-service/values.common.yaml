# Default values for graph-service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "defence-read"

podAnnotations:
  # ES logging
  co.elastic.logs/json.add_error_key: "true"
  co.elastic.logs/json.keys_under_root: "true"
  co.elastic.logs/json.message_key: message
  co.elastic.logs/json.overwrite_keys: "true"
  # https://www.elastic.co/guide/en/beats/filebeat/current/multiline-examples.html
  co.elastic.logs/multiline.pattern: '^(([a-zA-z\-_0-9]+_(INFO|WARN|ERROR|DEBUG|FATAL), RequestID: )|([^\[]{1,6}((\[32m)|(\[31m)|(\[31;1m))(\d{2}:\d{2})\s+(INFO|WARN|ERROR|DEBUG|DPANIC|PANIC|FATAL)\s+)|(\d{4}[\/-]\d{2}[\/-]\d{1,2})|(panic:))'
  co.elastic.logs/multiline.negate: "true"
  co.elastic.logs/multiline.match: "after"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 8081

ingress:
  enabled: false
  #  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    # hosts:
    # - host: chart-example.local
    # paths: []
    #tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80

tolerations: []

affinity: {}
