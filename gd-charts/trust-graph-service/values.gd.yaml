# Default values for graph-service in graph-engineering-grab namespace.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/defence/graph-service
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v_944344db"

nodeSelector:
  stack: graph-engineering-gd

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "graph-user-read"

configSecretGiestream: eyJnaWVzdHJlYW0tdXNlciI6ICJMVUQ0QlVHWkY2SktPWTZCIiwiZ2llc3RyZWFtLXBhc3N3b3JkIjogIlc2eDRsME94Tm80cUpQaWdGYTdoWkEyUG5HMkhRYVZFTzQvZEsyTUJSWS9GZU9ObVVSMk1qZXB4Y3FCNmE3cjgiCn0K
configSecretGostream: eyJnb3N0cmVhbS11c2VyIjoiTFVENEJVR1pGNkpLT1k2QiIsImdvc3RyZWFtLXBhc3N3b3JkIjoiVzZ4NGwwT3hObzRxSlBpZ0ZhN2haQTJQbkcySFFhVkVPNC9kSzJNQlJZL0ZlT05tVVIyTWplcHhjcUI2YTdyOCJ9Cg==

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1000m
    memory: 3Gi
  requests:
    cpu: 500m
    memory: 2Gi

ingress:
  enabled: true
  ingressClassName: internal-nginx
  #  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - graph-gd.stg.gdefence.io
  path: /
  labels:
    traffic-type: general

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: stack
              operator: In
              values:
                - graph-engineering-gd
