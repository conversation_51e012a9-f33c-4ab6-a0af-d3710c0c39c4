# helm

helm charts for graph-service tech stack

Helm for Service Initial Deployment:
1. In<PERSON><PERSON> helm with the kube config file from https://gdkube.gd.stg-myteksi.com/

2. Pull the helm config from https://gitlab.myteksi.net/grabdefence/helm and locate to defence/stg *** Make sure to update the image tag from the values.yaml

3. Try dry run first locally to check if there're any errors:

helm install graph-service --dry-run . --namespace defence
Then perform the initial deployment:

helm install graph-service . --namespace defence
Service Update:
Helm for Service Update:
*** Make sure to update the image tag from the values.yaml

helm upgrade graph-service . --namespace defence


