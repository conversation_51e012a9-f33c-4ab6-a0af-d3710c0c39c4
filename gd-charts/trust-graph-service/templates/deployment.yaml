apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "graph-service.fullname" . }}
  labels:
    {{- include "graph-service.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
      {{- include "graph-service.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "graph-service.selectorLabels" . | nindent 8 }}
        {{- include "graph-service.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "graph-service.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: GRAPH_SERVICE_BASE_CONF
            value: /var/config.json
          - name: DOGSTATSD_ADDR
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          - name: DATA_CONF
            value: /go/src/gitlab.myteksi.net/trust/grab-defence-base/commons/data/config-ci.json
          - name: GRAPH_SERVICE_VAULT_FOLDER
            value: /vault/secrets
          - name: GOLANG_PROTOBUF_REGISTRATION_CONFLICT
            value: warn
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
          - name: POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: POD_SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                fieldPath: spec.serviceAccountName
          volumeMounts:
            - name: graph-service-conf
              mountPath: /var/config.json
              subPath: config.json
            - name: graph-service-secret-vol
              mountPath: /vault/secrets
          ports:
            - name: http
              containerPort: 8081
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: 8081
            initialDelaySeconds: 5
            periodSeconds: 20
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 6
          readinessProbe:
            tcpSocket:
              port: 8081
            initialDelaySeconds: 5
            periodSeconds: 20
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 6
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      volumes:
        - name: graph-service-conf
          configMap:
            name: graph-service-conf
        - name: graph-service-secret-vol
          secret:
            secretName: graph-service-secret
            items:
              {{ if eq .stack "graph-engineering-gd" }}
              - key: config-secret-giestream.json
                path: config-secret-giestream.json
              - key: config-secret-gostream.json
                path: config-secret-gostream.json
              {{ else if eq .stack "graph-engineering-grab" }}
              - key: placeholder.json
                path: placeholder.json
              {{ end }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
