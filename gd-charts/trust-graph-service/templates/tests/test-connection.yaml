apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "graph-service.fullname" . }}-test-connection"
  labels:
    {{- include "graph-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "graph-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
