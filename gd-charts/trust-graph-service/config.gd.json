{"app_config": {"mode": "staging", "structured_logger": {"syslogTag": "structuredlog.graph-service", "workerCount": 4, "bufferSize": 1000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "cgls", "development": true}, "statsd": {"host": "", "port": 8125, "appName": "graph-service.gd"}, "dynamo_db_config": {"region": "ap-southeast-1", "table": "stg-graph-service-ddb-placeholder", "maxRetryAttempt": 3, "backoffBaseDelay": 50, "backoffMaxDelay": 200, "tag": "graph_service.gd.ddb", "timeoutMs": 2000, "errorPercent": 75, "maxConcurrentRequest": 8000, "maxVolPercentThreshold": 20, "sleepWindowMs": 5000, "maxQueueSize": 6000, "commandGroup": "graph_service.gd.ddb.cmd"}, "gremlin_http_config": {"client_config": {"prefix": "/gremlin"}, "hystric_config": {"timeoutMs": 5000, "maxConcurrentRequest": 500, "errorPercent": 50}, "retry_config": {"enabled": true, "max_retry_attempt": 3, "backoff_base_delay_ms": 50, "backoff_max_delay_ms": 200}, "skip_http_client_log": false, "tags": ["client:graph-service.gd"]}, "global_table_schema_config": {"tableName": "nstg-graph-service-global-meta"}, "graph_service_metadata_config": {"interval": 60}, "gie_stream_reader_config": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-graph-service-gd", "topic": "giestream", "appName": "graph-service-gd", "dtoName": "GdInferenceEvent", "packageName": "streams.tis.trust.giestream", "offset": "newest", "workers": 2, "enableSASL": false, "enableTLS": false}, "go_stream_reader_config": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-graph-service-gd", "topic": "gostream", "appName": "graph-service-gd", "dtoName": "GuardianOutput", "packageName": "streams.guardians.gostream", "offset": "newest", "workers": 2, "enableSASL": false, "enableTLS": false}, "stream_graph_config": {"refresh_interval": 60, "stream_config_table_name": "nstg-graph-service-stream-graph-configs", "stream_config_scripts_table_name": "nstg-graph-service-stream-graph-config-scripts"}}, "dynamic_config": {"neptune.client.health_check.rollout": 100, "graph_service.resource.cache.rollout": 100, "stream_graph.config.cron.rollout": 0, "streams.retry_kingpin_client": {"roll_out_percentage": 100, "max_attempts": 10, "retry_base_delay_in_ms": 10000, "retry_max_delay_in_ms": 20000, "disabled_tenant_list": ["grab"]}, "streams.giestream.init.rollout": 0, "streams.giestream.process.rollout": 0, "streams.giestream.orgnames": "ula", "streams.giestream.graphs": "gd_graph", "streams.gostream.init.rollout": 0, "streams.gostream.process.rollout": 0, "streams.gostream.orgnames": "ula", "streams.gostream.graphs": "gd_graph", "streams.giestream.process.js_configure.tenants": "guardian", "streams.graph.process.org_prefix.rollout": 100, "streams.giestream.process.js_configure.rollout": 100}}