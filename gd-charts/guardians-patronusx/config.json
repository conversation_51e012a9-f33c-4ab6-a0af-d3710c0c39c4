{"name": "Patronusx Service", "serviceID": "patronusx", "configVersion": "v1.0.0.patronusx", "mode": "staging", "logger": {"level": 5, "tag": "patronusx", "yallMode": true, "callerEnabled": true, "callerSkip": 1}, "statsd": {"host": "localhost", "port": 8125, "appName": "patronusx"}, "grabkit": {"features": ["tracelog", "newstats", "structuredlogs"], "maxrecvmsgsize": 4194304}, "structuredlogger": {"syslogTag": "structuredlog.patronusx", "workerCount": 10, "bufferSize": 10000, "logLevel": 5, "stacktraceLevel": 4, "logFormat": "cglsdebug", "development": false}, "storageConfig": {"redis": {"circuitBreakerErrorPercent": 30, "circuitBreakerMaxConcurrentRequest": 90000, "circuitBreakerMaxQueueSize": 20, "circuitBreakerMaxVolPercentThreshold": 20, "circuitBreakerSleepWindowMS": 5000, "circuitBreakerTimeoutMs": 5000, "idleTimeoutMs": 30000, "masterHost": "nstg-patronusx-redis.nprv.stg.gdefence.io", "maxActive": 1000, "name": "patronusx-redis-cluster", "port": 6379, "readFromMasterOnly": true, "readFromSlaveOnly": false, "readTimeoutMs": 1000, "writeTimeoutMs": 1000}, "ddb": {"default": {"itr7ADRDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_device_registry_itr7-default", "timeoutMs": 120000}, "itr7ADRDeviceEntityMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-adr_device_entity_mapping_itr7-gsi-default", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_device_entity_mapping_itr7-default", "timeoutMs": 120000}, "itr7IOSDeviceAttributeConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_attribute_itr7-default", "timeoutMs": 120000}, "itr7IOSDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_registry_itr7-default", "timeoutMs": 120000}, "itr7WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr7-default", "timeoutMs": 120000}, "itr8ADRHashedEntityDeviceMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-adr_hashed_entity_device_mapping_itr8-gsi-default", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_hashed_entity_device_mapping_itr8-default", "timeoutMs": 120000}, "itr8IOSDeviceAttributeConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_attribute_itr8-default", "timeoutMs": 120000}, "itr8IOSDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_registry_itr8-default", "timeoutMs": 120000}, "itr8WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr8-default", "timeoutMs": 120000}, "itr9WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr9-default", "timeoutMs": 120000}, "itr9EntityDeviceMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-entity_device_mapping_itr9-gsi-default", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-entity_device_mapping_itr9-default", "timeoutMs": 120000}}, "grab": {"itr7ADRDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_device_registry_itr7-grab", "timeoutMs": 120000}, "itr7ADRDeviceEntityMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-adr_device_entity_mapping_itr7-gsi-grab", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_device_entity_mapping_itr7-grab", "timeoutMs": 120000}, "itr7IOSDeviceAttributeConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_attribute_itr7-grab", "timeoutMs": 120000}, "itr7IOSDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_registry_itr7-grab", "timeoutMs": 120000}, "itr7WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr7-grab", "timeoutMs": 120000}, "itr8ADRHashedEntityDeviceMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-adr_hashed_entity_device_mapping_itr8-gsi-grab", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_hashed_entity_device_mapping_itr8-grab", "timeoutMs": 120000}, "itr8IOSDeviceAttributeConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_attribute_itr8-grab", "timeoutMs": 120000}, "itr8IOSDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_registry_itr8-grab", "timeoutMs": 120000}, "itr8WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr8-grab", "timeoutMs": 120000}, "itr9WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr9-grab", "timeoutMs": 120000}, "itr9EntityDeviceMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-entity_device_mapping_itr9-gsi-grab", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-entity_device_mapping_itr9-grab", "timeoutMs": 120000}}, "guardian": {"itr7ADRDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_device_registry_itr7-guardian", "timeoutMs": 120000}, "itr7ADRDeviceEntityMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-adr_device_entity_mapping_itr7-gsi-guardian", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_device_entity_mapping_itr7-guardian", "timeoutMs": 120000}, "itr7IOSDeviceAttributeConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_attribute_itr7-guardian", "timeoutMs": 120000}, "itr7IOSDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_registry_itr7-guardian", "timeoutMs": 120000}, "itr7WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr7-guardian", "timeoutMs": 120000}, "itr8ADRHashedEntityDeviceMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-adr_hashed_entity_device_mapping_itr8-gsi-guardian", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_hashed_entity_device_mapping_itr8-guardian", "timeoutMs": 120000}, "itr8IOSDeviceAttributeConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_attribute_itr8-guardian", "timeoutMs": 120000}, "itr8IOSDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_registry_itr8-guardian", "timeoutMs": 120000}, "itr8WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr8-guardian", "timeoutMs": 120000}, "itr9WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr9-guardian", "timeoutMs": 120000}, "itr9EntityDeviceMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-entity_device_mapping_itr9-gsi-guardian", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-entity_device_mapping_itr9-guardian", "timeoutMs": 120000}}, "client2": {"itr7ADRDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_device_registry_itr7-client2", "timeoutMs": 120000}, "itr7ADRDeviceEntityMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-adr_device_entity_mapping_itr7-gsi-client2", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_device_entity_mapping_itr7-client2", "timeoutMs": 120000}, "itr7IOSDeviceAttributeConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_attribute_itr7-client2", "timeoutMs": 120000}, "itr7IOSDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_registry_itr7-client2", "timeoutMs": 120000}, "itr7WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr7-client2", "timeoutMs": 120000}, "itr8ADRHashedEntityDeviceMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-adr_hashed_entity_device_mapping_itr8-gsi-client2", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-adr_hashed_entity_device_mapping_itr8-client2", "timeoutMs": 120000}, "itr8IOSDeviceAttributeConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_attribute_itr8-client2", "timeoutMs": 120000}, "itr8IOSDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-ios_device_registry_itr8-client2", "timeoutMs": 120000}, "itr8WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr8-client2", "timeoutMs": 120000}, "itr9WebDeviceRegistryConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-web_device_registry_itr9-client2", "timeoutMs": 120000}, "itr9EntityDeviceMappingConfig": {"errorPercent": 50, "index": "nstg-patronusx-entity_device_mapping_itr9-gsi-client2", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-patronusx-entity_device_mapping_itr9-client2", "timeoutMs": 120000}}}}, "streamReader": {"SPOStream": {"readerEnabled": true, "maxWorkerCount": 5, "staleRecordTimeSec": 600, "batchSize": 1, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "readerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "shieldx-spostream-producer", "clusterType": "critical", "enableTLL": false, "enableSASL": false, "kafkaVersion": "2.2.1", "shortName": "spostream", "stream": "spostream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "shieldx", "packageName": "streams.guardians.spostream", "dtoName": "ShieldxPrimaryOutput", "consumerGroupID": "patronusx-spostream-consumer", "offsetType": "oldest", "consumerVersion": "v2"}}}, "streamWriter": {"GDStream": {"writerEnabled": true, "creationRetryLimit": 100, "creationRetryIntervalMs": 100, "writerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "patronusx-gdstream-producer", "clusterType": "critical", "enableSASL": false, "enableTLS": false, "kafkaVersion": "2.2.1", "shortName": "gdstream", "stream": "gdstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "patronusx", "packageName": "streams.guardians.gdstream", "dtoName": "GuardianDevice"}}}, "itr7Collision": {"hardDriveCID": {}, "macAddr": {}, "imei": {}, "gsfID": {}, "androidID": {}, "advertisingID": {}, "simSerialNumber": {}, "simSerialNumber1": {}, "simSerialNumber2": {}, "subscriberID": {}}, "itr7MatchingConfig": {"GeneralInfoFields": ["CPUInfo", "TotalRAM", "DeviceLanguage", "TimeZone", "NetworkOperator", "WebBrowserVersion", "RoProductManufacturer", "RoBuildFingerprint", "RoProductModel", "RoProductBrand", "RoProductDevice", "RoBoardPlatform", "RoBuildHost", "RoBuildID", "RoSfLcdDensity", "Proxy"], "GeneralInfoVersion": "gi.v2", "adr10IgnoreFields": ["MacAddr", "Imei1", "Imei2"], "confidenceScoreForIDFields": [29, 23, 19, 17, 13, 11, 7, 5], "confidenceScoreForVariableIDFields": [3, 2, 1], "deviceIDVersion": "did.v7", "dummyData": ["000000000000000", "fffffffffffffff", "----", "00:00:00:00:00:00", "ff:ff:ff:ff:ff:ff"], "idFields": ["HardDriveCID1", "HardDriveCID2", "MacAddr", "Imei1", "Imei2", "Gsfid", "AndroidID", "AdvertisingID"], "minMatchThreshold": 1, "variableIDFields": ["SimSerialNumber", "SimSerialNumber1", "SimSerialNumber2"]}, "itr7IOSMatchingConfig": {"GeneralInfoFields": ["DeviceName", "SystemName", "SystemVersion", "UserInterfaceIdiom", "DeviceModel", "InternalIPAddress", "CarrierName", "CountryCode", "CurrentRadioAccessTechnology", "TypeOfConnection", "CurrentWifiNetworkBSSID", "ExternalIPAddress", "LocalizedModel", "MobileCountryCode", "MobileNetworkCode", "Trust"], "GeneralInfoVersion": "gi.ios.v1", "confidenceScoreForIDFields": [7, 5, 3, 2], "deviceIDVersion": "did.v7", "dummyData": ["00000000-0000-0000-0000-000000000000"], "idFields": ["AdvertisingIdentifier", "ICloudIdentifier", "KeychainIdentifier", "VendorIdentifier"], "minMatchThreshold": 1}, "itr7WebMatchingConfig": {"deviceIDVersion": "did.v7"}, "itr8MatchingConfig": {"deviceIDVersion": "did.v8", "dummyData": ["000000000000000", "fffffffffffffff", "----", "00:00:00:00:00:00", "ff:ff:ff:ff:ff:ff"], "generalInfoVersion": "gi.adr.v8", "adr10IgnoreFields": ["MacAddr", "Imei1", "Imei2"], "hashFields": ["HardDriveCID1", "HardDriveCID2", "MacAddr", "Gsfid", "RoProductModel", "AndroidID"], "minMatchThreshold": 2}, "itr8IOSMatchingConfig": {"GeneralInfoFields": ["DeviceName", "SystemName", "SystemVersion", "UserInterfaceIdiom", "DeviceModel", "InternalIPAddress", "CarrierName", "CountryCode", "CurrentRadioAccessTechnology", "TypeOfConnection", "CurrentWifiNetworkBSSID", "ExternalIPAddress", "LocalizedModel", "MobileCountryCode", "MobileNetworkCode", "Trust"], "GeneralInfoVersion": "gi.ios.v1", "confidenceScoreForIDFields": [7, 5, 3, 2], "deviceIDVersion": "did.v8", "dummyData": ["00000000-0000-0000-0000-000000000000"], "idFields": ["AdvertisingIdentifier", "ICloudIdentifier", "KeychainIdentifier", "VendorIdentifier"], "minMatchThreshold": 1}, "itr8WebMatchingConfig": {"deviceIDVersion": "did.v8"}, "itr9WebMatchingConfig": {"deviceIDVersion": "did.v9"}, "defaultCallerConfig": {"deviceIDAlgorithm": "DID_ALGO_ITR_7", "associatedDeviceCountLimit": 500, "associatedUserCountLimit": 500, "lookupHistoryInDaysLimit": 90}, "itr7ConfidenceLevel": {"keyConfidenceWeight": 60, "lookupKey10ConfidenceWeight": 10, "lookupKey1ConfidenceWeight": 60, "lookupKey2ConfidenceWeight": 55, "lookupKey3ConfidenceWeight": 50, "lookupKey4ConfidenceWeight": 45, "lookupKey5ConfidenceWeight": 40, "lookupKey6ConfidenceWeight": 35, "lookupKey7ConfidenceWeight": 30, "lookupKey8ConfidenceWeight": 25, "lookupKey9ConfidenceWeight": 20, "trustConfidenceWeight": 40}, "rollOut": {"processHydraPayloadPercentage": 100, "processITR7IOSPercentage": 100, "processITR7Percentage": 100, "processITR7WebPercentage": 100, "processITR8IOSPercentage": 100, "processITR8Percentage": 100, "processITR8WebPercentage": 100, "processITR9Percentage": 100, "processITR9WebPercentage": 100, "processParallelismPercentage": 100}, "feature": {"enableSPOStreamReading": true, "enableHTTPEndpoints": true, "enableHydraPayloadInputEndpoint": true, "enableDBWriting": true, "enableItr8DeviceIDComputation": true, "enableItr8AndroidDeviceIDComputation": true, "enableItr8WebDeviceIDComputation": true, "enableItr8IOSDeviceIDComputation": true, "enableItr8ADRHashedEntityDeviceMappingDBWriting": true, "enableItr9DeviceIDComputation": true, "enableItr9WebDeviceIDComputation": true, "enableItr7DeviceIDComputation": true, "enableItr7DBReading": true, "enableItr7DBWriting": true, "enableItr7IOSDeviceIDComputation": true, "enableItr7IOSDBReading": true, "enableItr7IOSDBWriting": true, "enableItr7WebDeviceIDComputation": true, "enableItr7WebDBReading": true, "enableItr7WebDBWriting": true, "enableItr8IOSDBReading": true, "enableItr8IOSDBWriting": true, "enableItr8WebDBWriting": true, "enableItr9WebDBReading": true, "enableItr9WebDBWriting": true, "enableItr9EntityDeviceMappingDBWriting": true}, "general": {"itr7EntityDeviceLookupRedisTTLSeconds": 86400, "itr7IOSEntityDeviceLookupRedisTTLSeconds": 86400, "itr7WebEntityDeviceLookupRedisTTLSeconds": 86400, "itr8IOSEntityDeviceLookupRedisTTLSeconds": 86400, "itr8WebEntityDeviceLookupRedisTTLSeconds": 86400, "itr7DynamoDBTTLDays": 180, "itr8DynamoDBTTLDays": 180, "itr9DynamoDBTTLDays": 180}, "activeDeviceIDAlgo": ["DID_ALGO_ITR_4", "DID_ALGO_ITR_7", "DID_ALGO_ITR_8"]}