# data-sinker deployment

you can use this way to upgrade this service:
1. go to defence folder(under the helm/defence), if you are in this folder
```
cd ../..
```
2. use this command to upgrade the change. change the kubeconfig with your kubeconfig path, like removing --kubeconfig or add your kubeconfig.
```
 helm upgrade data-sinker ./templates/data-sinker/ -f ./nstg/data-sinker/values.yaml --namespace defence --kubeconfig ~/.kube-nstg/config
```
or for prod:
```
 helm upgrade data-sinker ./templates/data-sinker/ -f ./nprd-sg/data-sinker/values.yaml --namespace defence --kubeconfig ~/.kube-nprd/config
```
