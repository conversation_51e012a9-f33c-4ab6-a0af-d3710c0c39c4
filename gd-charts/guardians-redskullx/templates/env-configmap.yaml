apiVersion: v1
kind: ConfigMap
metadata:
  name: redskullx-env
data:
  GOPATH: /go
  GP: /go/src/gitlab.myteksi.net/grabdefence/guardians
  APP: /app
  STATIC_CONFIGS: /app/static_configs
  SYSAUTH3_CA_CHAIN: /app/static_configs/sysauth3_ca-chain-ci.crt.pem
  SYSAUTH3_SERVICE_KEY: /app/static_configs/sysauth3_service-ci.key
  SYSAUTH3_SERVICE_CERT: /app/static_configs/sys_auth3_service-ci.crt.pem
  DATA_CONF:  /app/static_configs/data_config-ci.json
  SITEVAR_CONF: /app/static_configs/sitevar_config-ci.json
  TEST_CONF: /app/static_configs/gtest_config-ci.json
  END_CATCHERSERVER_CONF: /app/static_configs/catcher_config-ci.json
  CONF_SERVER_CONF: /app/static_configs/etcd_config-ci.json
  GRAB_METADATA: /app/static_configs/gmeta_metadata.json
  TRACER_CONF: /app/static_configs/tracer_config-ci.json
  UCM_MODE: development
  MYSQL_HOST: mysql-service
  MYSQL_PORT: "3306"
  MYSQL_ROOT_PASSWORD: test
  REDIS_HOST: redis-service
  GRABDDB_HOST: "http://dynamodb-service:8000"
