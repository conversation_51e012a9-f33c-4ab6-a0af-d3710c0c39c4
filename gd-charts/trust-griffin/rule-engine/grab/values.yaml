env:
  env: sub_stg_grab
  orgname: guardian

configSecret: ****************************************************************************************************************************************************************

volumesMounts:
  - name: griffin-engine-grab-secret-vol
    mountPath: /vault/secrets

volumes:
  - name: griffin-engine-grab-secret-vol
    secret:
      secretName: griffin-engine-grab-secret
      items:
        - key: config-secret.json
          path: config-secret.json
    
griffinSecretName: griffin-engine-grab-secret