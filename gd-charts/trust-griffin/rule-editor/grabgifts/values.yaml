env:
  env: sub_stg_grabgifts
  orgname: second-client

configSecret: ****************************************************************************************************************************************************************************************************************************************

volumesMounts:
  - name: griffin-editor-grabgifts-secret-vol
    mountPath: /vault/secrets

volumes:
  - name: griffin-editor-grabgifts-secret-vol
    secret:
      secretName: griffin-editor-grabgifts-secret
      items:
        - key: config-secret.json
          path: config-secret.json

ingress:
  enabled: true
  ingressClassName: internal-nginx
  #  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - griffin-grabgifts.stg.gdefence.io
  path: /
  labels: {}
    # traffic-type: general
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
    
griffinSecretName: griffin-editor-grabgifts-secret
