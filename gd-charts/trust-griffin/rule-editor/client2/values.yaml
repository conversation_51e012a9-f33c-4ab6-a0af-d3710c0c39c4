env:
  env: sub_stg_client2
  orgname: second-client

configSecret: ****************************************************************************************************************************************************************************************************************************************************************

volumesMounts:
  - name: griffin-editor-client2-secret-vol
    mountPath: /vault/secrets

volumes:
  - name: griffin-editor-client2-secret-vol
    secret:
      secretName: griffin-editor-client2-secret
      items:
        - key: config-secret.json
          path: config-secret.json

ingress:
  enabled: true
  ingressClassName: internal-nginx
  #  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - griffin-client2.stg.gdefence.io
  path: /
  labels: {}
    # traffic-type: general
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
    
griffinSecretName: griffin-editor-client2-secret
