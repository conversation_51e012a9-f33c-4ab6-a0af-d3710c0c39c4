env:
  env: sub_stg_grab
  orgname: guardian

configSecret: ****************************************************************************************************************************************************************************************************************************************************

volumesMounts:
  - name: griffin-editor-grab-secret-vol
    mountPath: /vault/secrets

volumes:
  - name: griffin-editor-grab-secret-vol
    secret:
      secretName: griffin-editor-grab-secret
      items:
        - key: config-secret.json
          path: config-secret.json
        
ingress:
  enabled: true
  #  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  ingressClassName: internal-nginx
  hosts:
    - griffin.stg.gdefence.io
  path: /
  labels: {}
    # traffic-type: general
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
    
griffinSecretName: griffin-editor-grab-secret
  
