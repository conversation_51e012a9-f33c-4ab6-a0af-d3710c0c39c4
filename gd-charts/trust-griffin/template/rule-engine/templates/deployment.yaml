apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "griffin.fullname" . }}
  labels:
    {{- include "griffin.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
      {{- include "griffin.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "griffin.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "griffin.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: ENV
              value: {{ .Values.env.env }}
            - name: ORG_NAME
              value: {{ .Values.env.orgname }}
            - name: VAULT_CONFIG_PATH
              value: /vault/secrets/config-secret.json
            - name: GRIFFIN_ENABLE_COUNT_CHECK
              value: "True"
            - name: DOGSTATSD_PORT_8125_UDP_ADDR
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
          volumeMounts:
            {{- toYaml .Values.volumesMounts | nindent 12 }}
          ports:
            - name: http
              containerPort: 9001
              protocol: TCP
          livenessProbe:
            httpGet:
              scheme: HTTP
              port: 9001
              path: /health_check
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              scheme: HTTP
              port: 9001
              path: /health_check
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          startupProbe:
            httpGet:
              scheme: HTTP
              port: 9001
              path: /health_check
            failureThreshold: 30
            periodSeconds: 10
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes: 
        {{- toYaml .Values.volumes | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
