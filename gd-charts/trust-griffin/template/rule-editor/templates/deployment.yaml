apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "griffin.fullname" . }}
  labels:
    {{- include "griffin.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
      {{- include "griffin.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "griffin.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "griffin.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: ENV
              value: {{ .Values.env.env }}
            - name: ORG_NAME
              value: {{ .Values.env.orgname }}
            - name: VAULT_CONFIG_PATH
              value: /vault/secrets/config-secret.json
            - name: DOGSTATSD_PORT_8125_UDP_ADDR
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: GRIFFIN_ENABLE_COUNT_CHECK
              value: "True"
            - name: GOOGLE_CLIENT_ID
              value: "************-5ngcj48o3kopila0hpj9fijd5ka6p418.apps.googleusercontent.com"
            - name: DEFENCE_SERVICE_END_POINT
              value: "defenceapi:8081"
            - name: OUTBOUND_SQS_QUEUE_URL
              value: "https://sqs.ap-southeast-1.amazonaws.com/128701485547/stg-defence-griffin-backtesting-outbound"
            - name: INBOUND_SQS_QUEUE_URL
              value: "https://sqs.ap-southeast-1.amazonaws.com/128701485547/stg-defence-griffin-backtesting-inbound"
            - name: GRIFFIN_DISABLE_BACKTESTING
              value: "True"
            - name: ONEPORTAL_URL
              value: "https://oneportal.stg.gdefence.io"
            - name: APIGATEWAY_URL
              value: "https://api.stg.gdefence.io"
            - name: ENABLE_GD_APIGW_SSO
              value: "True"
          volumeMounts:
            {{- toYaml .Values.volumesMounts | nindent 12 }}
          ports:
            - name: http
              containerPort: 9000
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: 9000
          readinessProbe:
            tcpSocket:
              port: 9000
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes: 
        {{- toYaml .Values.volumes | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
