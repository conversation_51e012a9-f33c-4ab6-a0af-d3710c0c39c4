# Default values for defence_api.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/new/defence/griffin
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "edit_e90b580e"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "defence-read"

podAnnotations:
  co.elastic.logs/json.add_error_key: "true"
  co.elastic.logs/json.keys_under_root: "true"
  co.elastic.logs/json.message_key: message
  co.elastic.logs/json.overwrite_keys: "true"
  # https://www.elastic.co/guide/en/beats/filebeat/current/multiline-examples.html
  co.elastic.logs/multiline.pattern: '^([\d\-\:\+ \[\]TZ\.]+(\[?(INFO|WARNING|ERROR|DEBUG|CRITICAL|FATAL)\]?))|(\[?(INFO|WARNING|ERROR|DEBUG|CRITICAL|FATAL)\]?: )'
  co.elastic.logs/multiline.negate: "true"
  co.elastic.logs/multiline.match: "after"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 9000

ingress:
  enabled: false
  #  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    # hosts:
    # - host: chart-example.local
    # paths: []
    #tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: 
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 150m
    memory: 1Gi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 70

nodeSelector:
  stack: general

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    - labelSelector:
        matchExpressions:
        - key: app
          operator: In
          values:
          - griffin
      topologyKey: "kubernetes.io/hostname"
