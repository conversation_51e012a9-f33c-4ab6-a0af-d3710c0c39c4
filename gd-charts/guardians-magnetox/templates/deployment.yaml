apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "magnetox.fullname" . }}
  labels:
    {{- include "magnetox.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
      {{- include "magnetox.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "magnetox.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "magnetox.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      terminationGracePeriodSeconds: 30
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: BINARY_TAG
            value: {{ .Values.image.tag | default .Chart.AppVersion }}
          - name: MAGNETOX_CONFIG
            value: /code/abuse/magnetox/config.json
          - name: MAGNETOX_CONFIG_SECRET
            value: /vault/secrets/config-secret.json
          - name: DOGSTATSD_ADDR
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          envFrom:
          - configMapRef:
              name: magnetox-env
          volumeMounts:
            - name: config
              mountPath: /code/abuse/magnetox/config.json
              subPath: config.json
            - name: magnetox-secret-vol
              mountPath: /vault/secrets
          ports:
            - name: http
              containerPort: 8088
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health_check
              port: 8088
          readinessProbe:
            httpGet:
              path: /health_check
              port: 8088
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      volumes:
        - name: config
          configMap:
            name: magnetox-configmap
        - name: magnetox-secret-vol
          secret:
            secretName: magnetox-secret
            items:
              - key: config-secret.json
                path: config-secret.json
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
