# Default values for magnetox.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/new/guardians/magnetox
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v-**********-3ffca9ba"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

env: "nstg"

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "guardians-read"

podAnnotations:
  co.elastic.logs/json.add_error_key: "true"
  co.elastic.logs/json.keys_under_root: "true"
  co.elastic.logs/json.message_key: message
  co.elastic.logs/json.overwrite_keys: "true"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 8088

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: 100m
    memory: 192Mi
  requests:
    cpu: 100m
    memory: 192Mi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 30
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

nodeSelector:
  stack: general

tolerations: []

affinity: {}

configSecret:
  eyJ2YXVsdC5kZWZhdWx0Lm1hc3RlciI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1tYWduZXRveC1teXNxbC1kZWZhdWx0Lm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpLzxubyB2YWx1ZT4/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIsInZhdWx0LmRlZmF1bHQucmVwbGljYSI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1tYWduZXRveC1teXNxbC1yZXBsaWNhLWRlZmF1bHQubnBydi5zdGcuZ2RlZmVuY2UuaW86MzMwNikvbWFnbmV0b3g/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIsInZhdWx0LmdyYWIubWFzdGVyIjogImdkYWRtaW46TmpSNEh5dmFHZE1HQHRjcChuc3RnLW1hZ25ldG94LW15c3FsLWdyYWIubnBydi5zdGcuZ2RlZmVuY2UuaW86MzMwNikvbWFnbmV0b3g/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIsInZhdWx0LmdyYWIucmVwbGljYSI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1tYWduZXRveC1teXNxbC1yZXBsaWNhLWdyYWIubnBydi5zdGcuZ2RlZmVuY2UuaW86MzMwNikvbWFnbmV0b3g/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIsInZhdWx0Lmd1YXJkaWFuLm1hc3RlciI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1tYWduZXRveC1teXNxbC1ndWFyZGlhbi5ucHJ2LnN0Zy5nZGVmZW5jZS5pbzozMzA2KS9tYWduZXRveD9wYXJzZVRpbWU9dHJ1ZSZsb2M9VVRDIiwidmF1bHQuZ3VhcmRpYW4ucmVwbGljYSI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1tYWduZXRveC1teXNxbC1yZXBsaWNhLWd1YXJkaWFuLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL21hZ25ldG94P3BhcnNlVGltZT10cnVlJmxvYz1VVEMiLCJ2YXVsdC5uaW5qYXZhbi5tYXN0ZXIiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKHN0Zy1tYWduZXRveC1teXNxbC1uaW5qYXZhbi5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL21hZ25ldG94P3BhcnNlVGltZT10cnVlJmxvYz1VVEMiLCJ2YXVsdC5uaW5qYXZhbi5yZXBsaWNhIjogImdkYWRtaW46TmpSNEh5dmFHZE1HQHRjcChzdGctbWFnbmV0b3gtbXlzcWwtcmVwbGljYS1uaW5qYXZhbi5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL21hZ25ldG94P3BhcnNlVGltZT10cnVlJmxvYz1VVEMiLCJ2YXVsdC5jbGllbnQyLm1hc3RlciI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1tYWduZXRveC1teXNxbC1jbGllbnQyLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL21hZ25ldG94P3BhcnNlVGltZT10cnVlJmxvYz1VVEMiLCJ2YXVsdC5jbGllbnQyLnJlcGxpY2EiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKG5zdGctbWFnbmV0b3gtbXlzcWwtcmVwbGljYS1jbGllbnQyLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL21hZ25ldG94P3BhcnNlVGltZT10cnVlJmxvYz1VVEMiCn0K