{"name": "Magnetox Service", "serviceID": "magnetox", "configVersion": "v1.0.0.magnetox", "mode": "staging", "logger": {"level": 5, "tag": "magnetox", "yallMode": true, "callerEnabled": true, "callerSkip": 1}, "structuredlogger": {"syslogTag": "structuredlog.magnetox", "workerCount": 10, "bufferSize": 10000, "logLevel": 5, "stacktraceLevel": 4, "logFormat": "cglsdebug", "development": false}, "statsd": {"host": "localhost", "port": 8125, "appName": "magnetox"}, "grabkit": {"features": ["tracelog", "newstats", "structuredlogs"], "maxrecvmsgsize": 4194304}, "storageConfig": {"redisCluster": {"circuitBreakerErrorPercent": 80, "circuitBreakerMaxConcurrentRequest": 1000000, "circuitBreakerMaxQueueSize": 20, "circuitBreakerMaxVolPercentThreshold": 20, "circuitBreakerSleepWindowMS": 5000, "circuitBreakerTimeoutMs": 7000, "idleTimeoutMs": 30000, "masterHost": "nstg-magnetox-redis.nprv.stg.gdefence.io", "maxActive": 1000, "name": "magnetox-redis-cluster", "port": 6379, "readFromMasterOnly": true, "readFromSlaveOnly": false, "readTimeoutMs": 5000, "writeTimeoutMs": 5000}, "sssStorageConfig": {"enabled": true, "compressionEnabled": false, "bucket": "nstg-magnetox-s3", "region": "ap-southeast-1"}, "mysqlCircuitBreaker": {"readFromSlave": false, "timeoutMs": 5000, "errorPercent": 50, "maxConcurrentRequest": 90000, "maxVolPercentThreshold": 20, "sleepWindowMs": 5000, "maxQueueSize": 5000}, "mysql": {"default": {"mysql": {"master": {"dsn": "vault.default.master", "maxIdle": 20, "maxOpen": 200}, "slave": {"dsn": "vault.default.replica", "maxIdle": 20, "maxOpen": 200}}}, "grab": {"mysql": {"master": {"dsn": "vault.grab.master", "maxIdle": 20, "maxOpen": 200}, "slave": {"dsn": "vault.grab.replica", "maxIdle": 20, "maxOpen": 200}}}, "guardian": {"mysql": {"master": {"dsn": "vault.guardian.master", "maxIdle": 20, "maxOpen": 200}, "slave": {"dsn": "vault.guardian.replica", "maxIdle": 20, "maxOpen": 200}}}, "ninjavan": {"mysql": {"master": {"dsn": "vault.ninja<PERSON>.master", "maxIdle": 20, "maxOpen": 200}, "slave": {"dsn": "vault.ninjavan.replica", "maxIdle": 20, "maxOpen": 200}}}, "client2": {"mysql": {"master": {"dsn": "vault.client2.master", "maxIdle": 20, "maxOpen": 200}, "slave": {"dsn": "vault.client2.replica", "maxIdle": 20, "maxOpen": 200}}}}, "ddb": {"default": {"deviceDataConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-magnetox-device_data-default", "timeoutMs": 120000}}, "grab": {"deviceDataConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-magnetox-device_data-grab", "timeoutMs": 120000}}, "guardian": {"deviceDataConfig": {"errorPercent": 50, "index": "<nil>", "maxConcurrentRequest": 90000, "maxQueueSize": 5000, "maxVolPercentThreshold": 20, "region": "ap-southeast-1", "sleepWindowMs": 5000, "table": "nstg-magnetox-device_data-guardian", "timeoutMs": 120000}}}}, "streamReader": {"SPVStream": {"readerEnabled": true, "maxWorkerCount": 5, "staleRecordTimeSec": 600, "batchSize": 1, "creationRetryLimit": 100, "creationRetryIntervalMs": 1000, "readerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "magnetox-spvstream-consumer", "clusterType": "critical", "enableTLL": false, "enableSASL": false, "kafkaVersion": "2.2.1", "shortName": "spvstream", "stream": "spvstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "magnetox", "packageName": "streams.guardians.spvstream", "dtoName": "ShieldxPrimaryVerdict", "consumerGroupID": "magnetox-spvstream-consumer", "offsetType": "oldest", "consumerVersion": "v2"}}, "SPOStream": {"readerEnabled": true, "maxWorkerCount": 5, "staleRecordTimeSec": 600, "batchSize": 1, "creationRetryLimit": 10, "creationRetryIntervalMs": 1000, "readerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "magnetox-spostream-consumer", "clusterType": "critical", "enableTLL": false, "enableSASL": false, "kafkaVersion": "2.2.1", "shortName": "spostream", "stream": "spostream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "magnetox", "packageName": "streams.guardians.spostream", "dtoName": "ShieldxPrimaryOutput", "consumerGroupID": "magnetox-spostream-consumer", "offsetType": "oldest", "consumerVersion": "v2"}}, "NHPStream": {"readerEnabled": true, "maxWorkerCount": 5, "staleRecordTimeSec": 600, "batchSize": 1, "creationRetryLimit": 10, "creationRetryIntervalMs": 1000, "readerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "magnetox-nhpstream-consumer", "clusterType": "critical", "enableTLL": false, "enableSASL": false, "kafkaVersion": "2.2.1", "shortName": "nhpstream", "stream": "nhpstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "magnetox", "packageName": "streams.guardians.nhpstream", "dtoName": "NoHydraPayload", "consumerGroupID": "magnetox-nhpstream-consumer", "offsetType": "oldest", "consumerVersion": "v2"}}, "SCStream": {"readerEnabled": true, "maxWorkerCount": 5, "staleRecordTimeSec": 600, "batchSize": 1, "creationRetryLimit": 10, "creationRetryIntervalMs": 1000, "readerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "magnetox-scstream-consumer", "clusterType": "critical", "enableTLL": false, "enableSASL": false, "kafkaVersion": "2.2.1", "shortName": "scstream", "stream": "scstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "magnetox", "packageName": "streams.guardians.scstream", "dtoName": "ShieldxCheckpoint", "consumerGroupID": "magnetox-scstream-consumer", "offsetType": "oldest", "consumerVersion": "v2"}}, "GDStream": {"readerEnabled": true, "maxWorkerCount": 5, "staleRecordTimeSec": 600, "batchSize": 1, "creationRetryLimit": 10, "creationRetryIntervalMs": 1000, "readerConfig": {"brokers": ["stg-gd-singapore.coban-partner:30100"], "clientID": "magnetox-gdstream-consumer", "clusterType": "critical", "enableTLL": false, "enableSASL": false, "kafkaVersion": "2.2.1", "shortName": "gdstream", "stream": "gdstream", "compressionCodec": "snappy", "enableRetry": true, "requiredAcks": 1, "appname": "magnetox", "packageName": "streams.guardians.gdstream", "dtoName": "GuardianDevice", "consumerGroupID": "magnetox-gdstream-consumer", "offsetType": "oldest", "consumerVersion": "v2"}}}, "generalConfig": {"getVerdictsMaxLookUpTimeSecs": 7776000, "getVerdictsMaxRecordCount": 100, "deviceDataDDBTTLDays": 7}, "defaultClientConfig": {"clientName": "default", "isDisabled": false, "fetchS3Payload": true, "lastKnownHydraInfoLookupTTLSec": 604800, "lastKnownHydraInfoLookupCountLimit": 1, "hydraInfoByEventIdLookupTTLSec": 86400}}