{"appConfig": {"mode": "staging", "structuredlogger": {"workerCount": 5, "bufferSize": 1000, "logLevel": 2, "stacktraceLevel": 4, "logFormat": "cgls", "development": true}, "statsd": {"host": "", "port": 8125, "appName": "counter-service-lite"}, "mySQLConfig": {"mysql": {"master": {"dsn": "<vault.counter-service-lite>", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "<vault.counter-service-lite>", "maxIdle": 2, "maxOpen": 10}}}, "dynamoDBConfig": {"region": "ap-southeast-1", "table": "nstg-trust-counter-service-default", "maxRetryAttempt": 3, "backoffBaseDelay": 1000, "backoffMaxDelay": 3000, "tag": "ddb.cb", "timeoutMs": 1000, "errorPercent": 75, "maxConcurrentRequest": 8000, "maxVolPercentThreshold": 20, "sleepWindowMs": 5000, "maxQueueSize": 6000, "commandGroup": "ddb.cmd"}, "dayTableSchemaConfig": {"tableName": "nstg-counter-service-lite-day", "partitionKey": "key", "rangeKey": "timestamp", "ttlAttribute": "ttl", "ttlInSec": 86400}, "hourTableSchemaConfig": {"tableName": "nstg-counter-service-lite-hour", "partitionKey": "key", "rangeKey": "timestamp", "ttlAttribute": "ttl", "ttlInSec": 86400}, "minuteTableSchemaConfig": {"tableName": "nstg-counter-service-lite-minute", "partitionKey": "key", "rangeKey": "timestamp", "ttlAttribute": "ttl", "ttlInSec": 86400}, "gieStreamTableSchemaConfig": {"tableName": "nstg-counter-service-lite-giestream-distinct", "partitionKey": "key", "rangeKey": "timestamp", "ttlAttribute": "ttl", "ttlInSec": 2592000}, "goStreamTableSchemaConfig": {"tableName": "nstg-counter-service-lite-gostream-distinct", "partitionKey": "key", "rangeKey": "timestamp", "ttlAttribute": "ttl", "ttlInSec": 864000}, "spoStreamTableSchemaConfig": {"tableName": "nstg-counter-service-lite-spostream-distinct", "partitionKey": "key", "rangeKey": "timestamp", "ttlAttribute": "ttl", "ttlInSec": 864000}, "redisClusterConfig": {"clusterNodeAddrs": ["nstg-counter-service-lite-redis.nprv.stg.gdefence.io:6379"], "poolSize": 6, "readTimeoutInMs": 100, "writeTimeoutInMs": 100, "readOnlyFromSlaves": false}, "counterConfig": {"queryIntervalInSec": 3}, "goStreamReaderConfig": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-counter-service-lite", "topic": "gostream", "appName": "counter-service-lite", "dtoName": "GuardianOutput", "packageName": "streams.guardians.gostream", "offset": "newest", "workers": 2, "enableSASL": false, "enableTLS": false}, "gieStreamReaderConfig": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-counter-service-lite", "topic": "giestream", "appName": "counter-service-lite", "dtoName": "GdInferenceEvent", "packageName": "streams.tis.trust.giestream", "offset": "newest", "workers": 2, "enableSASL": false, "enableTLS": false}, "spoStreamReaderConfig": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-counter-service-lite", "topic": "spostream", "appName": "counter-service-lite", "dtoName": "ShieldxPrimaryOutput", "packageName": "streams.guardians.spostream", "offset": "newest", "workers": 2, "enableSASL": false, "enableTLS": false}, "tcrStreamConfig": {"disable": false, "broker": ["stg-gd-singapore.coban-partner:30100"], "groupID": "nstg-consumer-app-counter-service-lite", "topic": "tcrstream", "appName": "counter-service-lite", "dtoName": "TrustCounterResource", "packageName": "streams.tis.trust.tcrstream", "offset": "newest", "workers": 2, "enableSASL": false, "enableTLS": false}}, "dynamicConfig": {"moi.stream.rollout": 100, "batch_read.cache.enabled": true, "batch_read.logging.sampling": true, "gostream.retry_kingpin_client": {"roll_out_percentage": 100, "max_attempts": 5, "retry_base_delay_in_ms": 10000, "retry_max_delay_in_ms": 15000, "disabled_tenant_list": ["grab"]}}}