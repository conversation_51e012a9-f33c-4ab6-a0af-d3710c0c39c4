# Default values for counter-service-lite.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/defence/counter-service-lite
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v_d5e318f4"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "defence-read"

podAnnotations:
  co.elastic.logs/json.add_error_key: "true"
  co.elastic.logs/json.keys_under_root: "true"
  co.elastic.logs/json.message_key: message
  co.elastic.logs/json.overwrite_keys: "true"
  co.elastic.logs/multiline.pattern: '^(([a-zA-z\-_0-9]+_(INFO|WARN|ERROR|DEBUG|FATAL), RequestID: )|([^\[]{1,6}((\[32m)|(\[31m)|(\[31;1m))(\d{2}:\d{2})\s+(INFO|WARN|ERROR|DEBUG|DPANIC|PANIC|FATAL)\s+)|(\d{4}[\/-]\d{2}[\/-]\d{1,2})|(panic:))'
  co.elastic.logs/multiline.negate: "true"
  co.elastic.logs/multiline.match: "after"
podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

configSecret: eyJjb3VudGVyLXNlcnZpY2UtbGl0ZSI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1jb3VudGVyLXNlcnZpY2UtbGl0ZS1teXNxbC5ucHJ2LnN0Zy5nZGVmZW5jZS5pbzozMzA2KS90cnVzdF9jb3VudGVyX3NlcnZpY2U/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIKfQ==

configSecretGiestream: eyJrYWZrYS11c2VyIjogIkxVRDRCVUdaRjZKS09ZNkIiLCJrYWZrYS1wYXNzd29yZCI6ICJXNng0bDBPeE5vNHFKUGlnRmE3aFpBMlBuRzJIUWFWRU80L2RLMk1CUlkvRmVPTm1VUjJNamVweGNxQjZhN3I4In0=

service:
  type: NodePort
  port: 8081

ingress:
  enabled: false
  #  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    # hosts:
    # - host: chart-example.local
    # paths: []
    #tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: 
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
   limits:
     cpu: 1000m
     memory: 256Mi
   requests:
     cpu: 100m
     memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 8
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector:
  stack: general

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    - labelSelector:
        matchExpressions:
        - key: app
          operator: In
          values:
          - counter-service-lite
      topologyKey: "kubernetes.io/hostname"
