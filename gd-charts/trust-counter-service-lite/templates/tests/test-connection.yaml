apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "counter-service-lite.fullname" . }}-test-connection"
  labels:
    {{- include "counter-service-lite.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "counter-service-lite.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
