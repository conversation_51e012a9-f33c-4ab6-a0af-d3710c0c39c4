apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "counter-service-lite.fullname" . }}
  labels:
    {{- include "counter-service-lite.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
      {{- include "counter-service-lite.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "counter-service-lite.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "counter-service-lite.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: COUNTER_SERVICE_LITE_BASE_CONF
            value: /var/config.json
          - name: DATA_CONF
            value: /go/src/gitlab.myteksi.net/trust/grab-defence-base/commons/data/config-ci.json
          - name: COUNTER_SERVICE_LITE_VAULT_FOLDER
            value: /vault/secrets
          - name: DOGSTATSD_ADDR
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          volumeMounts:
            - name: counter-service-lite-conf
              mountPath: /var/config.json
              subPath: config.json
            - name: counter-service-lite-secret-vol
              mountPath: /vault/secrets
          ports:
            - name: http
              containerPort: 8081
              protocol: TCP
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 20
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 6
            tcpSocket:
              port: 8081
          readinessProbe:
            initialDelaySeconds: 60
            periodSeconds: 20
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 6
            tcpSocket:
              port: 8081
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      volumes:
        - name: counter-service-lite-conf
          configMap:
            name: counter-service-lite-conf
        - name: counter-service-lite-secret-vol
          secret:
            secretName: counter-service-lite-secret
            items:
              - key: config-secret.json
                path: config-secret.json
              - key: config-secret-giestream.json
                path: config-secret-giestream.json

      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
