apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "kingpinx.fullname" . }}
  labels:
    {{- include "kingpinx.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
      {{- include "kingpinx.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "kingpinx.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "kingpinx.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      terminationGracePeriodSeconds: 30
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: BINARY_TAG
            value: {{ .Values.image.tag | default .Chart.AppVersion }}
          - name: KINGPINX_CONFIG
            value: /code/abuse/kingpinx/config.json
          - name: KINGPINX_CONFIG_SECRET
            value: /vault/secrets/config-secret.json
          - name: DOGSTATSD_ADDR
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          envFrom:
          - configMapRef:
              name: kingpinx-env
          volumeMounts:
            - name: config
              mountPath: /code/abuse/kingpinx/config.json
              subPath: config.json
          ports:
            - name: http
              containerPort: 8088
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health_check
              port: 8088
          readinessProbe:
            httpGet:
              path: /health_check
              port: 8088
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      volumes:
        - name: config
          configMap:
            name: kingpinx-configmap
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
