apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "mysql-partition-manager.fullname" . }}-test-connection"
  labels:
    {{- include "mysql-partition-manager.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "mysql-partition-manager.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
