{{- $outer := . -}}
{{- range $idx, $table := .Values.configs.tables }}
{{- with $outer -}}
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: {{ include "mysql-partition-manager.fullname" . }}-{{ $table.table_name  | replace "_" "-"  }}
  labels:
    {{- include "mysql-partition-manager.labels" . | nindent 4 }}
spec:
  successfulJobsHistoryLimit: 1
  schedule: {{ $table.schedule }}
  jobTemplate:
    spec:
      template:
        metadata:
        {{- with .Values.podAnnotations }}
          annotations:
            {{- toYaml . | nindent 12 }}
        {{- end }}
        spec:
          {{- with .Values.imagePullSecrets }}
          imagePullSecrets:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          serviceAccountName: {{ include "mysql-partition-manager.serviceAccountName" . }}
          securityContext:
            {{- toYaml .Values.podSecurityContext | nindent 12 }}
          containers:
            - name: {{ .Chart.Name }}
              securityContext:
                {{- toYaml .Values.securityContext | nindent 16 }}
              image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
              imagePullPolicy: {{ .Values.image.pullPolicy }}
              env:
                - name: ENV
                  value: "{{ .Values.configs.cluster }}"
                - name: RDS_INSTANCE
                  value: "{{ .Values.configs.rds_prefix }}-magnetox-mysql-{{ .Values.configs.tenant }}"
                - name: TENANT
                  value: "{{ .Values.configs.tenant }}"
                - name: TABLE_SCHEMA
                  value: "{{ $table.table_schema }}"
                - name: TABLE_NAME
                  value: "{{ $table.table_name }}"
                - name: RETAIN_PARTITIONS
                  value: "{{ $table.retain_partitions }}"
                - name: INTERVAL
                  value: "{{ $table.interval }}"
                - name: PARTITION_METHOD
                  value: "{{ $table.partition_method }}"
                - name: PARTITION_EXPRESSION
                  value: "{{ $table.partition_expression }}"
                - name: SLEEP_WAIT
                  value: "{{ $table.sleep_wait }}"
                - name: SELECT_PARTITION_COUNT
                  value: "{{ $table.select_partition_count }}"
              volumeMounts:
                - name: mysql-partition-manager-{{ .Values.configs.tenant }}-secret-vol
                  mountPath: /vault/secrets
          volumes:
            - name: mysql-partition-manager-{{ .Values.configs.tenant }}-secret-vol
              secret:
                secretName: mysql-partition-manager-{{ .Values.configs.tenant }}-secret
                items:
                  - key: config-secret.json
                    path: config-secret.json
          restartPolicy: OnFailure
          {{- with .Values.nodeSelector }}
          nodeSelector:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.affinity }}
          affinity:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.tolerations }}
          tolerations:
            {{- toYaml . | nindent 12 }}
          {{- end }}

---

  {{- end }}
  {{- end }}