# Default values for mysql-partition-manager.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/new/guardians/mysql-partition-manager
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v_1673232822_7cbcf2ca"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "guardians-read"

podAnnotations:
  co.elastic.logs/json.add_error_key: "true"
  co.elastic.logs/json.keys_under_root: "true"
  co.elastic.logs/json.message_key: message
  co.elastic.logs/json.overwrite_keys: "true"

configs:
  cluster: nstg
  rds_prefix: nstg
  tenant: guardian
  tables:
    - table_schema: magnetox
      table_name: verdicts
      retain_partitions: 5
      interval: WEEK
      partition_method: RANGE
      partition_expression: YEARWEEK(created_at)
      sleep_wait: 300
      schedule: 0 17 * * 5
      select_partition_count: 1
    - table_schema: magnetox
      table_name: checkpoints
      retain_partitions: 5
      interval: WEEK
      partition_method: RANGE
      partition_expression: YEARWEEK(created_at)
      sleep_wait: 300
      schedule: 0 17 * * 5
      select_partition_count: 1
    - table_schema: magnetox
      table_name: hydras
      retain_partitions: 5
      interval: WEEK
      partition_method: RANGE
      partition_expression: YEARWEEK(created_at)
      sleep_wait: 300
      schedule: 0 17 * * 5
      select_partition_count: 1
    - table_schema: magnetox
      table_name: no_hydra_payloads
      retain_partitions: 5
      interval: WEEK
      partition_method: RANGE
      partition_expression: YEARWEEK(created_at)
      sleep_wait: 300
      schedule: 0 17 * * 5
      select_partition_count: 1

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector:
  stack: general

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app
              operator: In
              values:
                - mysql-partition-manager
        topologyKey: "kubernetes.io/hostname"

configSecret:
  ewogICAgImd1YXJkaWFuLm1hc3Rlci5lbmRwb2ludCI6ICJuc3RnLW1hZ25ldG94LW15c3FsLWd1YXJkaWFuLm5wcnYuc3RnLmdkZWZlbmNlLmlvIiwKICAgICJndWFyZGlhbi5tYXN0ZXIuZGF0YWJhc2UiOiAibWFnbmV0b3giLAogICAgImd1YXJkaWFuLm1hc3Rlci51c2VybmFtZSI6ICJnZGFkbWluIiwKICAgICJndWFyZGlhbi5tYXN0ZXIucGFzc3dvcmQiOiAiTmpSNEh5dmFHZE1HIgp9