# scrat deployment

you can use this way to upgrade the scrat:
1. go to defence folder(under the helm/defence), if you are in this folder
```
cd ../..
```
2. use this command to upgrade the change. change the kubeconfig with your kubeconfig path, like removing --kubeconfig or add your kubeconfig.
```
 helm upgrade scrat ./templates/scrat/ -f ./nstg/scrat/values.yaml --namespace defence --values ./nstg/scrat/config_variables.yaml --kubeconfig ~/.kube-nstg/config
```
or for prod:
```
 helm upgrade scrat ./templates/scrat/ -f ./nprd-sg/scrat/values.yaml --namespace defence --values ./nprd-sg/scrat/config_variables.yaml --kubeconfig ~/.kube-nprd/config
```
