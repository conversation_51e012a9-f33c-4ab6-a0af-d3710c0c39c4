# Default values for scrat.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/defence/scrat
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v_d5e318f4"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

env: "stg"

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "defence-read"

podAnnotations:

configSecret: eyJzY3JhdC1teXNxbCI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1zY3JhdC1teXNxbC5ucHJ2LnN0Zy5nZGVmZW5jZS5pbzozMzA2KS9zY3JhdD9wYXJzZVRpbWU9dHJ1ZSZsb2M9VVRDIiwgInNjcmF0LW15c3FsLXJlcGxpY2EiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKG5zdGctc2NyYXQtbXlzcWwubnBydi5zdGcuZ2RlZmVuY2UuaW86MzMwNikvc2NyYXQ/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIsICJzY3JhdC1teXNxbC1ndWFyZGlhbiI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1zY3JhdC1teXNxbC5ucHJ2LnN0Zy5nZGVmZW5jZS5pbzozMzA2KS9zY3JhdD9wYXJzZVRpbWU9dHJ1ZSZsb2M9VVRDIiwgInNjcmF0LW15c3FsLXNlY29uZC1jbGllbnQiOiAiZ2RhZG1pbjpOalI0SHl2YUdkTUdAdGNwKG5zdGctc2NyYXQtbXlzcWwubnBydi5zdGcuZ2RlZmVuY2UuaW86MzMwNikvc2NyYXRfY2xpZW50Mj9wYXJzZVRpbWU9dHJ1ZSZsb2M9VVRDIiwgInNjcmF0LW15c3FsLWdyYWJnaWZ0cyI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1zY3JhdC1teXNxbC1ncmFiZ2lmdHMubnBydi5zdGcuZ2RlZmVuY2UuaW86MzMwNikvc2NyYXQ/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIsICJzY3JhdC1teXNxbC1yZXBsaWNhLWd1YXJkaWFuIjogImdkYWRtaW46TmpSNEh5dmFHZE1HQHRjcChuc3RnLXNjcmF0LW15c3FsLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL3NjcmF0P3BhcnNlVGltZT10cnVlJmxvYz1VVEMiLCAic2NyYXQtbXlzcWwtcmVwbGljYS1zZWNvbmQtY2xpZW50IjogImdkYWRtaW46TmpSNEh5dmFHZE1HQHRjcChuc3RnLXNjcmF0LW15c3FsLm5wcnYuc3RnLmdkZWZlbmNlLmlvOjMzMDYpL3NjcmF0X2NsaWVudDI/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyIsICJzY3JhdC1teXNxbC1yZXBsaWNhLWdyYWJnaWZ0cyI6ICJnZGFkbWluOk5qUjRIeXZhR2RNR0B0Y3AobnN0Zy1zY3JhdC1teXNxbC1ncmFiZ2lmdHMubnBydi5zdGcuZ2RlZmVuY2UuaW86MzMwNikvc2NyYXQ/cGFyc2VUaW1lPXRydWUmbG9jPVVUQyJ9
configSecretKafkastream: eyJrYWZrYS11c2VyIjogIkxVRDRCVUdaRjZKS09ZNkIiLCJrYWZrYS1wYXNzd29yZCI6ICJXNng0bDBPeE5vNHFKUGlnRmE3aFpBMlBuRzJIUWFWRU80L2RLMk1CUlkvRmVPTm1VUjJNamVweGNxQjZhN3I4In0=


podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 8081

ingress:
  enabled: false
  # annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  # hosts:
  #   - host: chart-example.local
  #     paths: []
  # tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
   limits:
     cpu: 1000m
     memory: 192Mi
   requests:
     cpu: 100m
     memory: 192Mi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 8
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector:
  stack: general

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    - labelSelector:
        matchExpressions:
        - key: app
          operator: In
          values:
          - scrat
      topologyKey: "kubernetes.io/hostname"
