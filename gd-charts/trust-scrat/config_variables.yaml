
### this file holds the configs used in config.json for the appconfig.
### this is for the dynamic config, use json format
dynamicConfig: |
  {
    "timer:load:config_cache:minutes": 5,
    "entity_state:read:batch_size": 200,
    "tenant:enabledefaultmysql": true,
    "tenant:enablemysqldynamodoublewrite": false,
    "tenant:enabledefaultmysqlconfigs":  true,
    "tes.stream.rollout": 100,
    "archivist:entitystatelist": "email_&_DenyList, email_&_AllowList, phone_number_&_DenyList, phone_number_&_AllowList",
    "cookie.domain": "grab.com"
  }

### this part is for the app config
mode: "staging"

### multi-client:
clientList:
  - guardian
  - second-client
  - grabgifts

structuredlogger: |
  {
    "syslogTag":"structuredlog.scrat",
    "workerCount":2,
    "bufferSize":500,
    "logLevel":1,
    "stacktraceLevel":4,
    "logFormat":"cgls",
    "development":true
  }

### mysql related config
mysqlSetting:
  maxIdle: 2
  maxOpen: 10

### redis related config
redisClusterConfig: |
  {
    "clusterNodeAddrs":[
      "nstg-defence-api-redis.nprv.stg.gdefence.io:6379"
    ],
    "poolSize":10,
    "readTimeoutInMs":150,
    "writeTimeoutInMs":200,
    "readOnlyFromSlaves":false
  }

### stream related configs
# tes stream for scrat
tesReaderConfig: |
  {
    "disable":false,
    "broker": [
      "stg-gd-singapore.coban-partner:30100"
    ],
    "groupID":"nstg-consumer-app-scrat",
    "topic":"tesstream",
    "appName":"scrat",
    "dtoName":"TrustEntityState",
    "packageName":"hello_world",
    "offset":"newest",
    "workers":2,
    "MaxInFlightPerConn": 4,
    "enableSASL": false,
    "enableTLS": false
  }

# tde stream for archivist
tdeStreamConfig: |
  {
    "disable":false,
    "broker": [
      "stg-gd-singapore.coban-partner:30100"
    ],
    "groupID":"nstg-consumer-app-scrat",
    "topic":"tdestream",
    "appName":"archivist",
    "dtoName":"TisDecisionEvent",
    "packageName":"streams.tis.trust.tdestream",
    "offset":"newest",
    "workers":2,
    "MaxInFlightPerConn": 4,
    "enableSASL": false,
    "enableTLS": false
  }

