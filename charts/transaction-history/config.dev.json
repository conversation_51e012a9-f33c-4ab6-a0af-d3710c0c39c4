{"name": "transaction-history Service", "serviceName": "transaction-history", "host": "0.0.0.0", "port": 8080, "env": "dev", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 10000}, "slaveCircuitBreaker": {"timeoutInMs": 10000}}}, "sqsConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/dev-backend-transaction-history-sqs20231210084151476500000004", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 1, "waitTimeSeconds": 10}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "peConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-payment-engine-tx", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "development-transaction-history", "enable": true}, "dcConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-deposits-posting-batch-event", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "development-transaction-history"}, "interestAggKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-deposits-posting-batch-event", "clusterType": "critical", "enableTLS": true, "initOffset": "newest", "clientID": "development-transaction-history", "enable": true, "consumerGroupID": "dev-interest-aggregate-consumerID"}, "dbConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-deposits-balance-event", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "development-transaction-history-client"}, "digicardTxnKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "digicard-txn-transaction-history-dev", "clusterType": "critical", "topicName": "dev-digicard-txn-tx", "enableTLS": true, "initOffset": "oldest"}, "loanCoreTxKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "transaction-history-dev", "clusterType": "critical", "topicName": "dev-loan-core-tx", "enableTLS": true, "initOffset": "oldest", "enable": true}, "loanExpTxKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "transaction-history-dev", "clusterType": "critical", "topicName": "dev-loan-exp-tx", "enableTLS": true, "initOffset": "oldest", "enable": true}, "customerMasterConfig": {"baseURL": "http://customer-master.onboarding.svc.cluster.local"}, "customerExperienceConfig": {"baseURL": "http://customer-experience.onboarding.svc.cluster.local"}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local"}, "paymentExperienceConfig": {"baseURL": "http://payment-experience.payments.svc.cluster.local"}, "transactionStatementsConfig": {"baseURL": "http://transaction-statements.core-banking.svc.cluster.local", "statementReadyByDays": 28, "_statementReadyByDays": "All statements will be ready by x days"}, "transactionHistoryClientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "transactionHistoryServiceConfig": {"defaultCurrency": "MYR", "pastMonthsThresholdForCalendarActivity": 12, "transactionsSearchDurationLimitInMonths": 6, "apiRequestTimeoutInSec": 30}, "iconConfig": {"interestEarn": "https://assets.dev.g-bank.app/txn-history/interest_earn.png", "bankAdjustment": "https://assets.dev.g-bank.app/txn-history/bank_adjustment.png", "pocketFunding": "https://assets.dev.g-bank.app/txn-history/pocket_funding.png", "mainPocketWithdrawal": "https://assets.dev.g-bank.app/txn-history/main_pocket_withdrawal.png", "pocketPocketWithdrawal": "https://assets.dev.g-bank.app/txn-history/pocket_pocket_withdrawal.png", "savingsPocketTransfer": "https://assets.dev.g-bank.app/txn-history/pocket_moneyin.png", "savingsPocketWithdrawal": "https://assets.dev.g-bank.app/txn-history/pocket_withdrawal.png", "grab": "https://assets.dev.g-bank.app/txn-history/grab.png", "rewards": "https://assets.dev.g-bank.app/txn-history/rewards.png", "withdrawal": "https://assets.dev.bankfama.net/dev/withdrawal.png", "transferIn": "https://assets.dev.g-bank.app/txn-history/main_money_in.png", "transferOut": "https://assets.dev.g-bank.app/txn-history/main_money_out.png", "transferFee": "https://assets.dev.bankfama.net/dev/transfer_money.png", "interestPayout": "https://assets.dev.g-bank.app/txn-history/interest_earn.png", "taxOnInterest": "https://assets.dev.bankfama.net/dev/tax_on_interest.png", "adjustment": "https://assets.dev.g-bank.app/txn-history/gxb.png", "defaultTransaction": "https://assets.dev.bankfama.net/dev/UserDefault.png", "mastercard": "https://assets.dev.g-bank.app/txn-history/txn_card_icon.png", "merchant": "https://assets.dev.g-bank.app/txn-history/merchant_icon.png", "lendingDrawdown": "https://assets.dev.g-bank.app/lending/lending_drawndown.png", "lendingRepayment": "https://assets.dev.g-bank.app/lending/lending_withdraw.png", "insurance": "https://assets.dev.g-bank.app/txn-history/insurance_icon.png"}, "workerConfig": {"updateInterestAggregateWorkerConf": {"lockDurationInMinutes": 1400, "cronExpression": "00 17 * * *", "enabled": false, "parentBatchSubPaginationInSeconds": 3600, "refreshAggregation": false, "updateBatchSizeInRow": 1000}}, "populateInterestAggregateOneTimer": {"startDate": "2024-04-12", "endDate": "2024-04-15", "runOneTimer": true, "batchSizeInDays": 1, "lockDurationInMinutes": 5, "refreshAggregation": false, "updateBatchSizeInRow": 1000}, "snowflakeConfig": {"dsnOPSReader": ""}, "stm453TransactionInfoFeatureFlag": {"enabled": false}, "digicardFeatureFlag": {"enabled": true}, "featureFlags": {"standaloneBalProcessor": {"enabled": true}, "blockingBalProcessor": {"enabled": true}, "insertTransactionsByBatch": {"enabled": true}, "InsertTransactionsByBatch": {"enabled": true}, "replicaRead": {"enabled": true}, "retryableDepositCoreStream": {"enabled": true}, "retryableDepositBalanceStream": {"enabled": true}, "checkStatementStatus": {"enabled": true}, "batchifyDailyInterestAgg": {"enabled": true}, "batchifyOneTimeInterestAgg": {"enabled": true}, "updatePostingInstructionBatchForLending": {"enabled": true}, "enableTransactionsSearchDurationFilter": {"enabled": true}, "depositCoreStream": {"enabled": true}, "depositBalanceStream": {"enabled": true}, "bizAuthorisation": {"enabled": true}, "enableSearchTransactionLimit": {"enabled": true}, "enableBizFlexiCredit": {"enabled": true}, "enableExhaustiveCursorPagination": {"enabled": true}, "enableExhaustiveCursorPaginationForCX": {"enabled": true}, "enableAuthzWithProfileID": {"enabled": true}, "isRetryableInterestAggStreamEnabled": {"enabled": true}, "enableInterestAggregateV2": {"enabled": true}, "enableBoostPocketNameQuery": {"enabled": true}}, "tenant": "MY", "locale": {"currency": "MYR", "language": "en"}, "pairingServiceConfig": {"serviceName": "pairing-service", "baseURL": "http://pairing-service.payments.svc.cluster.local", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "transactionsSearchFilterDurationInMonths": 6, "exhaustivePaginationConfig": {"maxCursorLimit": 10, "executionSleepDurationInMs": 500}, "cachingConfig": {"ttl": 30, "redisConfig": {"addr": "clustercfg.dbmy-dev-backend-ec-transaction-history.o9f56r.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 60, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}}, "interestAggregationConfig": {"retentionDay": 5, "startMonth": "2024-06", "excludeAccounts": {"*********": true}}, "populateInterestAggregateV2OneTimerConfig": {"batchSize": 100, "lockDurationInMinutes": 30, "checkpoint": "INTEREST_AGGREGATE_WORKER_CHECKPOINT_1", "cronExpression": "*/10 * * * *", "runPopulate": false, "runCrossCheck": false}}