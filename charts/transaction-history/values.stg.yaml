env: stg
configName: config.stg.json

argorollouts:
  disableDeployment: true
  analysis:
    enable: false
  rolloutTrafficRoutingEnabled: true
rollout: true

gateway:
  enabled: true
  annotations: {}
  hosts:
    - "internal.cb.stg.g-bank.app"
  tls: true

serviceAccount:
  name: transaction-history
  annotations: {
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/eks/cluster/stg-cb-01/transaction-history20240214132249349700000005"
  }

resources:
#  gxs cpu min is 200m and max is 500m, we'll take the average
  limits:
    memory: 512Mi
  requests:
    cpu: 350m
    memory: 512Mi

autoscaling:
  enabled: true
  minReplicas: 10
  maxReplicas: 15
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

envVars:
  MYSQL_HOST: "transaction-history-db.cb.stg.g-bank.app"
  MYSQL_HOST_REPLICA: "transaction-history-db.cb.stg.g-bank.app"
  SECRET_CONF: /vault/secrets/
  DB_NAME: transaction_history_stg
  SERVICE_CONF: /config_files/config.json
  LOCALISATION_PATH: /localise

podAnnotations:
  vault.hashicorp.com/agent-inject-secret-db_creds.json: "database/creds/stg-cb-transactionhistory-rds-mysql-dba"
  vault.hashicorp.com/role: "transaction-history"
  vault.hashicorp.com/agent-inject-template-db_creds.json: |
    {{ with secret "database/creds/stg-cb-transactionhistory-rds-mysql-dba" -}}
      {
        "MYSQL_USERNAME": "{{ .Data.username }}",
        "MYSQL_PASSWORD": "{{ .Data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-redis_creds.json: "kv/data/security/tf/stg/transaction-history/ec/auth"
  vault.hashicorp.com/agent-inject-template-redis_creds.json: |
    {{ with secret "kv/data/security/tf/stg/transaction-history/ec/auth" -}}
      {
        "REDIS_PASSWORD": "{{ .Data.auth_token }}"
      }
    {{- end }}

dailyInterestAggJob:
  enabled: true
  schedule: "0 17 * * *"
  cpu: "100m"
  memory: "512Mi"
oneTimeInterestAggJob:
  enabled: true
  schedule: "0 19 * * *"
  cpu: "100m"
  memory: "1Gi"
  suspend: true
populateInterestAggregateV2OneTimerJob:
  enabled: true
  schedule: "30 20 * * *"
  cpu: "100m"
  memory: "512Mi"
  suspend: true

authorizationpolicy:
  enabled: true
  rules:
    #    the following istio-ingress source is only for dev/staging env, it should not be copied to prod
    - sources: [ "cluster.local/ns/istio-ingress/sa/istio-ingressgateway-service-account" ]
    - sources: [ "cluster.local/ns/sentry-t6/sa/sentry-t6" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v2/transactions/list", "/v2/account-calendar-activity", "/v2/transactions/get", "/v1/pocket-total-interest-earned", "/v1/casa-total-interest-earned", "/v1/lending/transaction-detail", "/v1/lending/transactions-search"]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/operations/sa/customer-portal" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v2/transactions/list", "/v2/transactions/get", "/v1/casa-transactions-summary", "/v1/internal/transactions/get", "/v1/internal/transactions/list", "/v1/casa-total-interest-earned",  "/v1/transactions-search-cx", "/v1/lending/transactions-search-cx", "/v1/lending/transaction-detail-cx"]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/core-banking/sa/transaction-statements" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/internal/transactions/get" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/core-banking/sa/statements-gen-worker" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/loki/sa/loki" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/casa-total-interest-earned", "/v1/casa-transactions-summary" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/partner-gateway/sa/sentry-partner-t6" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/lending/transaction-detail-cx"]
    - sources: ["cluster.local/ns/payments/sa/payment-config"]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/internal/transactions/get" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
restarter:
  enabled: true
  # cron runs at 1:30am every day
  schedule: "30 17 * * *"
