env: dev
configName: config.dev.json

argorollouts:
  disableDeployment: true
  analysis:
    enable: false
  rolloutTrafficRoutingEnabled: true
rollout: true

gateway:
  enabled: true
  annotations: { }
  hosts:
    - "backend.dev.g-bank.app"
  tls: true

serviceAccount:
  annotations: {
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/eks/cluster/dev-backend-01/transaction-history20231210084151475800000003"
  }
  name: transaction-history

autoscaling:
  enabled: true

envVars:
  MYSQL_HOST: "dev-backend-transactionhistory-rds-mysql.cdnmfgps5qby.ap-southeast-1.rds.amazonaws.com"
  MYSQL_HOST_REPLICA: "dev-backend-transactionhistory-rds-mysql.cdnmfgps5qby.ap-southeast-1.rds.amazonaws.com"
  SECRET_CONF: /vault/secrets/
  DB_NAME: transaction_history_dev
  SERVICE_CONF: /config_files/config.json
  LOCALISATION_PATH: /localise

podAnnotations:
  vault.hashicorp.com/agent-inject-secret-db_creds.json: "database/creds/dev-backend-transactionhistory-rds-mysql-dba"
  vault.hashicorp.com/role: "transaction-history"
  vault.hashicorp.com/agent-inject-template-db_creds.json: |
    {{ with secret "database/creds/dev-backend-transactionhistory-rds-mysql-dba" -}}
      {
        "MYSQL_USERNAME": "{{ .Data.username }}",
        "MYSQL_PASSWORD": "{{ .Data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-redis_creds.json: "kv/data/security/tf/dev/transaction-history/ec/auth"
  vault.hashicorp.com/agent-inject-template-redis_creds.json: |
    {{ with secret "kv/data/security/tf/dev/transaction-history/ec/auth" -}}
      {
        "REDIS_PASSWORD": "{{ .Data.auth_token }}"
      }
    {{- end }}
dailyInterestAggJob:
  enabled: true
  schedule: "30 18 * * *"
  cpu: "100m"
  memory: "512Mi"
oneTimeInterestAggJob:
  enabled: true
  schedule: "30 20 * * *"
  cpu: "100m"
  memory: "512Mi"
  suspend: true
populateInterestAggregateV2OneTimerJob:
  enabled: true
  schedule: "30 20 * * *"
  cpu: "100m"
  memory: "512Mi"
  suspend: true

authorizationpolicy:
  enabled: true
  rules:
    #    the following istio-ingress source is only for dev/staging env, it should not be copied to prod
    - sources: [ "cluster.local/ns/istio-ingress/sa/istio-ingressgateway-service-account" ]
    - sources: [ "cluster.local/ns/sentry-t6/sa/sentry-t6" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v2/transactions/list", "/v2/account-calendar-activity", "/v2/transactions/get", "/v1/pocket-total-interest-earned", "/v1/casa-total-interest-earned", "/v1/lending/transaction-detail", "/v1/lending/transactions-search"]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/operations/sa/customer-portal" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v2/transactions/list", "/v2/transactions/get", "/v1/casa-transactions-summary", "/v1/internal/transactions/get", "/v1/internal/transactions/list", "/v1/casa-total-interest-earned",  "/v1/transactions-search-cx", "/v1/lending/transactions-search-cx", "/v1/lending/transaction-detail-cx"]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/core-banking/sa/transaction-statements" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/internal/transactions/get" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/core-banking/sa/statements-gen-worker" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/loki/sa/loki" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/casa-total-interest-earned", "/v1/casa-transactions-summary" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/partner-gateway/sa/sentry-partner-t6" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/lending/transaction-detail-cx"]
    - sources: ["cluster.local/ns/payments/sa/payment-config"]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/internal/transactions/get" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
restarter:
  enabled: true
  # cron runs at 1:30am every day
  schedule: "30 17 * * *"
