{"name": "transaction-history Service", "serviceName": "transaction-history", "host": "0.0.0.0", "port": 8080, "env": "stg", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 10000, "maxConcurrentReq": 100, "tag": "type:rds.masterCircuitBreaker"}, "slaveCircuitBreaker": {"timeoutInMs": 20000, "maxConcurrentReq": 100, "tag": "type:rds.slaveCircuitBreaker"}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "peConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "stg-payment-engine-tx", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "development-transaction-history", "enable": true}, "dcConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "stg-deposits-posting-batch-event", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "development-transaction-history"}, "dbConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "stg-deposits-balance-event", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "development-transaction-history-client"}, "digicardTxnKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "digicard-txn-transaction-history-stg-new", "clusterType": "critical", "topicName": "stg-digicard-txn-tx", "enableTLS": true, "initOffset": "oldest"}, "loanCoreTxKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "transaction-history-stg", "clusterType": "critical", "topicName": "stg-loan-core-tx", "enableTLS": true, "initOffset": "oldest", "enable": true}, "loanExpTxKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "transaction-history-stg", "clusterType": "critical", "topicName": "stg-loan-exp-tx", "enableTLS": true, "initOffset": "oldest", "enable": true}, "interestAggKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "stg-deposits-posting-batch-event", "clusterType": "critical", "enableTLS": true, "initOffset": "newest", "clientID": "transaction-history-stg", "enable": true, "consumerGroupID": "stg-interest-aggregate-consumerID"}, "customerMasterConfig": {"baseURL": "http://customer-master.onboarding.svc.cluster.local"}, "customerExperienceConfig": {"baseURL": "http://customer-experience.onboarding.svc.cluster.local"}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local"}, "paymentExperienceConfig": {"baseURL": "http://payment-experience.payments.svc.cluster.local"}, "transactionHistoryClientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "transactionHistoryServiceConfig": {"defaultCurrency": "MYR", "pastMonthsThresholdForCalendarActivity": 12, "maxRetryForBalanceStream": 15, "sleepDurationForBalanceStream": 1, "transactionsSearchDurationLimitInMonths": 6, "apiRequestTimeoutInSec": 30}, "transactionStatementsConfig": {"baseURL": "http://transaction-statements.core-banking.svc.cluster.local", "statementReadyByDays": 29, "_statementReadyByDays": "All statements will be ready by x days"}, "iconConfig": {"interestEarn": "https://assets.stg.g-bank.app/txn-history/interest_earn.png", "bankAdjustment": "https://assets.stg.g-bank.app/txn-history/bank_adjustment.png", "pocketFunding": "https://assets.stg.g-bank.app/txn-history/pocket_funding.png", "mainPocketWithdrawal": "https://assets.stg.g-bank.app/txn-history/main_pocket_withdrawal.png", "pocketPocketWithdrawal": "https://assets.stg.g-bank.app/txn-history/pocket_pocket_withdrawal.png", "savingsPocketTransfer": "https://assets.stg.g-bank.app/txn-history/pocket_moneyin.png", "savingsPocketWithdrawal": "https://assets.stg.g-bank.app/txn-history/pocket_withdrawal.png", "grab": "https://assets.stg.g-bank.app/txn-history/grab.png", "rewards": "https://assets.stg.g-bank.app/txn-history/rewards.png", "withdrawal": "https://assets.dev.bankfama.net/dev/withdrawal.png", "transferIn": "https://assets.stg.g-bank.app/txn-history/main_money_in.png", "transferOut": "https://assets.stg.g-bank.app/txn-history/main_money_out.png", "transferFee": "https://assets.dev.bankfama.net/dev/transfer_money.png", "interestPayout": "https://assets.stg.g-bank.app/txn-history/interest_earn.png", "taxOnInterest": "https://assets.dev.bankfama.net/dev/tax_on_interest.png", "adjustment": "https://assets.stg.g-bank.app/txn-history/gxb.png", "defaultTransaction": "https://assets.dev.bankfama.net/dev/UserDefault.png", "mastercard": "https://assets.stg.g-bank.app/txn-history/txn_card_icon.png", "merchant": "https://assets.stg.g-bank.app/txn-history/merchant_icon.png", "lendingDrawdown": "https://assets.stg.g-bank.app/lending/lending_drawndown.png", "lendingRepayment": "https://assets.stg.g-bank.app/lending/lending_withdraw.png", "insurance": "https://assets.stg.g-bank.app/txn-history/insurance_icon.png"}, "workerConfig": {"updateInterestAggregateWorkerConf": {"lockDurationInMinutes": 1400, "cronExpression": "00 17 * * *", "_cronExpression.comment": "This only works in legacy worker mode", "enable": false, "_enable.comment": "this config is deprecated", "batchCOPInSecond": 1, "batchSizeInRow": 1000, "parentBatchSubPaginationInSeconds": 3600, "refreshAggregation": false, "updateBatchSizeInRow": 1000}}, "sqsConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/stg-cb-transaction-history-sqs20240214132249351500000006", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 1, "waitTimeSeconds": 10}, "populateInterestAggregateOneTimer": {"startDate": "2023-03-03", "endDate": "2024-05-18", "runOneTimer": true, "batchSizeInDays": 1, "lockDurationInMinutes": 500, "refreshAggregation": true, "updateBatchSizeInRow": 1000}, "snowflakeConfig": {"dsnOPSReader": ""}, "stm453TransactionInfoFeatureFlag": {"enabled": false}, "digicardFeatureFlag": {"enabled": true}, "featureFlags": {"insertTransactionsByBatch": {"enabled": true}, "InsertTransactionsByBatch": {"enabled": true}, "replicaRead": {"enabled": true}, "checkStatementStatus": {"enabled": true}, "retryableDepositCoreStream": {"enabled": true}, "retryableDepositBalanceStream": {"enabled": true}, "batchifyDailyInterestAgg": {"enabled": true}, "batchifyOneTimeInterestAgg": {"enabled": true}, "updatePostingInstructionBatchForLending": {"enabled": true}, "enableTransactionsSearchDurationFilter": {"enabled": true}, "depositCoreStream": {"enabled": true}, "depositBalanceStream": {"enabled": true}, "bizAuthorisation": {"enabled": true}, "enableSearchTransactionLimit": {"enabled": true}, "enableExhaustiveCursorPagination": {"enabled": true}, "enableExhaustiveCursorPaginationForCX": {"enabled": true}, "enableAuthzWithProfileID": {"enabled": true}, "isRetryableInterestAggStreamEnabled": {"enabled": true}, "enableInterestAggregateV2": {"enabled": true}, "enableBoostPocketNameQuery": {"enabled": true}}, "tenant": "MY", "locale": {"currency": "MYR", "language": "en"}, "pairingServiceConfig": {"serviceName": "pairing-service", "baseURL": "http://pairing-service.payments.svc.cluster.local", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "transactionsSearchFilterDurationInMonths": 6, "exhaustivePaginationConfig": {"maxCursorLimit": 30, "executionSleepDurationInMs": 500}, "cachingConfig": {"ttl": 30, "redisConfig": {"addr": "clustercfg.dbmy-stg-cb-ec-transaction-history.esalbp.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 60, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}}, "interestAggregationConfig": {"retentionDay": 5, "startMonth": "2024-06", "excludeAccounts": {"*********": true}}, "populateInterestAggregateV2OneTimerConfig": {"batchSize": 1000, "lockDurationInMinutes": 30, "checkpoint": "", "cronExpression": "*/10 * * * *", "runPopulate": false, "runCrossCheck": false}}