apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "transaction-history.fullname" . }}-test-connection"
  labels:
    {{- include "transaction-history.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: 303083450960.dkr.ecr.ap-southeast-3.amazonaws.com/public-images:busybox-1.33.1
      command: ['wget']
      args: ['{{ include "transaction-history.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
