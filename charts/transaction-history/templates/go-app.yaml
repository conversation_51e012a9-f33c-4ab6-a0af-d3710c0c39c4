{{- define "transaction-history.deployment" -}}
{{- end -}}

{{- include "go-app-lib.deployment" (list . "transaction-history.deployment") }}
---
{{- define "transaction-history.rollout" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rollout" (list . "transaction-history.rollout") }}
---
{{- end -}}

{{- define "transaction-history.rolloutvirtualservice" -}}
{{- end -}}

{{- if and .Values.rollout .Values.argorollouts.rolloutTrafficRoutingEnabled  -}}
{{- include "go-app-lib.rolloutvirtualservice" (list . "transaction-history.rolloutvirtualservice") }}
---
{{- end -}}

{{- define "transaction-history.rolloutdestinationrule" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rolloutdestinationrule" (list . "transaction-history.rolloutdestinationrule") }}
---
{{- end -}}

{{- define "transaction-history.analysistemplate" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.analysistemplate" (list . "transaction-history.analysistemplate") }}
---
{{- end -}}

{{- define "transaction-history.service" -}}
{{- end -}}

{{- include "go-app-lib.service" (list . "transaction-history.service") }}
---
{{- define "transaction-history.hpa" -}}
{{- end -}}

{{- if .Values.autoscaling.enabled }}
{{- include "go-app-lib.hpa" (list . "transaction-history.hpa") }}
---
{{- end -}}

{{- define "transaction-history.ingress" -}}
{{- end -}}

{{- if .Values.ingress.enabled -}}
{{- include "go-app-lib.ingress" (list . "transaction-history.ingress") }}
---
{{- end -}}

{{- define "transaction-history.serviceaccount" -}}
{{- end -}}

{{- if .Values.serviceAccount.create -}}
{{- include "go-app-lib.serviceaccount" (list . "transaction-history.serviceaccount") }}
---
{{- end -}}

{{- define "transaction-history.gateway" -}}
{{- end -}}

{{- define "transaction-history.virtualservice" -}}
{{- end -}}

{{- if .Values.gateway.enabled -}}
{{- include "go-app-lib.gateway" (list . "transaction-history.gateway") }}
---
{{- include "go-app-lib.virtualservice" (list . "transaction-history.virtualservice") }}
---
{{- end -}}

{{- define "transaction-history.authorizationpolicy" -}}
{{- end -}}

{{- if .Values.authorizationpolicy.enabled -}}
{{- include "go-app-lib.authorizationpolicy" (list . "transaction-history.authorizationpolicy") }}
---
{{- end -}}

{{- define "transaction-history.podDisruptionBudget" -}}
{{- end -}}

{{- if and (.Values.podDisruptionBudget.enabled) (ne .Values.env "dev") -}}
{{- include "go-app-lib.podDisruptionBudget" (list . "transaction-history.podDisruptionBudget") }}
---
{{- end }}

{{- define "transaction-history.restartercron" -}}
{{- end -}}

{{- define "transaction-history.restarterrole" -}}
{{- end -}}

{{- define "transaction-history.restarterrolebinding" -}}
{{- end -}}

{{- define "transaction-history.restarterserviceaccount" -}}
{{- end -}}

{{- if and .Values.restarter .Values.restarter.enabled -}}
{{- include "go-app-lib.restartercron" (list . "transaction-history.restartercron") }}
---
{{- include "go-app-lib.restarterrole" (list . "transaction-history.restarterrole") }}
---
{{- include "go-app-lib.restarterrolebinding" (list . "transaction-history.restarterrolebinding") }}
---
{{- include "go-app-lib.restarterserviceaccount" (list . "transaction-history.restarterserviceaccount") }}
---
{{- end -}}