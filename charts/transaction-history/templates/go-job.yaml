{{- define "cron-resources.tpl" -}}
requests:
  memory: {{ .memory }}
  cpu: {{ .cpu }}
limits:
  memory: {{ .memory }}
{{- end }}

{{- define "daily-interest-agg-cron.tpl" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "ENABLE_DAILY_INTEREST_AGG_WORKER" "value" (ternary "true"  "false" .Values.dailyInterestAggJob.enabled) ) }}
{{ $env = append $env (dict "name" "WORKER_MODE" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $resources:= (include "cron-resources.tpl" .Values.dailyInterestAggJob) | fromYaml }}
{{ $_ := set $container "env" $env}}
{{ $_1 := set $container "resources" $resources }}
metadata:
  name: {{ include "go-app-lib.fullname" . }}-daily-interest-agg
spec:
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            tags.datadoghq.com/alias: daily-interest-agg-worker
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
          restartPolicy: Never
      ttlSecondsAfterFinished: 600
  schedule: {{ .Values.dailyInterestAggJob.schedule }}
  suspend: {{  ternary (hasKey .Values.dailyInterestAggJob "suspend") .Values.dailyInterestAggJob.suspend false }}
{{- end }}
{{- if and .Values.dailyInterestAggJob .Values.dailyInterestAggJob.enabled -}}
{{- include "go-app-lib.cron" (list . "daily-interest-agg-cron.tpl") }}
---
{{- end }}

{{- define "one-time-interest-agg-cron.tpl" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "ENABLE_ONE_TIME_INTEREST_AGG_WORKER" "value" (ternary "true"  "false" .Values.oneTimeInterestAggJob.enabled) ) }}
{{ $env = append $env (dict "name" "WORKER_MODE" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $resources:= (include "cron-resources.tpl" .Values.oneTimeInterestAggJob) | fromYaml }}
{{ $_ := set $container "env" $env}}
{{ $_1 := set $container "resources" $resources }}
metadata:
  name: {{ include "go-app-lib.fullname" . }}-one-time-interest-agg
spec:
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            tags.datadoghq.com/alias: one-time-interest-agg-worker
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
          restartPolicy: Never
      ttlSecondsAfterFinished: 600
  schedule: {{ .Values.oneTimeInterestAggJob.schedule }}
  suspend: {{  ternary (hasKey .Values.oneTimeInterestAggJob "suspend") .Values.oneTimeInterestAggJob.suspend false }}
{{- end }}
{{- if and .Values.oneTimeInterestAggJob .Values.oneTimeInterestAggJob.enabled -}}
{{- include "go-app-lib.cron" (list . "one-time-interest-agg-cron.tpl") }}
---
{{- end }}


{{- define "populate-interest-agg-v2-cron.tpl" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "ENABLE_POPULATE_INTEREST_AGG_V2_WORKER" "value" (ternary "true"  "false" .Values.populateInterestAggregateV2OneTimerJob.enabled) ) }}
{{ $env = append $env (dict "name" "WORKER_MODE" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $resources:= (include "cron-resources.tpl" .Values.populateInterestAggregateV2OneTimerJob) | fromYaml }}
{{ $_ := set $container "env" $env}}
{{ $_1 := set $container "resources" $resources }}
metadata:
  name: {{ include "go-app-lib.fullname" . }}-populate-interest-agg-v2
spec:
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            tags.datadoghq.com/alias: populate-interest-agg-v2-worker
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
          restartPolicy: Never
      ttlSecondsAfterFinished: 600
  schedule: {{ .Values.populateInterestAggregateV2OneTimerJob.schedule }}
  suspend: {{  ternary (hasKey .Values.populateInterestAggregateV2OneTimerJob "suspend") .Values.populateInterestAggregateV2OneTimerJob.suspend false }}
{{- end }}
  {{- if and .Values.populateInterestAggregateV2OneTimerJob .Values.oneTimeInterestAggJob.enabled -}}
  {{- include "go-app-lib.cron" (list . "populate-interest-agg-v2-cron.tpl") }}
---
{{- end }}