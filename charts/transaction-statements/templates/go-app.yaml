{{- define "transaction-statements.deployment" -}}
{{- end -}}

{{- include "go-app-lib.deployment" (list . "transaction-statements.deployment") }}
---
{{- define "transaction-statements.rollout" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rollout" (list . "transaction-statements.rollout") }}
---
{{- end -}}

{{- define "transaction-statements.rolloutvirtualservice" -}}
{{- end -}}

{{- if and .Values.rollout .Values.argorollouts.rolloutTrafficRoutingEnabled  -}}
{{- include "go-app-lib.rolloutvirtualservice" (list . "transaction-statements.rolloutvirtualservice") }}
---
{{- end -}}

{{- define "transaction-statements.rolloutdestinationrule" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rolloutdestinationrule" (list . "transaction-statements.rolloutdestinationrule") }}
---
{{- end -}}

{{- define "transaction-statements.analysistemplate" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.analysistemplate" (list . "transaction-statements.analysistemplate") }}
---
{{- end -}}

{{- define "transaction-statements.service" -}}
{{- end -}}

{{- include "go-app-lib.service" (list . "transaction-statements.service") }}
---
{{- define "transaction-statements.hpa" -}}
{{- end -}}

{{- if .Values.autoscaling.enabled }}
{{- include "go-app-lib.hpa" (list . "transaction-statements.hpa") }}
---
{{- end -}}

{{- define "transaction-statements.ingress" -}}
{{- end -}}

{{- if .Values.ingress.enabled -}}
{{- include "go-app-lib.ingress" (list . "transaction-statements.ingress") }}
---
{{- end -}}

{{- define "transaction-statements.serviceaccount" -}}
{{- end -}}

{{- if .Values.serviceAccount.create -}}
{{- include "go-app-lib.serviceaccount" (list . "transaction-statements.serviceaccount") }}
---
{{- end -}}

{{- define "transaction-statements.gateway" -}}
{{- end -}}

{{- define "transaction-statements.virtualservice" -}}
{{- end -}}

{{- if .Values.gateway.enabled -}}
{{- include "go-app-lib.gateway" (list . "transaction-statements.gateway") }}
---
{{- include "go-app-lib.virtualservice" (list . "transaction-statements.virtualservice") }}
---
{{- end -}}

{{- define "transaction-statements.authorizationpolicy" -}}
{{- end -}}

{{- if .Values.authorizationpolicy.enabled -}}
{{- include "go-app-lib.authorizationpolicy" (list . "transaction-statements.authorizationpolicy") }}
---
{{- end -}}

{{- define "transaction-statements.podDisruptionBudget" -}}
{{- end -}}

{{- if and (.Values.podDisruptionBudget.enabled) (ne .Values.env "dev") -}}
{{- include "go-app-lib.podDisruptionBudget" (list . "transaction-statements.podDisruptionBudget") }}
{{- end }}
---