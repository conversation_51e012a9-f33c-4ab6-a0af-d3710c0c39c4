env: dev
configName: config.dev.json

argorollouts:
  disableDeployment: true
  analysis:
    enable: false
  rolloutTrafficRoutingEnabled: true
rollout: true

gateway:
  enabled: true
  annotations: {}
  hosts:
    - "backend.dev.g-bank.app"
  tls: true

serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks/cluster/dev-backend-01/transaction-statements
  name: transaction-statements

autoscaling:
  enabled: true

envVars:
  SECRET_CONF: /vault/secrets/
  SERVICE_CONF: /config_files/config.json
  AWS_ROLE_ARN: arn:aws:iam::************:role/eks/cluster/dev-backend-01/transaction-statements
  AWS_WEB_IDENTITY_TOKEN_FILE: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
  AWS_REGION: ap-southeast-1

resources:
  limits:
    memory: 512Mi
  requests:
    cpu: 750m
    memory: 512Mi

podAnnotations:
  vault.hashicorp.com/role: "transaction-statements"
  vault.hashicorp.com/agent-inject-secret-transaction_statements_static_creds.json: database/static-creds/dev-data-backend-txn-statement-ro
  vault.hashicorp.com/agent-inject-template-transaction_statements_static_creds.json: |
    {{ with secret "database/static-creds/dev-data-backend-txn-statement-ro" -}}
      {
        "SNOWFLAKE_USER": "{{ .Data.username }}",
        "SNOWFLAKE_PASSWORD": "{{ .Data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-redis_creds.json: "kv/data/security/tf/dev/txn-statements/ec/auth"
  vault.hashicorp.com/agent-inject-template-redis_creds.json: |
    {{ with secret "kv/data/security/tf/dev/txn-statements/ec/auth" -}}
      {
        "REDIS_PASSWORD": "{{ .Data.auth_token }}"
      }
    {{- end }}

authorizationpolicy:
  enabled: true
  rules:
    - sources: [ "cluster.local/ns/istio-ingress/sa/istio-ingressgateway-service-account" ]
    - sources: ["cluster.local/ns/operations/sa/customer-portal"]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/internal/statements/get" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/sentry-t6/sa/sentry-t6" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: ["POST"]
          paths: ["/v2/statements/get", "/v1/account-calendar-activity", "/v1/lending/generate-statements" ]
        - methods: [ "POST" ]
          paths: [ "/v1/receipt" ]
    - sources: [ "cluster.local/ns/core-banking/sa/transaction-history" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: ["POST"]
          paths: [
            # TODO: remove /v2/statements/get this after dev history service remove this api call
            "/v2/statements/get",
            "/v1/statements/metadata/get"
          ]
    - sources: [ "cluster.local/ns/lending-platform/sa/loan-exp" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v2/generate/invoice", "/v2/get/invoice" ]
