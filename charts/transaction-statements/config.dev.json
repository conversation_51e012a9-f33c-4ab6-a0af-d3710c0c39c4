{"name": "transaction-statements Service", "serviceName": "transaction-statements", "host": "0.0.0.0", "port": 8080, "env": "dev", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": true}, "snowflake_config": {"dsn": "{{ SNOWFL<PERSON>KE_USER }}:{{ SNOWFLAKE_PASSWORD }}@hu79740.ap-southeast-1.snowflakecomputing.com/GOLD/STATEMENTS?role=TXN_STATEMENTS_SERVICE_ROLE&client_session_keep_alive=true", "engine": "snowflake"}, "templateVersion": "v0", "depositsTemplateVersions": [{"version": "v0", "effectiveSince": "2022-08-31T16:00:00Z"}, {"version": "v1", "effectiveSince": "2024-10-31T16:00:00Z", "_desc": "this means Nov 2024 statement, which will should be generated in Dec 2024, will use this version. Please Use actual value for prd config!"}], "templatesPath": "/statement-templates/dbmy", "bizTemplateVersion": "v0", "bizDepositsTemplateVersions": [{"version": "v0", "effectiveSince": "2024-05-29T16:00:00Z"}], "bizTemplatesPath": "/statement-templates/biz", "bizFlexiCreditTemplatesPath": "/statement-templates/biz-flexi-credit", "bizFlexiCreditTemplateVersions": [{"version": "v0", "effectiveSince": "2024-05-29T16:00:00Z"}], "htmlStatementsPath": "/html-statements", "receiptTemplateVersion": "v1", "receiptTemplatesPath": "/receipt-templates/dbmy", "htmlReceiptsPath": "/html-receipts", "templatePathForLoanInvoice": "/drawdown-templates", "htmlFilePathForLoanInvoice": "/html-statements", "templateVersionForLoanInvoice": "v0", "templatePathForBizLoanInvoice": "/biz-drawdown-templates", "templateVersionForBizLoanInvoice": "v0", "htmlFilePathForBizLoanInvoice": "/html-statements", "customerMasterConfig": {"baseURL": "http://customer-master.onboarding.svc.cluster.local"}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local"}, "transactionHistoryConfig": {"baseURL": "https://backend.dev.g-bank.app/transaction-history"}, "pairingServiceConfig": {"baseURL": "https://backend.dev.g-bank.app/pairing-service"}, "paymentExperienceConfig": {"baseURL": "http://payment-experience.payments.svc.cluster.local"}, "pigeon": {"serviceName": "pigeon", "baseURL": "http://pigeon.pigeon.svc.cluster.local"}, "customerExperienceConfig": {"baseURL": "http://customer-experience.onboarding.svc.cluster.local"}, "transactionStatementClientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "loanGenerateInvoiceKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "txn-statement-client-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-generate-invoice-event", "packageName": "pb", "offsetType": "oldest", "dtoName": "GenerateInvoiceEvent", "enable": false}, "s3Config": {"bucketName": "dbmy-dev-backend-transaction-statements", "flexiLoanBucketName": "dbmy-dev-backend-flexiloan-transaction-statement"}, "redisConfig": {"addr": "clustercfg.dbmy-dev-backend-ec-txn-statements.o9f56r.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{REDIS_PASSWORD}}", "tlsEnabled": true}, "arbitraryDelayBeforeRedisConn": 5, "flexiLoanTemplateVersion": "v0", "flexiLoanTemplatesPath": "/flexi-loan-templates", "htmlFlexiLoanPath": "/html-flexi-loan", "dynamicConstants": {"defaultCurrencyCode": "RM", "statementGenerationSLAInDays": 3, "localCurrencyCode": "MYR", "localThousandSeparator": ",", "localDecimalSeparator": "."}, "lendingDynamicConstants": {"penalInterestOverdueRateMultiplyFactor": 0.36, "overdueChargesDays": 120}, "statementGenerationWorkerConfig": {"enable": false, "runMonthly": false, "waitTimeSeconds": 2, "maxRetryCount": 2, "lockDurationInSeconds": 1, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000}, "bizOAStatementGenerationWorkerConfig": {"runMonthly": true, "waitTimeSeconds": 2, "maxRetryCount": 5, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "featureFlags": {"enableDebitCardTransactions": true, "enableBizAuthorisation": true, "enableCardMultiSettlementGrouping": false, "enableBizDrawdownInvoice": true, "enableAuthzWithProfileID": true}, "tableNamespace": {"schemaName": "STATEMENTS"}, "tenant": "MY", "generateMonthlyStatementsTimeoutInSeconds": 30, "largeConsumerQueueMinTransactionCount": 10000, "generateMonthlyLargeStatementsTimeoutInSeconds": 600, "balanceProcessingStrategy": "", "flexiLoanStatementGenerationWorkerConfig": {"enable": true, "runMonthly": true, "waitTimeSeconds": 2, "maxRetryCount": 2, "lockDurationInSeconds": 1, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000}, "bizFlexiCreditStatementGenerationWorkerConfig": {"enable": true, "runMonthly": true, "waitTimeSeconds": 2, "maxRetryCount": 2, "lockDurationInSeconds": 1, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "browserPoolSize": 2, "browserPoolShutdownGracePeriod": 5}