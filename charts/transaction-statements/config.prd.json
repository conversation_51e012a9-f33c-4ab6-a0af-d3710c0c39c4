{"name": "transaction-statements Service", "serviceName": "transaction-statements", "host": "0.0.0.0", "port": 8080, "env": "prd", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": true}, "snowflake_config": {"dsn": "{{ SNOWFLAKE_USER }}:{{ SNOWFLAKE_PASSWORD }}@fq85986.ap-southeast-1.privatelink.snowflakecomputing.com/GOLD/STATEMENTS?warehouse=txn_statements_wh&role=TXN_STATEMENTS_SERVICE_ROLE&client_session_keep_alive=true", "engine": "snowflake"}, "templateVersion": "v0", "depositsTemplateVersions": [{"version": "v0", "effectiveSince": "2022-08-31T16:00:00Z"}, {"version": "v1", "effectiveSince": "2024-12-31T16:00:00Z", "_desc": "this means transactions starting from 1 Jan 2025 will be using this template."}], "templatesPath": "/statement-templates/dbmy", "bizTemplateVersion": "v0", "bizDepositsTemplateVersions": [{"version": "v0", "effectiveSince": "2024-05-29T16:00:00Z"}], "templatePathForBizLoanInvoice": "/biz-drawdown-templates", "templateVersionForBizLoanInvoice": "v0", "htmlFilePathForBizLoanInvoice": "/html-statements", "bizTemplatesPath": "/statement-templates/biz", "htmlStatementsPath": "/html-statements", "receiptTemplateVersion": "v1", "receiptTemplatesPath": "/receipt-templates/dbmy", "htmlReceiptsPath": "/html-receipts", "customerMasterConfig": {"baseURL": "http://customer-master.onboarding.svc.cluster.local"}, "customerExperienceConfig": {"baseURL": "http://customer-experience.onboarding.svc.cluster.local"}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local"}, "transactionHistoryConfig": {"baseURL": "http://transaction-history.core-banking.svc.cluster.local"}, "pairingServiceConfig": {"baseURL": "http://pairing-service.payments.svc.cluster.local"}, "paymentExperienceConfig": {"baseURL": "http://payment-experience.payments.svc.cluster.local"}, "pigeon": {"serviceName": "pigeon", "baseURL": "http://pigeon.pigeon.svc.cluster.local"}, "transactionStatementClientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "loanGenerateInvoiceKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "txn-statement-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-generate-invoice-event", "packageName": "pb", "offsetType": "oldest", "dtoName": "GenerateInvoiceEvent", "enable": true}, "s3Config": {"bucketName": "dbmy-prd-cb-transaction-statements", "flexiLoanBucketName": "dbmy-prd-cb-flexiloan-transaction-statement", "depositsOperationBucketName": "dbmy-prd-cb-deposits-transaction-statements-ops"}, "redisConfig": {"addr": "clustercfg.dbmy-prd-cb-ec-txn-statements.1via9d.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{REDIS_PASSWORD}}", "tlsEnabled": true}, "arbitraryDelayBeforeRedisConn": 5, "flexiLoanTemplateVersion": "v0", "flexiLoanTemplatesPath": "/flexi-loan-templates", "htmlFlexiLoanPath": "/html-flexi-loan", "templatePathForLoanInvoice": "/drawdown-templates", "htmlFilePathForLoanInvoice": "/html-statements", "templateVersionForLoanInvoice": "v0", "dynamicConstants": {"defaultCurrencyCode": "RM", "statementGenerationSLAInDays": 3, "localCurrencyCode": "MYR", "localThousandSeparator": ",", "localDecimalSeparator": "."}, "lendingDynamicConstants": {"penalInterestOverdueRateMultiplyFactor": 0.36, "overdueChargesDays": 120}, "statementGenerationWorkerConfig": {"enable": false, "runMonthly": false, "waitTimeSeconds": 2, "maxRetryCount": 2, "lockDurationInSeconds": 1, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "bizOAStatementGenerationWorkerConfig": {"runMonthly": false, "waitTimeSeconds": 2, "maxRetryCount": 5, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "bizFlexiCreditStatementGenerationWorkerConfig": {"enable": false, "runMonthly": true, "waitTimeSeconds": 2, "maxRetryCount": 2, "lockDurationInSeconds": 1, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "featureFlags": {"enableDebitCardTransactions": true, "enableBizAuthorisation": true, "enableCardMultiSettlementGrouping": false, "enableBizDrawdownInvoice": true, "enableAuthzWithProfileID": true}, "tableNamespace": {"schemaName": "STATEMENTS"}, "tenant": "MY", "generateMonthlyStatementsTimeoutInSeconds": 30, "largeConsumerQueueMinTransactionCount": 1000, "generateMonthlyLargeStatementsTimeoutInSeconds": 600, "balanceProcessingStrategy": "VERIFY_TRANSACTION_LEVEL", "flexiLoanStatementGenerationWorkerConfig": {"enable": true, "runMonthly": true, "waitTimeSeconds": 2, "maxRetryCount": 2, "lockDurationInSeconds": 1, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "browserPoolSize": 2, "browserPoolShutdownGracePeriod": 5, "concurrency": 5}