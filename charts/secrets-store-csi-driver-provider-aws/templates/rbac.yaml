{{- if .Values.rbac.install }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "provider.fullname" . }}-cluster-role-binding
  labels:
{{ include "provider.labels" . | indent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "provider.fullname" . }}-cluster-role
subjects:
  - kind: ServiceAccount
    name: {{ template "provider.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "provider.fullname" . }}-cluster-role
  labels:
{{ include "provider.labels" . | indent 4 }}
rules:
  - apiGroups: [""]
    resources: ["serviceaccounts/token"]
    verbs: ["create"]
  - apiGroups: [""]
    resources: ["serviceaccounts"]
    verbs: ["get"]
  {{- if not .Values.awsRegion }}
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get"]
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get"]
  {{- end }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "provider.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "provider.labels" . | indent 4 }}
{{- end }}
