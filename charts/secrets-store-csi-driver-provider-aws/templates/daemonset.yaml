apiVersion: apps/v1
kind: DaemonSet
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ template "provider.fullname" . }}
  labels:
{{ include "provider.labels" . | indent 4 }}
spec:
  updateStrategy:
{{ toYaml .Values.updateStrategy | indent 4 }}
  selector:
    matchLabels:
      app: {{ template "provider.name" . }}
  template:
    metadata:
      labels:
{{ include "provider.labels" . | indent 8 }}
{{- if .Values.podLabels }}
{{- toYaml .Values.podLabels | nindent 8 }}
{{- end }}
{{- if .Values.podAnnotations }}
      annotations:
{{- toYaml .Values.podAnnotations | nindent 8 }}
{{- end }}
    spec:
      serviceAccountName: {{ template "provider.serviceAccountName" . }}
      hostNetwork: false
{{- if .Values.priority_class_name }}
      priorityClassName: {{.Values.priority_class_name}}
{{- end}}
      containers:
        - name: provider-aws-installer
          image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args:
            - --provider-volume={{ .Values.providerVolume }}
            {{- if .Values.driverWritesSecrets }}
            - --driver-writes-secrets=true
            {{- end }}
            {{- if .Values.k8sThrottlingParams }}
            {{- if .Values.k8sThrottlingParams.qps }}
            - --qps={{ .Values.k8sThrottlingParams.qps }}
            {{- end }}
            {{- if .Values.k8sThrottlingParams.burst }}
            - --burst={{ .Values.k8sThrottlingParams.burst }}
            {{- end }}
            {{- end }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
          securityContext:
{{ toYaml .Values.securityContext | indent 12 }}
          volumeMounts:
            - mountPath: {{ .Values.providerVolume }}
              name: providervol
            - name: mountpoint-dir
              mountPath: {{ .Values.kubeletPath }}/pods
              mountPropagation: HostToContainer
          {{- if or .Values.useFipsEndpoint .Values.awsRegion }}
          env:
            {{- if .Values.useFipsEndpoint }}
            - name: AWS_USE_FIPS_ENDPOINT
              value: {{ .Values.useFipsEndpoint | quote }}
            {{- end }}
            {{- if .Values.awsRegion }}
            - name: AWS_REGION
              value: {{ .Values.awsRegion | quote }}
            {{- end }}
          {{- end }}
      volumes:
        - name: providervol
          hostPath:
            path: {{ .Values.providerVolume }}
        - name: mountpoint-dir
          hostPath:
            path: {{ .Values.kubeletPath }}/pods
            type: DirectoryOrCreate
{{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName | quote }}
{{- end }}
{{- with .Values.imagePullSecrets }}
      imagePullSecrets:
{{- toYaml . | nindent 8 }}
{{- end }}
      nodeSelector:
        kubernetes.io/os: linux
{{- if .Values.nodeSelector }}
{{- toYaml .Values.nodeSelector | nindent 8 }}
{{- end }}
{{- with .Values.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
{{- end }}
{{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
{{- end }}
