image:
  repository: public.ecr.aws/aws-secrets-manager/secrets-store-csi-driver-provider-aws
  pullPolicy: IfNotPresent
  tag: 1.0.r2-96-gfeeb3ac-2025.05.06.20.19

awsRegion: ""
nameOverride: ""
fullnameOverride: ""
providerVolume: "/var/run/secrets-store-csi-providers"
kubeletPath: "/var/lib/kubelet"

driverWritesSecrets: false

podLabels: {}
podAnnotations: {}

affinity: {}

resources:
  requests:
    cpu: 50m
    memory: 100Mi
  limits:
    cpu: 50m
    memory: 100Mi

priorityClassName: ""
nodeSelector: {}

tolerations: []

port: 8989

updateStrategy:
  type: RollingUpdate

imagePullSecrets: []

rbac:
  install: true

securityContext:
  privileged: false
  allowPrivilegeEscalation: false

useFipsEndpoint: false
