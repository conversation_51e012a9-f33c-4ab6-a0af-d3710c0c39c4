{{- define "partner-integration-api.deployment" -}}
{{- end -}}

{{- include "go-app-lib.deployment" (list . "partner-integration-api.deployment") }}
---
{{- define "partner-integration-api.rollout" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rollout" (list . "partner-integration-api.rollout") }}
---
{{- end -}}

{{- define "partner-integration-api.rolloutvirtualservice" -}}
{{- end -}}

{{- if and .Values.rollout .Values.argorollouts.rolloutTrafficRoutingEnabled  -}}
{{- include "go-app-lib.rolloutvirtualservice" (list . "partner-integration-api.rolloutvirtualservice") }}
---
{{- end -}}

{{- define "partner-integration-api.rolloutdestinationrule" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rolloutdestinationrule" (list . "partner-integration-api.rolloutdestinationrule") }}
---
{{- end -}}

{{- define "partner-integration-api.analysistemplate" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.analysistemplate" (list . "partner-integration-api.analysistemplate") }}
---
{{- end -}}

{{- define "partner-integration-api.service" -}}
{{- end -}}

{{- include "go-app-lib.service" (list . "partner-integration-api.service") }}
---
{{- define "partner-integration-api.hpa" -}}
{{- end -}}

{{- if .Values.autoscaling.enabled }}
{{- include "go-app-lib.hpa" (list . "partner-integration-api.hpa") }}
---
{{- end -}}

{{- define "partner-integration-api.ingress" -}}
{{- end -}}

{{- if .Values.ingress.enabled -}}
{{- include "go-app-lib.ingress" (list . "partner-integration-api.ingress") }}
---
{{- end -}}

{{- define "partner-integration-api.serviceaccount" -}}
{{- end -}}

{{- if .Values.serviceAccount.create -}}
{{- include "go-app-lib.serviceaccount" (list . "partner-integration-api.serviceaccount") }}
---
{{- end -}}

{{- define "partner-integration-api.gateway" -}}
{{- end -}}

{{- define "partner-integration-api.virtualservice" -}}
{{- end -}}

{{- if .Values.gateway.enabled -}}
{{- include "go-app-lib.gateway" (list . "partner-integration-api.gateway") }}
---
{{- include "go-app-lib.virtualservice" (list . "partner-integration-api.virtualservice") }}
---
{{- end -}}

{{- define "partner-integration-api.authorizationpolicy" -}}
{{- end -}}

{{- if .Values.authorizationpolicy.enabled -}}
{{- include "go-app-lib.authorizationpolicy" (list . "partner-integration-api.authorizationpolicy") }}
---
{{- end -}}

{{- define "partner-integration-api.podDisruptionBudget" -}}
{{- end -}}

{{- if and (.Values.podDisruptionBudget.enabled) (ne .Values.env "dev") -}}
{{- include "go-app-lib.podDisruptionBudget" (list . "partner-integration-api.podDisruptionBudget") }}
{{- end }}