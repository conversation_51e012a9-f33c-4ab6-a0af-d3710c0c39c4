apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "partner-integration-api.fullname" . }}-test-connection"
  labels:
    {{- include "partner-integration-api.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "partner-integration-api.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
