env: dev
configName: config.dev.json
replicaCount: 2
fullnameOverride: "partner-integration-api"

envVars:
  MYSQL_HOST: partner-integration-api-db.backend.dev.g-bank.app
  MYSQL_HOST_REPLICA: partner-integration-api-db.backend.dev.g-bank.app
  DB_NAME: partner_integration_api
  SECRET_CONF: /vault/secrets/

deployment:
  enabled: true

autoscaling:
  type: container
  targetCPUUtilizationPercentage: 75

podAnnotations:
  vault.hashicorp.com/role: "partner-integration-api"
  vault.hashicorp.com/agent-inject-secret-partner_integration_api_db_creds.json: "database/creds/dev-backend-partner-integration-api-rds-mysql-dba"
  vault.hashicorp.com/agent-inject-template-partner_integration_api_db_creds.json: |
    {{ with secret "database/creds/dev-backend-partner-integration-api-rds-mysql-dba" -}}
      {
        "MYSQL_USERNAME": "{{ .Data.username }}",
        "MYSQL_PASSWORD": "{{ .Data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-redis_creds.json: "kv/data/security/tf/dev/partner-api/ec/auth"
  vault.hashicorp.com/agent-inject-template-redis_creds.json: |
    {{ with secret "kv/data/security/tf/dev/partner-api/ec/auth" -}}
      {
        "REDIS_PASSWORD": "{{ .Data.auth_token }}"
      }
    {{- end }}

authorizationpolicy:
  enabled: true
  rules:
    - sources: [ "cluster.local/ns/istio-ingress/sa/istio-ingressgateway-service-account" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]

gateway:
  enabled: true
  annotations: {}
  hosts:
    - "backend.dev.g-bank.app"
  tls: true
