{{- define "referral-experience.deployment" -}}
{{- end -}}

{{- include "go-app-lib.deployment" (list . "referral-experience.deployment") }}
---
{{- define "referral-experience.rollout" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rollout" (list . "referral-experience.rollout") }}
---
{{- end -}}

{{- define "referral-experience.rolloutvirtualservice" -}}
{{- end -}}

{{- if and .Values.rollout .Values.argorollouts.rolloutTrafficRoutingEnabled  -}}
{{- include "go-app-lib.rolloutvirtualservice" (list . "referral-experience.rolloutvirtualservice") }}
---
{{- end -}}

{{- define "referral-experience.rolloutdestinationrule" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rolloutdestinationrule" (list . "referral-experience.rolloutdestinationrule") }}
---
{{- end -}}

{{- define "referral-experience.analysistemplate" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.analysistemplate" (list . "referral-experience.analysistemplate") }}
---
{{- end -}}

{{- define "referral-experience.service" -}}
{{- end -}}

{{- include "go-app-lib.service" (list . "referral-experience.service") }}
---
{{- define "referral-experience.hpa" -}}
{{- end -}}

{{- if .Values.autoscaling.enabled }}
{{- include "go-app-lib.hpa" (list . "referral-experience.hpa") }}
---
{{- end -}}

{{- define "referral-experience.ingress" -}}
{{- end -}}

{{- if .Values.ingress.enabled -}}
{{- include "go-app-lib.ingress" (list . "referral-experience.ingress") }}
---
{{- end -}}

{{- define "referral-experience.serviceaccount" -}}
{{- end -}}

{{- if .Values.serviceAccount.create -}}
{{- include "go-app-lib.serviceaccount" (list . "referral-experience.serviceaccount") }}
---
{{- end -}}

{{- define "referral-experience.gateway" -}}
{{- end -}}

{{- define "referral-experience.virtualservice" -}}
{{- end -}}

{{- if .Values.gateway.enabled -}}
{{- include "go-app-lib.gateway" (list . "referral-experience.gateway") }}
---
{{- include "go-app-lib.virtualservice" (list . "referral-experience.virtualservice") }}
---
{{- end -}}

{{- define "referral-experience.authorizationpolicy" -}}
{{- end -}}

{{- if .Values.authorizationpolicy.enabled -}}
{{- include "go-app-lib.authorizationpolicy" (list . "referral-experience.authorizationpolicy") }}
---
{{- end -}}

{{- define "referral-experience.podDisruptionBudget" -}}
{{- end -}}

{{- if and (.Values.podDisruptionBudget.enabled) (ne .Values.env "dev") -}}
{{- include "go-app-lib.podDisruptionBudget" (list . "referral-experience.podDisruptionBudget") }}
{{- end }}
