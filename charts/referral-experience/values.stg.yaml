env: stg
configName: config.stg.json
replicaCount: 1
fullnameOverride: "referral-experience"

envVars:
  SECRET_CONF: /vault/secrets/

deployment:
  enabled: true

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
  targetCPUUtilizationPercentage: 50

podAnnotations:
  vault.hashicorp.com/role: "referral-experience"

authorizationpolicy:
  enabled: true
  rules:
    - sources: [ "cluster.local/ns/istio-ingress/sa/istio-ingressgateway-service-account" ]
    - sources: [ "cluster.local/ns/sentry-t6/sa/sentry-t6" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/api/v1/promo-codes", "/api/v1/referral-codes/validate", "/api/v1/referral-codes/apply" ]
        - methods: [ "PUT" ]
          paths: [ "/api/v1/flags" ]

gateway:
  enabled: true
  annotations: {}
  hosts:
    - "internal.loyalty.stg.g-bank.app"
  tls: true