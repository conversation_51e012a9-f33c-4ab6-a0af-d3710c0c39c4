env: prd
configName: config.prd.json
replicaCount: 1
fullnameOverride: "referral-experience"

envVars:
  SECRET_CONF: /vault/secrets/

deployment:
  enabled: true

resources:
  limits:
    memory: 1024Mi
  requests:
    cpu: 250m
    memory: 1024Mi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 6
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

podAnnotations:
  vault.hashicorp.com/role: "referral-experience"

authorizationpolicy:
  enabled: true
  rules:
    - sources: [ "cluster.local/ns/istio-ingress/sa/istio-ingressgateway-service-account" ]
    - sources: [ "cluster.local/ns/sentry-t6/sa/sentry-t6" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/api/v1/promo-codes", "/api/v1/referral-codes/validate", "/api/v1/referral-codes/apply" ]
        - methods: [ "PUT" ]
          paths: [ "/api/v1/flags" ]

gateway:
  enabled: true
  annotations: {}
  hosts:
    - "internal.loyalty.prd.g-bank.app"
  tls: true