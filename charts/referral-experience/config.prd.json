{"name": "referral-experience Service", "serviceName": "referral-experience", "env": "stg", "host": "0.0.0.0", "port": 8080, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 0, "stacktraceLevel": 4, "logFormat": "json"}, "referralEngineClientConf": {"serviceName": "referral-engine", "group": "dbmy", "baseURL": "http://referral-engine.loyalty.svc.cluster.local"}, "customerJournalClientConf": {"eventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "referral-experience-service-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-audit-log", "packageName": "pb", "dtoName": "AuditLog", "offsetType": "oldest"}}, "featureFlags": {"enableCustomerJournalPublishAuditLog": false}}