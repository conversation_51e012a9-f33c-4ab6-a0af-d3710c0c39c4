{{- define "application-service.deployment" -}}
{{- end -}}

{{- include "go-app-lib.deployment" (list . "application-service.deployment") }}
---
{{- define "application-service.rollout" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rollout" (list . "application-service.rollout") }}
---
{{- end -}}

{{- define "application-service.rolloutvirtualservice" -}}
{{- end -}}

{{- if and .Values.rollout .Values.argorollouts.rolloutTrafficRoutingEnabled  -}}
{{- include "go-app-lib.rolloutvirtualservice" (list . "application-service.rolloutvirtualservice") }}
---
{{- end -}}

{{- define "application-service.rolloutdestinationrule" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rolloutdestinationrule" (list . "application-service.rolloutdestinationrule") }}
---
{{- end -}}

{{- define "application-service.analysistemplate" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.analysistemplate" (list . "application-service.analysistemplate") }}
---
{{- end -}}

{{- define "application-service.service" -}}
{{- end -}}

{{- include "go-app-lib.service" (list . "application-service.service") }}
---
{{- define "application-service.hpa" -}}
{{- end -}}

{{- if .Values.autoscaling.enabled }}
{{- include "go-app-lib.hpa" (list . "application-service.hpa") }}
---
{{- end -}}

{{- define "application-service.ingress" -}}
{{- end -}}

{{- if .Values.ingress.enabled -}}
{{- include "go-app-lib.ingress" (list . "application-service.ingress") }}
---
{{- end -}}

{{- define "application-service.serviceaccount" -}}
{{- end -}}

{{- if .Values.serviceAccount.create -}}
{{- include "go-app-lib.serviceaccount" (list . "application-service.serviceaccount") }}
---
{{- end -}}

{{- define "application-service.gateway" -}}
{{- end -}}

{{- define "application-service.virtualservice" -}}
{{- end -}}

{{- if .Values.gateway.enabled -}}
{{- include "go-app-lib.gateway" (list . "application-service.gateway") }}
---
{{- include "go-app-lib.virtualservice" (list . "application-service.virtualservice") }}
---
{{- end -}}

{{- define "application-service.authorizationpolicy" -}}
{{- end -}}

{{- if .Values.authorizationpolicy.enabled -}}
{{- include "go-app-lib.authorizationpolicy" (list . "application-service.authorizationpolicy") }}
---
{{- end -}}

{{- define "application-service.podDisruptionBudget" -}}
{{- end -}}

{{- if and (.Values.podDisruptionBudget.enabled) (ne .Values.env "dev") -}}
{{- include "go-app-lib.podDisruptionBudget" (list . "application-service.podDisruptionBudget") }}
{{- end }}