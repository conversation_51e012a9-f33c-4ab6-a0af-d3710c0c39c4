{"name": "Application Service", "serviceName": "application-service", "mode": "development", "host": "0.0.0.0", "port": 8080, "env": "dev", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "https://digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/application_service?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/application_service?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "templates": {"dbmyCasaOnboardingEKYC": ["personalInfo.dbmyCasaOnboardingEKYC@v1.1.1", "financialInfo.dbmyCasaOnboardingEKYC@v1.1.4", "proofOfIncome.dbmyCasaOnboardingEKYC@v1.0.0", "proofOfStudent.dbmyCasaOnboardingEKYC@v1.0.0", "enhancedFinancialInfo.dbmyCasaOnboardingEKYC@v1.0.0", "tncAgreement.dbmyCasaOnboardingEKYC@v2.2.2", "embeddedForm.financial.privateEmployee@v1.2.0", "embeddedForm.financial.publicEmployee@v1.3.0", "embeddedForm.financial.selfEmployed@v1.2.0"], "dbmyCasaLendingOnboardingEKYC": ["personalInfo.dbmyCasaOnboardingEKYC@v1.1.1", "financialInfo.dbmyCasaOnboardingEKYC@v2.0.0", "proofOfIncome.dbmyCasaOnboardingEKYC@v1.0.0", "proofOfStudent.dbmyCasaOnboardingEKYC@v1.0.0", "enhancedFinancialInfo.dbmyCasaOnboardingEKYC@v1.0.0", "tncAgreement.dbmyCasaOnboardingEKYC@v2.2.2", "embeddedForm.financial.privateEmployee@v1.2.0", "embeddedForm.financial.publicEmployee@v1.3.0", "embeddedForm.financial.selfEmployed@v1.2.0"], "dbmyCasaOnboardingPartner": ["personalInfo.dbmyCasaOnboardingPartner@v1.1.1", "financialInfo.dbmyCasaOnboardingEKYC@v1.0.1", "tncAgreement.dbmyCasaOnboardingEKYC@v2.2.2", "embeddedForm.financial.privateEmployee@v1.1.0", "embeddedForm.financial.publicEmployee@v1.2.0", "embeddedForm.financial.selfEmployed@v1.1.0"], "dbmyLendingOnboarding": ["financialInfo.dbmyLendingOnboarding@v1.1.0", "tncAgreement.dbmyLendingOnboarding@v1.0.0"], "dbmyBizNTBOnboarding": ["personalInfo.dbmyBizOnboarding@v1.0.0", "enterBRN.dbmyBizOnboarding@v1.0.0", "bizDetails.dbmyBizOnboarding@v1.1.0", "updateBizAddress.dbmyBizOnboarding@v1.0.0", "moreBizDetails.dbmyBizOnboarding@v1.1.0", "financialInfo.dbmyBizOnboarding@v1.0.0", "verifyEmail.dbmyBizOnboarding@v1.0.0", "tncAgreement.dbmyBizOnboarding@v1.1.0", "embeddedForm.financial.privateEmployee@v2.1.0", "embeddedForm.financial.publicEmployee@v2.1.0", "embeddedForm.financial.selfEmployed@v2.1.0", "embeddedForm.updateBizState.wpkl@v1.0.0", "embeddedForm.updateBizState.johor@v1.0.0", "embeddedForm.updateBizState.kedah@v1.0.0", "embeddedForm.updateBizState.kelantan@v1.0.0", "embeddedForm.updateBizState.melaka@v1.0.0", "embeddedForm.updateBizState.negeriSembilan@v1.0.0", "embeddedForm.updateBizState.pahang@v1.0.0", "embeddedForm.updateBizState.penang@v1.0.0", "embeddedForm.updateBizState.perak@v1.0.0", "embeddedForm.updateBizState.perlis@v1.0.0", "embeddedForm.updateBizState.sabah@v1.0.0", "embeddedForm.updateBizState.sarawak@v1.0.0", "embeddedForm.updateBizState.selangor@v1.0.0", "embeddedForm.updateBizState.terengganu@v1.0.0", "embeddedForm.updateBizState.labuan@v1.0.0", "embeddedForm.updateBizState.putrajaya@v1.0.0"], "dbmyBizETBOnboarding": ["enterBRN.dbmyBizETBOnboarding@v1.0.0", "bizDetails.dbmyBizOnboarding@v1.1.0", "updateBizAddress.dbmyBizOnboarding@v1.0.0", "moreBizDetails.dbmyBizOnboarding@v1.1.0", "financialInfo.dbmyBizOnboarding@v1.0.0", "verifyEmail.dbmyBizOnboarding@v1.0.0", "tncAgreement.dbmyBizOnboarding@v1.0.0", "embeddedForm.financial.privateEmployee@v2.1.0", "embeddedForm.financial.publicEmployee@v2.1.0", "embeddedForm.financial.selfEmployed@v2.1.0", "embeddedForm.updateBizState.wpkl@v1.0.0", "embeddedForm.updateBizState.johor@v1.0.0", "embeddedForm.updateBizState.kedah@v1.0.0", "embeddedForm.updateBizState.kelantan@v1.0.0", "embeddedForm.updateBizState.melaka@v1.0.0", "embeddedForm.updateBizState.negeriSembilan@v1.0.0", "embeddedForm.updateBizState.pahang@v1.0.0", "embeddedForm.updateBizState.penang@v1.0.0", "embeddedForm.updateBizState.perak@v1.0.0", "embeddedForm.updateBizState.perlis@v1.0.0", "embeddedForm.updateBizState.sabah@v1.0.0", "embeddedForm.updateBizState.sarawak@v1.0.0", "embeddedForm.updateBizState.selangor@v1.0.0", "embeddedForm.updateBizState.terengganu@v1.0.0", "embeddedForm.updateBizState.labuan@v1.0.0", "embeddedForm.updateBizState.putrajaya@v1.0.0"]}, "featureFlags": {"checkDuplicateID": false, "publishCustomerClosing": true, "copyApplicationApp": false, "delayCustomerDeletion": false, "storeDedupInfo": false, "disableCustomerDeletion": true, "enforceApplicationProductStatusTransitionValidation": true}, "unhandledStatusesToBeExpired": ["AS_SUBMITTED", "AS_IN_REVIEW", "AS_PENDING_EKYC_FACE_VERIFICATION", "AS_PENDING_ADDITIONAL_PRODUCT"], "pigeon": {"baseURL": "http://pigeon.pigeon.svc.cluster.local"}, "redisConfig": {"addr": "clustercfg.dbmy-dev-backend-ec-application-service.o9f56r.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}, "customerMaster": {"serviceName": "customer-master", "baseURL": "http://customer-master.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "workers": {"notificationWorker": {"delayInSecs": 60, "startImmediately": false, "logTag": "notificationWorkerTag", "name": "notificationWorker", "lockKey": "notificationWorkerLock", "lockDurationInSecs": 300, "disableWorker": false}, "manualNotificationWorker": {"delayInSecs": 60, "startImmediately": false, "logTag": "manualNotificationWorkerTag", "name": "manualNotificationWorker", "lockKey": "manualNotificationWorkerLock", "lockDurationInSecs": 300, "disableWorker": true}, "expiryWorker": {"delayInSecs": 60, "startImmediately": false, "logTag": "expiryWorkerTag", "name": "expiryWorker", "lockKey": "expiryWorkerLock", "lockDurationInSecs": 300, "disableWorker": false}, "scheduleWorker": {"delayInSecs": 60, "startImmediately": false, "logTag": "scheduleWorkerTag", "name": "scheduleWorker", "lockKey": "scheduleWorkerLock", "lockDurationInSecs": 300, "disableWorker": false}, "appDataProcessorWorker": {"delayInSecs": 10, "startImmediately": false, "logTag": "appDataProcessorWorkerTag", "name": "appDataProcessorWorker", "lockKey": "AppDataProcessorWorkerLock", "lockDurationInSecs": 300, "disableWorker": false}, "customerDeletionWorker": {"delayInSecs": 1800, "startImmediately": false, "logTag": "customerDeletionWorkerTag", "name": "customerDeletionWorker", "lockKey": "customerDeletionWorkerLock", "lockDurationInSecs": 300, "disableWorker": false}}, "applicationExpiredStreamConfig": {"streamID": "dev_application_expired", "topic": "dev-application-expired", "dtoName": "ApplicationExpiry"}, "customerDeletionStreamConfig": {"streamID": "dev_customer_deletion", "topic": "dev-customer-deletion", "dtoName": "CustomerDeletion"}, "customerClosingStreamConfig": {"streamID": "dev_customer_closing", "topic": "dev-customer-closing", "dtoName": "CustomerClosing"}, "applicationStatusTransitionStreamConfig": {"streamID": "dev_application_status_transition", "topic": "dev-application-status-update-transition", "dtoName": "ApplicationStatusTransition"}, "kafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "application-service", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest"}, "applicationReminderConfig": [{"window": {"atLeast": "1d", "atMost": "2d"}, "groupID": "application_draft_for_one_day", "notificationTemplate": {"pushTemplate": "application_reminder_one_day", "lendingPushTemplate": "lending_application_reminder_one_day"}}, {"window": {"atLeast": "3d", "atMost": "4d"}, "groupID": "application_draft_for_three_days", "notificationTemplate": {"pushTemplate": "application_reminder_three_days"}}, {"window": {"atLeast": "7d", "atMost": "8d"}, "groupID": "application_draft_for_seven_days", "notificationTemplate": {"pushTemplate": "application_reminder_seven_days", "lendingPushTemplate": "lending_application_reminder_greater_than_seven_days"}}, {"window": {"atLeast": "9d", "atMost": "10d"}, "groupID": "application_draft_for_nine_days", "notificationTemplate": {"pushTemplate": "application_expiring_in_five_days", "emailTemplate": "application_expiring_in_five_days_email"}}, {"window": {"atLeast": "11d", "atMost": "12d"}, "groupID": "application_draft_for_eleven_days", "notificationTemplate": {"pushTemplate": "application_expiring_in_three_days", "emailTemplate": "application_expiring_in_three_days_email"}}, {"window": {"atLeast": "13d", "atMost": "14d"}, "groupID": "application_draft_for_thirteen_days", "notificationTemplate": {"pushTemplate": "application_expiring_in_one_days", "emailTemplate": "application_expiring_in_one_days_email", "lendingPushTemplate": "lending_application_reminder_greater_than_thirteen_days"}}], "bizApplicationReminderConfig": [{"window": {"atLeast": "1d", "atMost": "2d"}, "groupID": "biz_application_draft_for_one_day", "notificationTemplate": {"pushTemplate": "business_application_reminder_one_day"}, "targetAppStatus": [1]}, {"window": {"atLeast": "3d", "atMost": "4d"}, "groupID": "biz_application_draft_for_three_days", "notificationTemplate": {"pushTemplate": "business_application_reminder_three_days"}, "targetAppStatus": [1]}, {"window": {"atLeast": "7d", "atMost": "8d"}, "groupID": "biz_application_draft_for_seven_days", "notificationTemplate": {"pushTemplate": "business_application_reminder_seven_days"}, "targetAppStatus": [1]}, {"window": {"atLeast": "1d", "atMost": "2d"}, "groupID": "biz_business_los_reminder_for_one_day", "notificationTemplate": {"pushTemplate": "business_los_reminder"}, "targetAppStatus": [17]}, {"window": {"atLeast": "3d", "atMost": "4d"}, "groupID": "biz_business_los_reminder_for_three_days", "notificationTemplate": {"pushTemplate": "business_los_reminder"}, "targetAppStatus": [17]}, {"window": {"atLeast": "7d", "atMost": "8d"}, "groupID": "biz_business_los_reminder_for_seven_days", "notificationTemplate": {"pushTemplate": "business_los_reminder"}, "targetAppStatus": [17]}, {"window": {"atLeast": "25d", "atMost": "26d"}, "groupID": "biz_business_application_expiring_in_five_day", "notificationTemplate": {"pushTemplate": "business_application_expiring_in_five_days", "emailTemplate": "business_application_expiring_in_five_days_email"}, "targetAppStatus": [1, 2, 3, 13, 15, 16, 17, 18]}, {"window": {"atLeast": "27d", "atMost": "28d"}, "groupID": "biz_business_application_expiring_in_three_days", "notificationTemplate": {"pushTemplate": "business_application_expiring_in_three_days", "emailTemplate": "business_application_expiring_in_three_days_email"}, "targetAppStatus": [1, 2, 3, 13, 15, 16, 17, 18]}, {"window": {"atLeast": "29d", "atMost": "30d"}, "groupID": "biz_business_application_expiring_in_one_day", "notificationTemplate": {"pushTemplate": "business_application_expiring_in_one_days", "emailTemplate": "business_application_expiring_in_one_days_email"}, "targetAppStatus": [1, 2, 3, 13, 15, 16, 17, 18]}], "documentReminderConfig": [{"window": {"atLeast": "1d"}, "groupID": "documentGroupID", "notificationTemplate": {"pushTemplate": "additional_details_needed", "lendingPushTemplate": "lending_additional_details_needed"}}, {"window": {"atLeast": "2d"}, "groupID": "documentGroupID", "notificationTemplate": {"pushTemplate": "additional_details_needed", "lendingPushTemplate": "lending_additional_details_needed_two_days"}}, {"window": {"atLeast": "7d"}, "groupID": "documentGroupID", "notificationTemplate": {"lendingPushTemplate": "lending_additional_details_needed_seven_days_waiting", "lendingEmailTemplate": "lending_additional_details_needed_seven_days_waiting_email"}}], "applicationExpiryConfig": {"draft": {"window": {"atLeast": "14d", "atMost": "19d"}, "notificationTemplate": {"pushTemplate": "application_expired", "lendingPushTemplate": "lending_application_expired", "lendingEmailTemplate": "lending_application_expired_email", "hedwigPushTemplate": "hedwig_template_uuid"}}, "pendingAndFailedEkycFaceVerification": {"window": {"atLeast": "14d", "atMost": "44d"}}, "remainingStatuses": {"window": {"atLeast": "28d", "atMost": "34d"}}, "businessExpiryConfig": {"window": {"atLeast": "30d", "atMost": "35d"}, "notificationTemplate": {"pushTemplate": "business_application_expired", "emailTemplate": "business_application_expired_email"}}, "businessEtbExpiryConfig": {"window": {"atLeast": "30d", "atMost": "35d"}, "notificationTemplate": {"pushTemplate": "business_application_expired", "emailTemplate": "business_application_expired_email"}}}, "customerDeletionConfig": {"atLeastDays": 14, "atMostDays": 365}, "businessDeletionConfig": {"atLeastDays": 14, "atMostDays": 365}, "customerJournalStreamConfig": {"streamID": "customer-journal-audit_log", "topic": "dev-audit-log", "dtoName": "AuditLog"}, "businessStatusStreamConfig": {"streamID": "dev_business_status", "topic": "dev-business-status", "dtoName": "BusinessStatus"}, "manualNotificationConfig": {"notificationType": 1, "templateID": "resume_application", "customerIDs": []}, "failFastConfig": {"enabled": true, "nonPiiRequestFields": {"listApplicationsUserID": "a6fb4a6c-06de-41e6-a252-32acf5b31343"}}}