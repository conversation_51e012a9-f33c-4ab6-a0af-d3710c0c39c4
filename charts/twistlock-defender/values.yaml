CLEAN_STALE_NAMESPACES: "false"
COMMUNICATION_PORT: "443"
CONSOLE_CN: asia-southeast1.cloud.twistlock.com
DATA_FOLDER: /var/lib/twistlock
DATA_RECOVERY_ENABLED: "true"
DATA_RECOVERY_VOLUME: /var/lib/twistlock-backup
DEFENDER_CN: ""
DISABLE_CONSOLE_CGROUP_LIMITS: "false"
DOCKER_SOCKET: /var/run/docker.sock
DOCKER_TWISTLOCK_TAG: _34_01_132
FIPS_ENABLED: "false"
MANAGEMENT_PORT_HTTP: ""
MANAGEMENT_PORT_HTTPS: "8083"
READ_ONLY_FS: "true"
RUN_CONSOLE_AS_ROOT: "false"
SELINUX_LABEL: disable
SYSTEMD_ENABLED: "false"
access_ca: ""
cluster: ""
cluster_id: 28e689fd-2886-c1fe-e93b-b8053bdca23d
cluster_name_resolving_method: default
cluster_role_name: twistlock-view
collect_pod_labels: true
container_runtime_socket_folder: /var/run
containers_storage_mount: /var/lib/containerd
containers_storage_mount_rke2: /var/lib/rancher/rke2/agent/containerd
cri_data_rke2_volume_name: cri-rke2-data
cri_data_volume_name: cri-data
defender_service_port: 9998
defender_type: cri
docker_socket_path: /var/run/docker.sock
gke_autopilot_annotation: ""
host_custom_compliance: false
image_name: registry-auth.twistlock.com/tw_bz0oqaxhrpopuhzw0xpcrymiq0uofnym/twistlock/defender:defender
install_bundle: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
limit_cpu: '"900m"'
limit_memory: '"512Mi"'
monitor_istio: true
monitor_service_accounts: true
namespace: twistlock
node_selector: ""
old_defender_ca_cert: ""
old_defender_client_cert: ""
old_defender_client_key: ""
openshift: false
privileged: "false"
role_arn: ""
runc_proxy_sock_folder: /run
secrets_name: ""
selinux_header: "seLinuxOptions:"
selinux_options: "type: super_t"
talos: false
twistlock_data_folder: /var/lib/twistlock
unique_hostname: false
ws_address: wss://asia-southeast1.cloud.twistlock.com:443
# priority_class_name: cluster-important
