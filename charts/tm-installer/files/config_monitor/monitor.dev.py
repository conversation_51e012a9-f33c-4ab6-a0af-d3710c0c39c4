import argparse
import json
import os
import re
from typing import Any, Dict, Optional

from kubernetes import client, config
from kubernetes.client.rest import ApiException
import jmespath

def setup_kubernetes_client():
    """
    Set up the Kubernetes client with proper authentication.

    This function attempts to load the Kubernetes configuration in the following order:
    1. In-cluster configuration (if running inside a pod)
    2. Kubeconfig file from default locations

    Returns:
        kubernetes.client.ApiClient: Configured Kubernetes API client

    Raises:
        Exception: If unable to load Kubernetes configuration
    """

    # setup kube config that's equivalent to the above bash script

    try:
        context = os.getenv('KUBE_CONTEXT')
        config.load_config(context=context)
    except config.ConfigException as e:
        raise Exception(f"Unable to load Kubernetes configuration: {e}")

    return client.ApiClient()


def extract_jsonpath_value(resource_dict: Dict[str, Any], jsonpath: str) -> Optional[str]:
    """
    Extract a value from a Kubernetes resource dictionary using a JSONPath-like expression.

    This function supports a subset of JSONPath expressions commonly used with kubectl:
    - Simple dot notation: .spec.replicas
    - Array indexing: .spec.containers[0].name
    - Array filtering by name: .spec.containers[?(@.name=="main")].resources.limits.memory

    Args:
        resource_dict (dict): The Kubernetes resource as a dictionary
        jsonpath (str): JSONPath expression (with or without surrounding braces)

    Returns:
        str: The extracted value as a string, or None if not found
    """
    # Remove surrounding braces if present
    if jsonpath.startswith('{') and jsonpath.endswith('}'):
        jsonpath = jsonpath[1:-1]

    # Start with the root object
    current = resource_dict

    # Split the path by dots, but handle array expressions
    parts = []
    current_part = ""
    bracket_depth = 0

    for char in jsonpath:
        if char == '[':
            bracket_depth += 1
            current_part += char
        elif char == ']':
            bracket_depth -= 1
            current_part += char
        elif char == '.' and bracket_depth == 0:
            if current_part:
                parts.append(current_part)
                current_part = ""
        else:
            current_part += char

    if current_part:
        parts.append(current_part)

    try:
        for part in parts:
            if not part:
                continue

            # Handle array indexing and filtering
            if '[' in part:
                field_name = part.split('[')[0]
                bracket_content = part[part.find('[') + 1:part.rfind(']')]

                # Navigate to the field first
                if field_name:
                    current = current[field_name]

                # Handle array filtering with conditions
                if bracket_content.startswith('?(@.'):
                    # Parse condition like ?(@.name=="main")
                    condition_match = re.match(r'\?\(@\.(\w+)==["\']([^"\']+)["\']', bracket_content)
                    if condition_match and isinstance(current, list):
                        field_to_check = condition_match.group(1)
                        expected_value = condition_match.group(2)

                        # Find the first item that matches the condition
                        for item in current:
                            if isinstance(item, dict) and item.get(field_to_check) == expected_value:
                                current = item
                                break
                        else:
                            return None
                    else:
                        return None
                # Handle simple array indexing
                elif bracket_content.isdigit():
                    index = int(bracket_content)
                    if isinstance(current, list) and 0 <= index < len(current):
                        current = current[index]
                    else:
                        return None
                else:
                    return None
            else:
                # Simple field access
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return None

        # Convert the final value to string
        if current is not None:
            return str(current)
        return None

    except (KeyError, IndexError, TypeError):
        return None


def run_kubectl_check(check_config, k8s_client=None):
    """
    Runs a Kubernetes API check based on the provided check configuration
    and compares the output to the expected value.

    Args:
        check_config (dict): A dictionary containing the check details:
            - name (str): A descriptive name for the check.
            - resource_type (str): e.g., "deployment", "statefulset", "hpa".
            - resource_name (str): The name of the Kubernetes resource.
            - namespace (str): The namespace of the resource.
            - json_path (str): The JSONPath expression to extract the value.
            - expected_value (str): The expected value (as a string).
            - value_suffix (str, optional): A suffix to remove from the command output
                                            before comparison (e.g., "%"). Defaults to None.
        k8s_client: Kubernetes API client instance (optional, will create if not provided)

    Returns:
        dict: A dictionary containing the check result:
            - name (str): The name of the check.
            - success (bool): True if the check passed, False otherwise.
            - command (str): The equivalent kubectl command for reference.
            - actual_value (str): The actual value obtained from the API.
            - expected_value (str): The expected value.
            - error (str, optional): Error message if the API call failed.
    """
    result = {
        "name": check_config["name"],
        "success": False,
        "actual_value": None,
        "expected_value": check_config["expected_value"],
        "command": None,
        "error": None,
    }

    try:
        # Create equivalent kubectl command for reference
        cmd_parts = [
            "kubectl", "get", check_config["resource_type"],
            check_config["resource_name"],
            "-n", check_config["namespace"],
            "-o", f"jsonpath='{check_config['json_path']}'"
        ]
        result["command"] = " ".join(cmd_parts)

        # Set up Kubernetes client if not provided
        if k8s_client is None:
            k8s_client = setup_kubernetes_client()

        # Get the appropriate API client based on resource type
        resource_type = check_config["resource_type"].lower()
        resource_name = check_config["resource_name"]
        namespace = check_config["namespace"]

        # Map resource types to their corresponding API methods
        resource_api_map = {
            'deployment': ('apps_v1_api', 'read_namespaced_deployment'),
            'statefulset': ('apps_v1_api', 'read_namespaced_stateful_set'),
            'daemonset': ('apps_v1_api', 'read_namespaced_daemon_set'),
            'replicaset': ('apps_v1_api', 'read_namespaced_replica_set'),
            'service': ('core_v1_api', 'read_namespaced_service'),
            'pod': ('core_v1_api', 'read_namespaced_pod'),
            'configmap': ('core_v1_api', 'read_namespaced_config_map'),
            'secret': ('core_v1_api', 'read_namespaced_secret'),
            'persistentvolumeclaim': ('core_v1_api', 'read_namespaced_persistent_volume_claim'),
            'pvc': ('core_v1_api', 'read_namespaced_persistent_volume_claim'),
            'hpa': ('autoscaling_v2_api', 'read_namespaced_horizontal_pod_autoscaler'),
            'horizontalpodautoscaler': ('autoscaling_v2_api', 'read_namespaced_horizontal_pod_autoscaler'),
            'ingress': ('networking_v1_api', 'read_namespaced_ingress'),
            'networkpolicy': ('networking_v1_api', 'read_namespaced_network_policy'),
        }

        if resource_type not in resource_api_map:
            result["error"] = f"Unsupported resource type: {resource_type}"
            return result

        api_client_name, method_name = resource_api_map[resource_type]

        # Get the appropriate API client
        if api_client_name == 'apps_v1_api':
            api_instance = client.AppsV1Api(k8s_client)
        elif api_client_name == 'core_v1_api':
            api_instance = client.CoreV1Api(k8s_client)
        elif api_client_name == 'autoscaling_v2_api':
            api_instance = client.AutoscalingV2Api(k8s_client)
        elif api_client_name == 'networking_v1_api':
            api_instance = client.NetworkingV1Api(k8s_client)
        else:
            result["error"] = f"Unknown API client: {api_client_name}"
            return result

        # Call the appropriate API method
        api_method = getattr(api_instance, method_name)
        resource = api_method(name=resource_name, namespace=namespace)

        # Convert the resource to a dictionary
        resource_dict = api_instance.api_client.sanitize_for_serialization(resource)

        # Extract the value using JSONPath
        actual_value_raw = extract_jsonpath_value(resource_dict, check_config['json_path'])

        if actual_value_raw is None:
            result["error"] = f"JSONPath '{check_config['json_path']}' did not match any value in the resource"
            result["actual_value"] = "null"
            return result

        # Process the value (remove suffix if specified)
        value_suffix = check_config.get("value_suffix")
        if value_suffix and actual_value_raw.endswith(value_suffix):
            actual_value = actual_value_raw[:-len(value_suffix)]
        else:
            actual_value = actual_value_raw

        result["actual_value"] = actual_value
        if actual_value == check_config["expected_value"]:
            result["success"] = True
        else:
            result["error"] = f"Mismatch: Actual='{actual_value}', Expected='{check_config['expected_value']}'"

    except ApiException as e:
        if e.status == 404:
            result["error"] = f"Resource '{resource_name}' not found in namespace '{namespace}'"
        else:
            result["error"] = f"Kubernetes API error: {e.reason} (status: {e.status})"
        result["actual_value"] = "error"
    except Exception as e:
        result["error"] = f"An unexpected error occurred: {str(e)}"
        result["actual_value"] = "error"

    return result


def main():
    """
    Main function to load checks from config and run them.
    """
    parser = argparse.ArgumentParser(description="Run checks from a configuration file.")
    parser.add_argument('--config',
                        default="config.dev.json",
                        help='Path to the configuration file.')
    args = parser.parse_args()
    config_file = args.config
    results = []

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
    except FileNotFoundError:
        print(f"❌ Error: Configuration file '{config_file}' not found.")
        return
    except json.JSONDecodeError:
        print(f"❌ Error: Invalid JSON in '{config_file}'.")
        return

    # Extract checks from config - handle both direct array and nested structure
    if isinstance(config_data, list):
        checks = config_data
    elif isinstance(config_data, dict):
        # Look for common keys that might contain the checks
        checks = config_data.get('checks', config_data.get('tm_config', []))
    else:
        print(f"❌ Error: Invalid configuration format in '{config_file}'.")
        return

    if not checks:
        print(f"❌ Error: No checks found in configuration file '{config_file}'.")
        return

    print(f"⚙️  Loading checks from '{config_file}'...\n")

    # Initialize Kubernetes client once
    try:
        k8s_client = setup_kubernetes_client()
        print("✅ Successfully connected to Kubernetes cluster\n")
    except Exception as e:
        print(f"❌ Error: Failed to connect to Kubernetes cluster: {e}")
        return

    for i, check_config in enumerate(checks):
        print(f"🔄 Running check {i + 1}/{len(checks)}: {check_config.get('name', 'Unnamed Check')}...")
        check_result = run_kubectl_check(check_config, k8s_client)
        results.append(check_result)

        if check_result["success"]:
            print(f"✅ Check PASSED: {check_result['name']}")
            print(f"   📄 Command: {check_result['command']}")
            print(f"   ➡️  Actual: {check_result['actual_value']}, Expected: {check_result['expected_value']}\n")
        else:
            print(f"❌ Check FAILED: {check_result['name']}")
            print(f"   📄 Command: {check_result['command']}")
            print(f"   ➡️  Expected: {check_result['expected_value']}")
            print(f"   ⬅️  Actual: {check_result['actual_value']}")
            if check_result['error'] and "Mismatch" not in check_result['error']:
                print(f"   ❗ Error: {check_result['error']}\n")
            else:
                print("")

    print("\n--- Summary ---")
    passed_count = sum(1 for r in results if r["success"])
    failed_count = len(results) - passed_count

    print(f"Total checks: {len(results)}")
    print(f"✅ Passed: {passed_count}")
    print(f"❌ Failed: {failed_count}")

    if failed_count > 0:
        print("\nFailed checks details:")
        for r in results:
            if not r["success"]:
                print(
                    f"- {r['name']}: Expected '{r['expected_value']}', Got '{r['actual_value']}'. Error: {r.get('error', 'N/A')}")


if __name__ == "__main__":
    main()
