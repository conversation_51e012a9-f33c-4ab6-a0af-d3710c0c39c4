import argparse
import json
import subprocess


def run_kubectl_check(check_config):
    """
    Runs a kubectl command based on the provided check configuration
    and compares the output to the expected value.

    Args:
        check_config (dict): A dictionary containing the check details:
            - name (str): A descriptive name for the check.
            - resource_type (str): e.g., "deployment", "statefulset", "hpa".
            - resource_name (str): The name of the Kubernetes resource.
            - namespace (str): The namespace of the resource.
            - json_path (str): The JSONPath expression to extract the value.
            - expected_value (str): The expected value (as a string).
            - value_suffix (str, optional): A suffix to remove from the command output
                                            before comparison (e.g., "%"). Defaults to None.

    Returns:
        dict: A dictionary containing the check result:
            - name (str): The name of the check.
            - success (bool): True if the check passed, False otherwise.
            - command (str): The executed kubectl command.
            - actual_value (str): The actual value obtained from kubectl.
            - expected_value (str): The expected value.
            - error (str, optional): Error message if the command failed.
    """
    result = {
        "name": check_config["name"],
        "success": False,
        "actual_value": None,
        "expected_value": check_config["expected_value"],
        "command": None,
        "error": None,
    }

    try:
        # Construct the kubectl command
        cmd_parts = [
            "kubectl", "get", check_config["resource_type"],
            check_config["resource_name"],
            "-n", check_config["namespace"],
            "-o", f"jsonpath='{check_config['json_path']}'"
        ]
        result["command"] = " ".join(cmd_parts)

        # Execute the command
        process = subprocess.run(cmd_parts, capture_output=True, text=True, check=False, timeout=30)

        if process.returncode == 0:
            actual_value_raw = process.stdout.strip()
            # Remove potential single quotes from kubectl output
            if actual_value_raw.startswith("'") and actual_value_raw.endswith("'"):
                actual_value_raw = actual_value_raw[1:-1]

            value_suffix = check_config.get("value_suffix")
            if value_suffix and actual_value_raw.endswith(value_suffix):
                actual_value = actual_value_raw[:-len(value_suffix)]
            else:
                actual_value = actual_value_raw

            result["actual_value"] = actual_value
            if actual_value == check_config["expected_value"]:
                result["success"] = True
            else:
                result["error"] = f"Mismatch: Actual='{actual_value}', Expected='{check_config['expected_value']}'"
        else:
            result["error"] = f"kubectl command failed with error: {process.stderr.strip()}"
            result["actual_value"] = process.stderr.strip()  # Or None, depending on preference

    except FileNotFoundError:
        result["error"] = "kubectl command not found. Please ensure it's installed and in your PATH."
    except subprocess.TimeoutExpired:
        result["error"] = "kubectl command timed out."
    except Exception as e:
        result["error"] = f"An unexpected error occurred: {str(e)}"

    return result


def main():
    """
    Main function to load checks from config and run them.
    """
    parser = argparse.ArgumentParser(description="Run checks from a configuration file.")
    parser.add_argument('--config',
                        default="gxs_prod_tm_customized_config.json",
                        help='Path to the configuration file.')
    args = parser.parse_args()
    config_file = args.config
    results = []

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            checks = json.load(f)
    except FileNotFoundError:
        print(f"❌ Error: Configuration file '{config_file}' not found.")
        return
    except json.JSONDecodeError:
        print(f"❌ Error: Invalid JSON in '{config_file}'.")
        return

    print(f"⚙️  Loading checks from '{config_file}'...\n")

    for i, check_config in enumerate(checks):
        print(f"🔄 Running check {i + 1}/{len(checks)}: {check_config.get('name', 'Unnamed Check')}...")
        check_result = run_kubectl_check(check_config)
        results.append(check_result)

        if check_result["success"]:
            print(f"✅ Check PASSED: {check_result['name']}")
            print(f"   📄 Command: {check_result['command']}")
            print(f"   ➡️  Actual: {check_result['actual_value']}, Expected: {check_result['expected_value']}\n")
        else:
            print(f"❌ Check FAILED: {check_result['name']}")
            print(f"   📄 Command: {check_result['command']}")
            print(f"   ➡️  Expected: {check_result['expected_value']}")
            print(f"   ⬅️  Actual: {check_result['actual_value']}")
            if check_result['error'] and "Mismatch" not in check_result['error']:
                print(f"   ❗ Error: {check_result['error']}\n")
            else:
                print("")

    print("\n--- Summary ---")
    passed_count = sum(1 for r in results if r["success"])
    failed_count = len(results) - passed_count

    print(f"Total checks: {len(results)}")
    print(f"✅ Passed: {passed_count}")
    print(f"❌ Failed: {failed_count}")

    if failed_count > 0:
        print("\nFailed checks details:")
        for r in results:
            if not r["success"]:
                print(
                    f"- {r['name']}: Expected '{r['expected_value']}', Got '{r['actual_value']}'. Error: {r.get('error', 'N/A')}")


if __name__ == "__main__":
    main()
