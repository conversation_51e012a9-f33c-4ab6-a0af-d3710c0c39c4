
exit_code=0
echo "INFO: Installing dependency"
pip install --user datadog requests || exit_code=$?

if [ $exit_code -ne 0 ]; then
    echo "ERROR: Failed to install datadog and requests"
    exit 0
fi

echo "INFO: Running Thought Machine config monitoring script"
python /config_files/monitor.py || exit_code=$?

if [ $exit_code -ne 0 ]; then
    echo "ERROR: Failed to run monitor.py"
    exit 0
fi

echo "INFO: Thought Machine config monitor script completed successfully, exiting..."
