apiVersion: v1
kind: ConfigMap
metadata:
  name: tm-scheduled-scaler
  namespace: tm-system
  labels:
    app: tm-scheduled-scaler
    app.kubernetes.io/name: tm-scheduled-scaler
    app.kubernetes.io/instance: tm-scheduled-scaler
    tags.datadoghq.com/service: tm-scheduled-scaler
data:
  run-scaling.sh: |-
    #!/bin/env sh
    set -e

    if ! [ "${ENABLED}" = "true" ]; then
      echo "INFO: Scaling is not enabled"
      exit 0
    fi

    # setup kubectl
    namespace=tm-system
    serviceAccount=tm-installer
    secretName=$(kubectl --namespace $namespace get serviceAccount $serviceAccount -o jsonpath='{.secrets[0].name}')
    KUBE_API_EP="https://$KUBERNETES_PORT_443_TCP_ADDR:443"
    KUBE_API_TOKEN="$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)"
    KUBE_CERT=$(kubectl --namespace $namespace get secret/$secretName -o jsonpath='{.data.ca\.crt}' | base64 -d)

    echo "$KUBE_CERT
    " >deploy.crt
    kubectl config set-cluster k8s --server=$KUBE_API_EP --certificate-authority=deploy.crt --embed-certs=true
    kubectl config set-credentials tm-installer --token=$KUBE_API_TOKEN
    kubectl config set-context k8s --cluster k8s --user tm-installer
    kubectl config use-context k8s

    # Perform scaling by patching HPA
    hpa="${HPA}"
    min_replicas="${MIN_REPLICAS}"
    echo "INFO: Input min replicas for ${hpa} is ${min_replicas}"
    echo "INFO: Getting current max replicas for ${hpa}"
    current_max_replicas=$(kubectl -n tm-vault get hpa "${hpa}" -o jsonpath='{.spec.maxReplicas}')
    echo "INFO: Current max replicas for ${hpa} is ${current_max_replicas}"
    echo "INFO: Getting current min replicas for ${hpa}"
    current_min_replicas=$(kubectl -n tm-vault get hpa "${hpa}" -o jsonpath='{.spec.minReplicas}')
    echo "INFO: Current min replicas for ${hpa} is ${current_min_replicas}"
    if [ "${current_max_replicas}" -ge "${min_replicas}" ]
    then
      echo "INFO: Patching ${hpa} min replicas from ${current_min_replicas} to ${min_replicas}"
      kubectl -n tm-vault patch hpa "${hpa}" --type='json' -p='[{"op": "replace", "path": "/spec/minReplicas", "value": '"${min_replicas}"'}]'
    else
      echo "WARN: ${min_replicas} is greater than ${current_max_replicas} for ${hpa}. Skipping"
    fi
