{{- if .Values.windows.enabled }}
kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: {{ template "sscd.fullname" . }}-windows
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "sscd.labels" . | indent 4 }}
{{- if .Values.windows.daemonsetAnnotations }}
  annotations:
{{ toYaml .Values.windows.daemonsetAnnotations | indent 4 }}
{{- end }}
spec:
  selector:
    matchLabels:
      app: {{ template "sscd.name" . }}
  updateStrategy:
{{ toYaml .Values.windows.updateStrategy | indent 4 }}
  template:
    metadata:
      labels:
{{ include "sscd.labels" . | indent 8 }}
{{- if .Values.windows.podLabels }}
{{- toYaml .Values.windows.podLabels | nindent 8 }}
{{- end }}
      annotations:
        kubectl.kubernetes.io/default-container: secrets-store
{{- if .Values.windows.podAnnotations }}
{{ toYaml .Values.windows.podAnnotations | indent 8 }}
{{- end }}
    spec:
      serviceAccountName: secrets-store-csi-driver
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
        {{ toYaml .Values.imagePullSecrets | indent 8 }}
      {{- end }}
      affinity:
{{ toYaml .Values.windows.affinity | indent 8 }}
      containers:
        - name: node-driver-registrar
          {{- if .Values.windows.registrarImage.digest }}
          image: "{{ .Values.windows.registrarImage.repository }}@{{ .Values.windows.registrarImage.digest }}"
          {{- else }}
          image: "{{ .Values.windows.registrarImage.repository }}:{{ .Values.windows.registrarImage.tag }}"
          {{- end }}
          args:
            - --v={{ .Values.windows.registrar.logVerbosity }}
            - "--csi-address=unix://C:\\csi\\csi.sock"
            - --kubelet-registration-path={{ .Values.windows.kubeletRootDir }}\plugins\csi-secrets-store\csi.sock
          imagePullPolicy: {{ .Values.windows.registrarImage.pullPolicy }}
          volumeMounts:
            - name: plugin-dir
              mountPath: C:\csi
            - name: registration-dir
              mountPath: C:\registration
{{- with .Values.windows.registrar.resources }}
          resources:
{{ toYaml . | indent 12 }}
{{- end }}
        - name: secrets-store
          {{- if .Values.windows.image.digest }}
          image: "{{ .Values.windows.image.repository }}@{{ .Values.windows.image.digest }}"
          {{- else }}
          image: "{{ .Values.windows.image.repository }}:{{ .Values.windows.image.tag }}"
          {{- end }}
          args:
            {{- if .Values.logVerbosity }}
            - -v={{ .Values.logVerbosity }}
            {{- end }}
            {{- if .Values.logFormatJSON }}
            - --log-format-json={{ .Values.logFormatJSON }}
            {{- end }}
            - "--endpoint=$(CSI_ENDPOINT)"
            - "--nodeid=$(KUBE_NODE_NAME)"
            - "--provider-volume={{ .Values.windows.providersDir }}"
            - "--additional-provider-volume-paths={{ join "," .Values.windows.additionalProvidersDirs }}"
            {{- if .Values.enableSecretRotation }}
            - "--enable-secret-rotation={{ .Values.enableSecretRotation }}"
            {{- end }}
            {{- if .Values.rotationPollInterval }}
            - "--rotation-poll-interval={{ .Values.rotationPollInterval }}"
            {{- end }}
            - "--metrics-addr={{ .Values.windows.metricsAddr }}"
            {{- if .Values.providerHealthCheck }}
            - "--provider-health-check={{ .Values.providerHealthCheck }}"
            {{- end }}
            {{- if .Values.providerHealthCheckInterval }}
            - "--provider-health-check-interval={{ .Values.providerHealthCheckInterval }}"
            {{- end }}
            {{- if .Values.maxCallRecvMsgSize }}
            - "--max-call-recv-msg-size={{ .Values.maxCallRecvMsgSize | int64 }}"
            {{- end }}
          env:
          {{- with .Values.windows.env }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
          - name: CSI_ENDPOINT
            value: unix://C:\\csi\\csi.sock
          - name: KUBE_NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          imagePullPolicy: {{ .Values.windows.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.livenessProbe.port }}
              name: healthz
              protocol: TCP
            - containerPort: {{ trimPrefix ":" .Values.windows.metricsAddr }}
              name: metrics
              protocol: TCP
          livenessProbe:
              failureThreshold: 5
              httpGet:
                path: /healthz
                port: healthz
              initialDelaySeconds: 30
              timeoutSeconds: 10
              periodSeconds: 15
          volumeMounts:
            - name: plugin-dir
              mountPath: C:\csi
            - name: mountpoint-dir
              mountPath: {{ .Values.windows.kubeletRootDir }}\pods
            {{- $providersDir := .Values.windows.providersDir }}
            - name: providers-dir
              mountPath: "{{ $providersDir }}"
            {{- range $i, $path := .Values.windows.additionalProvidersDirs }}
            {{- if ne $providersDir $path }}
            - name: providers-dir-{{ $i }}
              mountPath: "{{ $path }}"
            {{- end }}
            {{- end }}
            {{- if .Values.windows.volumeMounts }}
              {{- toYaml .Values.windows.volumeMounts | nindent 12 }}
            {{- end }}
{{- with .Values.windows.driver.resources }}
          resources:
{{ toYaml . | indent 12 }}
{{- end }}
        - name: liveness-probe
          {{- if .Values.windows.livenessProbeImage.digest }}
          image: "{{ .Values.windows.livenessProbeImage.repository }}@{{ .Values.windows.livenessProbeImage.digest }}"
          {{- else }}
          image: "{{ .Values.windows.livenessProbeImage.repository }}:{{ .Values.windows.livenessProbeImage.tag }}"
          {{- end }}
          imagePullPolicy: {{ .Values.windows.livenessProbeImage.pullPolicy }}
          args:
          - "--csi-address=unix://C:\\csi\\csi.sock"
          - --probe-timeout=3s
          - --http-endpoint=0.0.0.0:{{ .Values.livenessProbe.port }}
          - -v={{ .Values.livenessProbe.logLevel }}
          volumeMounts:
            - name: plugin-dir
              mountPath: C:\csi
{{- with .Values.windows.livenessProbe.resources }}
          resources:
{{ toYaml . | indent 12 }}
{{- end }}
      {{- if .Values.windows.priorityClassName }}
      priorityClassName: {{ .Values.windows.priorityClassName | quote }}
      {{- end }}
      volumes:
        - name: mountpoint-dir
          hostPath:
            path: {{ .Values.windows.kubeletRootDir }}\pods\
            type: DirectoryOrCreate
        - name: registration-dir
          hostPath:
            path: {{ .Values.windows.kubeletRootDir }}\plugins_registry\
            type: Directory
        - name: plugin-dir
          hostPath:
            path: {{ .Values.windows.kubeletRootDir }}\plugins\csi-secrets-store\
            type: DirectoryOrCreate
        {{- $providersDir := .Values.windows.providersDir }}
        - name: providers-dir
          hostPath:
            path: "{{ $providersDir }}"
            type: DirectoryOrCreate
        {{- range $i, $path := .Values.windows.additionalProvidersDirs }}
        {{- if ne $path $providersDir }}
        - name: providers-dir-{{ $i }}
          hostPath:
            path: "{{ $path }}"
            type: DirectoryOrCreate
        {{- end }}
        {{- end }}
        {{- if .Values.windows.volumes }}
          {{- toYaml .Values.windows.volumes | nindent 8 }}
        {{- end }}
      nodeSelector:
        kubernetes.io/os: windows
{{- if .Values.windows.nodeSelector }}
{{- toYaml .Values.windows.nodeSelector | nindent 8 }}
{{- end }}
{{- with .Values.windows.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
{{- end }}
{{- end -}}
