{{ if .Values.rbac.install }}

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  labels:
{{ include "sscd.labels" . | indent 4 }}
    rbac.authorization.k8s.io/aggregate-to-view: "true"
  name: secretproviderclasspodstatuses-viewer-role
rules:
- apiGroups:
  - secrets-store.csi.x-k8s.io
  resources:
  - secretproviderclasspodstatuses
  verbs:
  - get
  - list
  - watch
{{ end }}
