{{- if .Values.linux.crds.enabled -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "sscd.fullname" . }}-upgrade-crds
  labels:
{{ include "sscd.labels" . | indent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-delete-policy: "hook-succeeded,before-hook-creation"
    helm.sh/hook-weight: "1"
rules:
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "create", "update", "patch"]
{{- if and .Values.rbac.pspEnabled (.Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy") }}
  - apiGroups: ['policy']
    resources: ['podsecuritypolicies']
    verbs:     ['use']
    resourceNames:
    - allow-upgrade-crds
{{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "sscd.fullname" . }}-upgrade-crds
  labels:
{{ include "sscd.labels" . | indent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-delete-policy: "hook-succeeded,before-hook-creation"
    helm.sh/hook-weight: "1"
subjects:
  - kind: ServiceAccount
    name: {{ template "sscd.fullname" . }}-upgrade-crds
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ template "sscd.fullname" . }}-upgrade-crds
  apiGroup: rbac.authorization.k8s.io
---
{{- if and .Values.rbac.pspEnabled (.Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy") }}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: allow-upgrade-crds
  labels:
{{ include "sscd.labels" . | indent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-delete-policy: "hook-succeeded,before-hook-creation"
    helm.sh/hook-weight: "1"
spec:
  fsGroup:
    rule: RunAsAny
  runAsUser:
    rule: RunAsAny
  seLinux:
    rule: RunAsAny
  supplementalGroups:
    rule: RunAsAny
  volumes:
  - secret
{{- end }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "sscd.fullname" . }}-upgrade-crds
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "sscd.labels" . | indent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-delete-policy: "hook-succeeded,before-hook-creation"
    helm.sh/hook-weight: "1"
---
apiVersion: batch/v1
kind: Job
metadata:
  name: secrets-store-csi-driver-upgrade-crds
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "sscd.labels" . | indent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "10"
    helm.sh/hook-delete-policy: "hook-succeeded,before-hook-creation"
spec:
  backoffLimit: 3
  template:
    metadata:
      name: {{ template "sscd.fullname" . }}-upgrade-crds
      {{- if .Values.linux.crds.annotations }}
      annotations:
        {{- toYaml .Values.linux.crds.annotations | nindent 8 }}
      {{- end }}
      {{- if .Values.linux.crds.podLabels }}
      labels:
        {{- toYaml .Values.linux.crds.podLabels | nindent 8 }}
      {{- end }}
    spec:
      serviceAccountName: {{ template "sscd.fullname" . }}-upgrade-crds
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
{{ toYaml .Values.imagePullSecrets | indent 6 }}
      {{- end }}
      restartPolicy: Never
      containers:
      - name: crds-upgrade
        image: "{{ .Values.linux.crds.image.repository }}:{{ .Values.linux.crds.image.tag }}"
        args:
        - apply
        - -f
        - crds/
        imagePullPolicy: {{ .Values.linux.crds.image.pullPolicy }}
        {{- with .Values.linux.crds.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      nodeSelector:
        kubernetes.io/os: linux
{{- if .Values.linux.nodeSelector }}
{{- toYaml .Values.linux.nodeSelector | nindent 8 }}
{{- end }}
{{- with .Values.linux.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
{{- end }}
{{- end }}
