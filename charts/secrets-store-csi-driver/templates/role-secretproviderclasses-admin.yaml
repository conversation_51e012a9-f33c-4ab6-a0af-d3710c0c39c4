{{ if .Values.rbac.install }}

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  labels:
{{ include "sscd.labels" . | indent 4 }}
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
    rbac.authorization.k8s.io/aggregate-to-edit: "true"
  name: secretproviderclasses-admin-role
rules:
- apiGroups:
  - secrets-store.csi.x-k8s.io
  resources:
  - secretproviderclasses
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
{{ end }}
