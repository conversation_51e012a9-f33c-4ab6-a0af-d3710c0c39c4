{{- if .Values.linux.enabled }}
kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: {{ template "sscd.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "sscd.labels" . | indent 4 }}
{{- if .Values.linux.daemonsetAnnotations }}
  annotations:
{{ toYaml .Values.linux.daemonsetAnnotations | indent 4 }}
{{- end }}
spec:
  selector:
    matchLabels:
      app: {{ template "sscd.name" . }}
  updateStrategy:
{{ toYaml .Values.linux.updateStrategy | indent 4 }}
  template:
    metadata:
      labels:
{{ include "sscd.labels" . | indent 8 }}
{{- if .Values.linux.podLabels }}
{{- toYaml .Values.linux.podLabels | nindent 8 }}
{{- end }}
      annotations:
        kubectl.kubernetes.io/default-container: secrets-store
{{- if .Values.linux.podAnnotations }}
{{ toYaml .Values.linux.podAnnotations | indent 8 }}
{{- end }}
    spec:
      serviceAccountName: secrets-store-csi-driver
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
        {{ toYaml .Values.imagePullSecrets | indent 8 }}
      {{- end }}
      affinity:
{{ toYaml .Values.linux.affinity | indent 8 }}
{{- if .Values.priority_class_name }}
      priorityClassName: {{.Values.priority_class_name}}
{{- end}}
      containers:
        - name: node-driver-registrar
          {{- if .Values.linux.registrarImage.digest }}
          image: "{{ .Values.linux.registrarImage.repository }}@{{ .Values.linux.registrarImage.digest }}"
          {{- else }}
          image: "{{ .Values.linux.registrarImage.repository }}:{{ .Values.linux.registrarImage.tag }}"
          {{- end }}
          args:
            - --v={{ .Values.linux.registrar.logVerbosity }}
            - --csi-address=/csi/csi.sock
            - --kubelet-registration-path={{ .Values.linux.kubeletRootDir }}/plugins/csi-secrets-store/csi.sock
          imagePullPolicy: {{ .Values.linux.registrarImage.pullPolicy }}
          volumeMounts:
            - name: plugin-dir
              mountPath: /csi
            - name: registration-dir
              mountPath: /registration
{{- with .Values.linux.registrar.resources }}
          resources:
{{ toYaml . | indent 12 }}
{{- end }}
        - name: secrets-store
          {{- if .Values.linux.image.digest }}
          image: "{{ .Values.linux.image.repository }}@{{ .Values.linux.image.digest }}"
          {{- else }}
          image: "{{ .Values.linux.image.repository }}:{{ .Values.linux.image.tag }}"
          {{- end }}
          args:
            {{- if .Values.logVerbosity }}
            - -v={{ .Values.logVerbosity }}
            {{- end }}
            {{- if .Values.logFormatJSON }}
            - --log-format-json={{ .Values.logFormatJSON }}
            {{- end }}
            - "--endpoint=$(CSI_ENDPOINT)"
            - "--nodeid=$(KUBE_NODE_NAME)"
            - "--provider-volume={{ .Values.linux.providersDir }}"
            - "--additional-provider-volume-paths={{ join "," .Values.linux.additionalProvidersDirs }}"
            {{- if .Values.enableSecretRotation }}
            - "--enable-secret-rotation={{ .Values.enableSecretRotation }}"
            {{- end }}
            {{- if .Values.rotationPollInterval }}
            - "--rotation-poll-interval={{ .Values.rotationPollInterval }}"
            {{- end }}
            - "--metrics-addr={{ .Values.linux.metricsAddr }}"
            {{- if .Values.providerHealthCheck }}
            - "--provider-health-check={{ .Values.providerHealthCheck }}"
            {{- end }}
            {{- if .Values.providerHealthCheckInterval }}
            - "--provider-health-check-interval={{ .Values.providerHealthCheckInterval }}"
            {{- end }}
            {{- if .Values.maxCallRecvMsgSize }}
            - "--max-call-recv-msg-size={{ .Values.maxCallRecvMsgSize | int64 }}"
            {{- end }}
          env:
          {{- with .Values.linux.env }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
          - name: CSI_ENDPOINT
            value: unix:///csi/csi.sock
          - name: KUBE_NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          imagePullPolicy: {{ .Values.linux.image.pullPolicy }}
          securityContext:
            privileged: true
          ports:
            - containerPort: {{ .Values.livenessProbe.port }}
              name: healthz
              protocol: TCP
            - containerPort: {{ trimPrefix ":" .Values.linux.metricsAddr }}
              name: metrics
              protocol: TCP
          livenessProbe:
              failureThreshold: 5
              httpGet:
                path: /healthz
                port: healthz
              initialDelaySeconds: 30
              timeoutSeconds: 10
              periodSeconds: 15
          volumeMounts:
            - name: plugin-dir
              mountPath: /csi
            - name: mountpoint-dir
              mountPath: {{ .Values.linux.kubeletRootDir }}/pods
              mountPropagation: Bidirectional
            {{- $providersDir := .Values.linux.providersDir }}
            - name: providers-dir
              mountPath: {{ $providersDir }}
            {{- range $i, $path := .Values.linux.additionalProvidersDirs }}
            {{- if ne $path $providersDir }}
            - name: providers-dir-{{ $i }}
              mountPath: "{{ $path }}"
            {{- end }}
            {{- end }}
            {{- if .Values.linux.volumeMounts }}
              {{- toYaml .Values.linux.volumeMounts | nindent 12 }}
            {{- end }}
{{- with .Values.linux.driver.resources }}
          resources:
{{ toYaml . | indent 12 }}
{{- end }}
        - name: liveness-probe
          {{- if .Values.linux.livenessProbeImage.digest }}
          image: "{{ .Values.linux.livenessProbeImage.repository }}@{{ .Values.linux.livenessProbeImage.digest }}"
          {{- else }}
          image: "{{ .Values.linux.livenessProbeImage.repository }}:{{ .Values.linux.livenessProbeImage.tag }}"
          {{- end }}
          imagePullPolicy: {{ .Values.linux.livenessProbeImage.pullPolicy }}
          args:
          - --csi-address=/csi/csi.sock
          - --probe-timeout=3s
          - --http-endpoint=0.0.0.0:{{ .Values.livenessProbe.port }}
          - -v={{ .Values.livenessProbe.logLevel }}
          volumeMounts:
            - name: plugin-dir
              mountPath: /csi
{{- with .Values.linux.livenessProbe.resources }}
          resources:
{{ toYaml . | indent 12 }}
{{- end }}
      {{- if .Values.linux.priorityClassName }}
      priorityClassName: {{ .Values.linux.priorityClassName | quote }}
      {{- end }}
      volumes:
        - name: mountpoint-dir
          hostPath:
            path: {{ .Values.linux.kubeletRootDir }}/pods
            type: DirectoryOrCreate
        - name: registration-dir
          hostPath:
            path: {{ .Values.linux.kubeletRootDir }}/plugins_registry/
            type: Directory
        - name: plugin-dir
          hostPath:
            path: {{ .Values.linux.kubeletRootDir }}/plugins/csi-secrets-store/
            type: DirectoryOrCreate
        {{- $providersDir := .Values.linux.providersDir }}
        - name: providers-dir
          hostPath:
            path: {{ $providersDir }}
            type: DirectoryOrCreate
        {{- range $i, $path := .Values.linux.additionalProvidersDirs }}
        {{- if ne $path $providersDir }}
        - name: providers-dir-{{ $i }}
          hostPath:
            path: "{{ $path }}"
            type: DirectoryOrCreate
        {{- end }}
        {{- end }}
        {{- if .Values.linux.volumes }}
          {{- toYaml .Values.linux.volumes | nindent 8 }}
        {{- end }}
      nodeSelector:
        kubernetes.io/os: linux
{{- if .Values.linux.nodeSelector }}
{{- toYaml .Values.linux.nodeSelector | nindent 8 }}
{{- end }}
{{- with .Values.linux.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
{{- end }}
{{- end -}}
