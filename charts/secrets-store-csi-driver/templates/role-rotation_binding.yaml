{{ if .Values.enableSecretRotation }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: secretproviderrotation-rolebinding
  labels:
{{ include "sscd.labels" . | indent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: secretproviderrotation-role
subjects:
- kind: ServiceAccount
  name: secrets-store-csi-driver
  namespace: {{ .Release.Namespace }}
{{ end }}
