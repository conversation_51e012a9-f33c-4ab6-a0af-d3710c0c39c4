---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.3
  name: secretproviderclasses.secrets-store.csi.x-k8s.io
spec:
  group: secrets-store.csi.x-k8s.io
  names:
    kind: SecretProviderClass
    listKind: SecretProviderClassList
    plural: secretproviderclasses
    singular: secretproviderclass
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: SecretProviderClass is the Schema for the secretproviderclasses
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: SecretProviderClassSpec defines the desired state of SecretProviderClass
            properties:
              parameters:
                additionalProperties:
                  type: string
                description: Configuration for specific provider
                type: object
              provider:
                description: Configuration for provider name
                type: string
              secretObjects:
                items:
                  description: SecretObject defines the desired state of synced K8s
                    secret objects
                  properties:
                    annotations:
                      additionalProperties:
                        type: string
                      description: annotations of k8s secret object
                      type: object
                    data:
                      items:
                        description: SecretObjectData defines the desired state of
                          synced K8s secret object data
                        properties:
                          key:
                            description: data field to populate
                            type: string
                          objectName:
                            description: name of the object to sync
                            type: string
                        type: object
                      type: array
                    labels:
                      additionalProperties:
                        type: string
                      description: labels of K8s secret object
                      type: object
                    secretName:
                      description: name of the K8s secret object
                      type: string
                    type:
                      description: type of K8s secret object
                      type: string
                  type: object
                type: array
            type: object
          status:
            description: SecretProviderClassStatus defines the observed state of SecretProviderClass
            type: object
        type: object
    served: true
    storage: true
  - deprecated: true
    deprecationWarning: secrets-store.csi.x-k8s.io/v1alpha1 is deprecated. Use secrets-store.csi.x-k8s.io/v1
      instead.
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: SecretProviderClass is the Schema for the secretproviderclasses
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: SecretProviderClassSpec defines the desired state of SecretProviderClass
            properties:
              parameters:
                additionalProperties:
                  type: string
                description: Configuration for specific provider
                type: object
              provider:
                description: Configuration for provider name
                type: string
              secretObjects:
                items:
                  description: SecretObject defines the desired state of synced K8s
                    secret objects
                  properties:
                    annotations:
                      additionalProperties:
                        type: string
                      description: annotations of k8s secret object
                      type: object
                    data:
                      items:
                        description: SecretObjectData defines the desired state of
                          synced K8s secret object data
                        properties:
                          key:
                            description: data field to populate
                            type: string
                          objectName:
                            description: name of the object to sync
                            type: string
                        type: object
                      type: array
                    labels:
                      additionalProperties:
                        type: string
                      description: labels of K8s secret object
                      type: object
                    secretName:
                      description: name of the K8s secret object
                      type: string
                    type:
                      description: type of K8s secret object
                      type: string
                  type: object
                type: array
            type: object
          status:
            description: SecretProviderClassStatus defines the observed state of SecretProviderClass
            properties:
              byPod:
                items:
                  description: |-
                    ByPodStatus defines the state of SecretProviderClass as seen by
                    an individual controller
                  properties:
                    id:
                      description: id of the pod that wrote the status
                      type: string
                    namespace:
                      description: namespace of the pod that wrote the status
                      type: string
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: false
