---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.3
  name: secretproviderclasspodstatuses.secrets-store.csi.x-k8s.io
spec:
  group: secrets-store.csi.x-k8s.io
  names:
    kind: SecretProviderClassPodStatus
    listKind: SecretProviderClassPodStatusList
    plural: secretproviderclasspodstatuses
    singular: secretproviderclasspodstatus
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: SecretProviderClassPodStatus is the Schema for the secretproviderclassespodstatus
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          status:
            description: SecretProviderClassPodStatusStatus defines the observed state
              of SecretProviderClassPodStatus
            properties:
              mounted:
                type: boolean
              objects:
                items:
                  description: SecretProviderClassObject defines the object fetched
                    from external secrets store
                  properties:
                    id:
                      type: string
                    version:
                      type: string
                  type: object
                type: array
              podName:
                type: string
              secretProviderClassName:
                type: string
              targetPath:
                type: string
            type: object
        type: object
    served: true
    storage: true
  - deprecated: true
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: SecretProviderClassPodStatus is the Schema for the secretproviderclassespodstatus
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          status:
            description: SecretProviderClassPodStatusStatus defines the observed state
              of SecretProviderClassPodStatus
            properties:
              mounted:
                type: boolean
              objects:
                items:
                  description: SecretProviderClassObject defines the object fetched
                    from external secrets store
                  properties:
                    id:
                      type: string
                    version:
                      type: string
                  type: object
                type: array
              podName:
                type: string
              secretProviderClassName:
                type: string
              targetPath:
                type: string
            type: object
        type: object
    served: true
    storage: false
