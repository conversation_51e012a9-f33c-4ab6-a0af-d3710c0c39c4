---

apiVersion: v1
kind: ConfigMap
metadata:
  name: istio-upgrade
  labels:
    app: istio-upgrade
data:
  common.sh: |-
    ## Checking kubectl permission
    #
    if ! ( kubectl auth can-i list ns --all-namespaces && kubectl auth can-i get deploy --all-namespaces && \
      kubectl auth can-i get sts --all-namespaces && kubectl auth can-i get ds --all-namespaces && kubectl auth can-i get pods --all-namespaces
      ) >/dev/null; then
      echo "kubectl auth not enough..."
      exit 1
    fi

    ## Declare an associative map to store the namespace to deployment map that need to be rollout
    #
    declare -A TODO

    echo "verifying namespace labels and pods..."
    ## Get the name of the namespace with istio-injection=enabled labels
    #
    for i in $(kubectl get ns -l {{ .Values.configs.common.nsLabels | join "," }} -ocustom-columns=1:.metadata.name --no-headers); do
      
      echo "checking $i namespace..."
      ## Get deployment in the targeted ns and it's selectors
      #
      for j in $(kubectl get deploy,ds,sts -n $i -o go-template={{`'{{range .items}}{{.kind}}/{{.metadata.name}}_{{$first := false}}{{range $k,$v := .spec.selector.matchLabels}}{{if $first}},{{end}}{{$k}}={{$v}}{{$first = true}}{{end}} {{end}}'`}}); do

        ## Get non terminating running pods with istio-proxy that's not the targeted version
        #
        if ! kubectl get pod -o="custom-columns=1:.spec.containers[*].name,2:.status.phase,3:.metadata.deletionTimestamp,4:.metadata.annotations.sidecar\.istio\.io/status" --no-headers -n=$i -l=${j##*_} | \
          awk '$1 ~ /istio-proxy/ && $2 ~ /Running/ && $3 ~ /<none>/ && $4 !~ /(<none>|"revision":"{{ .Values.configs.targetRef }}")/ {exit 1}'; then

            echo "found ${j}..."
            ## Store the namespace -> deployment name in the map
            #
            TODO["$i"]+="${j%%_*} "
        fi
      done
    done

    ## Check if the target revision exist in the cluster
    #
    if [[ "${#TODO[@]}" -ne 0 ]] && [[ -z "$(kubectl get po -nistio-system -l istio.io/rev={{ .Values.configs.targetRef }} --no-headers)" ]]; then
      echo "{{ .Values.configs.targetRef }} version does not exist in istio-system please verify..."
      exit 1
    fi
{{- if .Values.configs.probing.enabled }}
  probing.sh : |-
    #!/bin/env bash
    set -e

    ## Check if istio sidecar is injected to the pod
    #
    INJECT=$(awk -F'=' '/sidecar.istio.io\/status/ {print $2}' /injection-status/annotations)
    if [[ "$INJECT" = "" ]]; then
      echo "probably not an istio mesh cluster exiting..."
      exit 0
    fi
    echo "found sidecar: ${INJECT}..."

    ## If failed patch current job with ttlSecondsAfterFinished to avoid job object persisting in the
    ## cluster and prevent argocd future attempt to re-sync the job, remove if job succeeded
    #
    EXT="if [[ \$? -ne 0 ]]; then \
      kubectl patch job -n $POD_NS $JOB_NAME -p '{\"spec\": {\"ttlSecondsAfterFinished\": 60}}'; \
    else \
      kubectl patch job -n $POD_NS $JOB_NAME --type=json -p='[{\"op\": \"remove\", \"path\": \"/spec/ttlSecondsAfterFinished\"}]' 2>/dev/null || true; fi"

    ## If injected is not empty set trap to terminate sidecar when main container exit
    ## else job will be stuck at initializing state as sidecar will never exit
    #
    SCR="echo 'terminating istio-proxy...';curl --max-time 2 -s -f -XPOST http://127.0.0.1:15020/quitquitquit; echo"; trap "$EXT;$SCR" EXIT

    cd {{ .Values.volumeMountPath }}
    source common.sh 

    ## Check if todo is empty skip probing if true
    #
    if [ "${#TODO[@]}" -eq 0 ]; then
      echo "nothing found skipped..."
      exit 0
    fi 

    ## Check if rolling forward or backward if rolling backward skip probing
    #
    if [[ "$(kubectl get ns $POD_NS -o go-template={{`'{{ range $k, $v := .metadata.labels }}{{if eq $k "istio.io/rev"}}{{$v}}{{end}}{{end}}'`}})" = "{{ .Values.configs.targetRef }}" ]]; then
      echo "roll forward operation, continue probing..."
    else
      echo "roll backward operation, skipping probing..."
      exit 0
    fi

    ## check if the correct sidecar was injected
    #
    echo "checking injected version..."
    if ! echo $INJECT | grep -qF '\"revision\":\"{{ .Values.configs.targetRef }}\"'; then
      echo "not a target ref sidecar"
      exit 1
    fi
    echo "{{ .Values.configs.targetRef }} version found..."

  {{- if .Values.configs.probing.probes.authPolicy }}
    ## start a python server for verifications
    #
    python -m http.server {{ .Values.configs.probing.servicePort }} >/tmp/log 2>&1 &

    ## run a pod with previous version and check the connection to the new version ie. this pod's istio-proxy
    #
    POD_NAME="istio-$(echo $RANDOM | md5sum | head -c 6)"

    echo "creating namespace ${POD_NAME}..."
    kubectl create namespace $POD_NAME
    kubectl label ns $POD_NAME {{ .Values.configs.common.nsLabels | join " " }} app=istio-upgrade-curl-test-ns
    SCR="echo 'deleting ns...';kubectl delete ns -l app=istio-upgrade-curl-test-ns;$SCR"; trap "$EXT;$SCR" EXIT

    echo "applying auth policy..."
    echo "apiVersion: security.istio.io/v1beta1
    kind: AuthorizationPolicy
    metadata:
      name: $POD_NAME
      namespace: $POD_NS
      labels:
        app: istio-curl-test
    spec:
      action: ALLOW
      rules:
      - from:
        - source:
            principals:
            - cluster.local/ns/$POD_NAME/sa/default
      selector:
        matchLabels:
          app: istio-probing" | kubectl apply -f -
    SCR="echo 'deleting authpol...';kubectl delete -n $POD_NS authorizationpolicy -l app=istio-curl-test;$SCR"; trap "$EXT;$SCR" EXIT

    kubectl run -qn $POD_NAME $POD_NAME --image={{ .Values.replicas.image }} -l app=istio-curl-test -- sh -c "curl -fLkm 10 --retry-all-errors --connect-timeout 5 --retry 5 --retry-max-time 25 -w '%{http_code}' http://istio-probing.${POD_NS}:{{ .Values.configs.probing.servicePort }}; sleep 3600"
    SCR="echo 'deleting pod...';kubectl delete --force --grace-period=0 -n $POD_NAME po -l app=istio-curl-test;$SCR"; trap "$EXT;$SCR" EXIT
    kubectl wait --for=condition=ready pod --timeout={{ .Values.configs.probing.timeout }} -n $POD_NAME $POD_NAME

    ## check if python server log contain the probing
    #
    for i in {1..7}; do
      if ! awk '/"GET \/ HTTP\/1.1" 200/ {exit 1}' /tmp/log; then
        echo "keyword found http code 200..."
        break
      fi

      echo "retrying $i..."
      if [[ $i -eq 7 ]]; then
        echo "failed curl test, keyword not found: $(cat /tmp/log)..."
        exit 1
      fi

      sleep $i
    done
  {{- end }}

  {{- if .Values.configs.probing.probes.sentry }}
    ## check connection of this pod's proxy to the previous version
    #
    RES=$(curl -Lkso /dev/null -m 10 --retry-connrefused --connect-timeout 5 --retry 5 --retry-max-time 25 -w '%{http_code}' http://sentry-t6-alb.sentry-t6:8080/health_check)
    echo -n "sentry reply: ${RES}..."
    if [ "$RES" != "200" ]; then
      echo "reply nok"
      exit 1
    fi
    echo "reply ok"
  {{- end }}

    echo "probing completed..."
{{- end }}
  promote.sh: |-
    #!/bin/env bash
    set -e

    cd {{ .Values.volumeMountPath }}
    source common.sh

    ## Check if the non-revision injector webhook exist and delete it to prevent injection by two different control plane
    #
    if [[ -n "$(kubectl get mutatingwebhookconfiguration istio-sidecar-injector 2>/dev/null)" ]] && [[ "{{ .Values.configs.targetRef }}" != "default" ]]; then
      kubectl delete mutatingwebhookconfiguration istio-sidecar-injector
    fi

    ## Point the default tag to the targeted version
    #
    [[ "${#TODO[@]}" -ne 0 ]] && kubectl apply -f {{ .Values.volumeMountPath }}/tag.yaml

    ## Create a map to store failed rollout
    #
    declare -A ERR

    ## Rollout restart the deployments to reinject the proxy sidecar
    #
    for i in "${!TODO[@]}"; do
      for j in ${TODO[$i]}; do
        kubectl rollout restart -n $i $j

        ## Add the deployment to the err map if rollout failed
        #
        if ! kubectl rollout status -n $i $j --timeout={{ .Values.configs.promote.timeout }}; then
          ERR[$i]+="$j "
        fi
      done
    done

    ## Output the failed rollout for further debugging exit 1 to fail the job. argocd will retry if auto sync or
    ## show the sync fail status if manual
    #
    if [ "${#ERR[@]}" -ne 0 ]; then  
        echo 'Failed rollout for:'
        for i in "${!ERR[@]}"; do
          echo -e "\t$i:\t${ERR[$i]}"
        done
      {{- if not .Values.configs.ignoreErrors }}
        exit 1
      {{- end }}
    fi

    ## verify that no running pod that's still using old version 
    #
    for i in {1..10}; do
      echo "verifying no more running pods with the previous version in this cluster..."

      ## test across all namespaces for running pods using the old version, test fails if pods still terminating
      #
      if ! kubectl get pod -Ao="custom-columns=1:.spec.containers[*].name,2:.status.phase,3:.metadata.annotations.sidecar\.istio\.io/status,4:.metadata.ownerReferences[0].kind" --no-headers | \
        awk '$1 ~ /istio-proxy/ && $2 ~ /Running/ && $3 !~ /(<none>|"revision":"{{ .Values.configs.targetRef }}")/ && $4 !~ /Job/ {exit 1}'; then

          ## if tried 10 times exit with error for further debugging
          #
          if [[ $i -eq 10 ]]; then
            echo "running/terminating pods with istio-proxy still not using {{ .Values.configs.targetRef }} version in this cluster please verify..."
          {{- if .Values.configs.ignoreErrors }}
            exit 0
          {{- else }}
            exit 1
          {{- end }}
          fi

          echo "retrying: $i times..."
          sleep $(( i * 10 ))
          continue
      else
          echo "no more running pods with previous version in the cluster (except Jobs)..."
          break
      fi
    done

    ## Upgrade successful
    #
    echo "completed successfully"
    exit 0
{{- $svcNm := list "istiod" }}
{{- if ne .Values.configs.targetRef "default" }}
{{- $svcNm = append $svcNm .Values.configs.targetRef }}
{{- end }}
  tag.yaml: |-
    ---

    apiVersion: admissionregistration.k8s.io/v1
    kind: MutatingWebhookConfiguration
    metadata:
      name: istio-revision-tag-default
      labels:
        istio.io/tag: default
        istio.io/rev: {{ .Values.configs.targetRef }}
        operator.istio.io/component: "Pilot"
        app: sidecar-injector
    webhooks:
    - name: rev.namespace.sidecar-injector.istio.io
      clientConfig:
        service:
          name: {{ $svcNm | join "-" }}
          namespace: istio-system
          path: "/inject"
          port: 443
      sideEffects: None
      rules:
      - operations: [ "CREATE" ]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
      failurePolicy: Fail
      admissionReviewVersions: ["v1beta1", "v1"]
      namespaceSelector:
        matchExpressions:
        - key: istio.io/rev
          operator: In
          values:
          - "default"
        - key: istio-injection
          operator: DoesNotExist
      objectSelector:
        matchExpressions:
        - key: sidecar.istio.io/inject
          operator: NotIn
          values:
          - "false"
    - name: rev.object.sidecar-injector.istio.io
      clientConfig:
        service:
          name: {{ $svcNm | join "-" }}
          namespace: istio-system
          path: "/inject"
          port: 443
      sideEffects: None
      rules:
      - operations: [ "CREATE" ]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
      failurePolicy: Fail
      admissionReviewVersions: ["v1beta1", "v1"]
      namespaceSelector:
        matchExpressions:
        - key: istio.io/rev
          operator: DoesNotExist
        - key: istio-injection
          operator: DoesNotExist
      objectSelector:
        matchExpressions:
        - key: sidecar.istio.io/inject
          operator: NotIn
          values:
          - "false"
        - key: istio.io/rev
          operator: In
          values:
          - "default"
    - name: namespace.sidecar-injector.istio.io
      clientConfig:
        service:
          name: {{ $svcNm | join "-" }}
          namespace: istio-system
          path: "/inject"
          port: 443
      sideEffects: None
      rules:
      - operations: [ "CREATE" ]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
      failurePolicy: Fail
      admissionReviewVersions: ["v1beta1", "v1"]
      namespaceSelector:
        matchExpressions:
        - key: istio-injection
          operator: In
          values:
          - enabled
      objectSelector:
        matchExpressions:
        - key: sidecar.istio.io/inject
          operator: NotIn
          values:
          - "false"
    - name: object.sidecar-injector.istio.io
      clientConfig:
        service:
          name: {{ $svcNm | join "-" }}
          namespace: istio-system
          path: "/inject"
          port: 443
      sideEffects: None
      rules:
      - operations: [ "CREATE" ]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
      failurePolicy: Fail
      admissionReviewVersions: ["v1beta1", "v1"]
      namespaceSelector:
        matchExpressions:
        - key: istio-injection
          operator: DoesNotExist
        - key: istio.io/rev
          operator: DoesNotExist
      objectSelector:
        matchExpressions:
        - key: sidecar.istio.io/inject
          operator: In
          values:
          - "true"
        - key: istio.io/rev
          operator: DoesNotExist
