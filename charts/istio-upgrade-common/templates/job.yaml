{{- if .Values.configs.probing.enabled }}
---

apiVersion: batch/v1
kind: Job
metadata:
  name: istio-probing-{{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum | trunc 6 }}
spec:
  backoffLimit: {{ .Values.configs.probing.retries }}
  completions: 1
  parallelism: 1
  template:
    metadata:
      labels:
        app: istio-probing
      {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- if .Values.podAnnotations }}
      annotations:
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- end }}
    spec:
      containers:
      - name: istio-probing
        env:
      {{- with .Values.envVars }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
        - name: JOB_NAME
          value: istio-probing-{{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum | trunc 6 }}
        - name: ENV
          value: {{ .Values.env }}
        - name: POD_NS
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        image: {{ .Values.replicas.image }}
        resources:
          {{- toYaml .Values.resources | nindent 10 }}
        ports:
        - containerPort: {{ .Values.configs.probing.servicePort }}
        command:
        - bash
        - "{{ .Values.volumeMountPath }}/probing.sh"
        volumeMounts:
        - mountPath: {{ .Values.volumeMountPath }}
          name: config-volume
        - mountPath: /injection-status
          name: inject-stats
      restartPolicy: Never
      securityContext:
        runAsNonRoot: true
        runAsUser: {{ .Values.securityContext.runAsUser | default 65532 }}
        runAsGroup: {{ .Values.securityContext.runAsGroup | default 65532 }}
        fsGroup: 65532
      serviceAccountName: istio-upgrade
      volumes:
      - name: inject-stats
        downwardAPI:
          defaultMode: 420
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.annotations
            path: annotations
      - name: config-volume
        configMap:
          name: istio-upgrade
          defaultMode: 0755
{{- end }}