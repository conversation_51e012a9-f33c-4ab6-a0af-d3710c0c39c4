---

apiVersion: v1
kind: ServiceAccount
metadata:
  name: istio-upgrade
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istio-upgrade
subjects:
  - kind: ServiceAccount
    name: istio-upgrade
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io

# ---

# apiVersion: rbac.authorization.k8s.io/v1
# kind: ClusterRole
# metadata:
#   name: istio-upgrade
#   annotations:
#     argocd.argoproj.io/hook: PreSync
#     argocd.argoproj.io/hook-delete-policy: HookSucceeded
# rules:
# - apiGroups:
#   - '*'
#   resources:
#   - '*'
#   verbs:
#   - '*'
# - nonResourceURLs:
#   - '*'
#   verbs:
#   - '*'
