---

apiVersion: batch/v1
kind: Job
metadata:
  name: istio-promote
  annotations:
    argocd.argoproj.io/hook: PostSync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
  labels:
    app: istio-promote
spec:
  backoffLimit: {{ .Values.configs.promote.retries }}
  completions: 1
  parallelism: 1
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        app: istio-promote
      {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      containers:
        - image: {{ .Values.replicas.image }}
          command:
            - /bin/bash
            - "{{ .Values.volumeMountPath }}/promote.sh"
          name: istio-promote
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
          {{- with .Values.envVars }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
            - name: ENV
              value: {{ .Values.env }}
          volumeMounts:
            - mountPath: {{ .Values.volumeMountPath }}
              name: config-volume
      restartPolicy: Never
      serviceAccountName: istio-upgrade
      securityContext:
        runAsNonRoot: true
        runAsUser: {{ .Values.securityContext.runAsUser | default 65532 }}
        runAsGroup: {{ .Values.securityContext.runAsGroup | default 65532 }}
        fsGroup: 65532
      volumes:
        - name: config-volume
          configMap:
            name: istio-upgrade
            defaultMode: 0755
