replicas:
  image: 712221657655.dkr.ecr.ap-southeast-1.amazonaws.com/mirror/docker.io/alpine/k8s:1.25.13

configs:
  ignoreErrors: false
  common:
    nsLabels:
    - istio-injection=enabled
    # - istio.gx/upgrade=auto
  probing:
    retries: 2
    enabled: true
    timeout: 300s
    servicePort: 8080
    probes:
      authPolicy: true
      ## stg and prd network don't have security group that enable 8080 port communication
      #
      sentry: false
  promote:
    retries: 1
    timeout: 600s

volumeMountPath: /config_files

podAnnotations: {}
podLabels: {}

serviceAccount:
  annotations: {}

resources:
  limits:
    memory: 200Mi
  requests:
    cpu: 25m
    memory: 200Mi

securityContext: {}
  # runAsGroup: 1000
  # runAsUser: 1000