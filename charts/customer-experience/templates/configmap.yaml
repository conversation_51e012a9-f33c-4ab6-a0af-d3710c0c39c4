{{- include "go-app-lib.configmap" (list . "customer-experience.configmap") -}}

{{- define "customer-experience.configmap" -}}
metadata:
  annotations:
    argocd.argoproj.io/sync-options: Replace=true
    
data:
  config.json: |-
    {{- .Files.Get .Values.configName | nindent 4 }}
  industry.csv: |-
    {{- .Files.Get .Values.industryConf | nindent 4 }}
  industry_exemption.csv: |-
    {{- .Files.Get .Values.industryExemptionConf | nindent 4 }}
  product.csv: |-
    {{- .Files.Get .Values.productConf | nindent 4 }}
  countryRiskMapping.csv: |-
    {{- .Files.Get .Values.countryRiskConf | nindent 4 }}
  employment.csv: |-
    {{- .Files.Get .Values.employmentConf | nindent 4 }}

{{- end -}}
