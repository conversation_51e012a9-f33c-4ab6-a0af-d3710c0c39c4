{{- define "customer-experience.deployment" -}}
{{- end -}}

{{- include "go-app-lib.deployment" (list . "customer-experience.deployment") }}
---
{{- define "customer-experience.rollout" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rollout" (list . "customer-experience.rollout") }}
---
{{- end -}}

{{- define "customer-experience.rolloutvirtualservice" -}}
{{- end -}}

{{- if and .Values.rollout .Values.argorollouts.rolloutTrafficRoutingEnabled  -}}
{{- include "go-app-lib.rolloutvirtualservice" (list . "customer-experience.rolloutvirtualservice") }}
---
{{- end -}}

{{- define "customer-experience.rolloutdestinationrule" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rolloutdestinationrule" (list . "customer-experience.rolloutdestinationrule") }}
---
{{- end -}}

{{- define "customer-experience.analysistemplate" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.analysistemplate" (list . "customer-experience.analysistemplate") }}
---
{{- end -}}

{{- define "customer-experience.service" -}}
{{- end -}}

{{- include "go-app-lib.service" (list . "customer-experience.service") }}
---
{{- define "customer-experience.hpa" -}}
{{- end -}}

{{- if .Values.autoscaling.enabled }}
{{- include "go-app-lib.hpa" (list . "customer-experience.hpa") }}
---
{{- end -}}

{{- define "customer-experience.ingress" -}}
{{- end -}}

{{- if .Values.ingress.enabled -}}
{{- include "go-app-lib.ingress" (list . "customer-experience.ingress") }}
---
{{- end -}}

{{- define "customer-experience.serviceaccount" -}}
{{- end -}}

{{- if .Values.serviceAccount.create -}}
{{- include "go-app-lib.serviceaccount" (list . "customer-experience.serviceaccount") }}
---
{{- end -}}

{{- define "customer-experience.gateway" -}}
{{- end -}}

{{- define "customer-experience.virtualservice" -}}
{{- end -}}

{{- if .Values.gateway.enabled -}}
{{- include "go-app-lib.gateway" (list . "customer-experience.gateway") }}
---
{{- include "go-app-lib.virtualservice" (list . "customer-experience.virtualservice") }}
---
{{- end -}}

{{- define "customer-experience.authorizationpolicy" -}}
{{- end -}}

{{- if .Values.authorizationpolicy.enabled -}}
{{- include "go-app-lib.authorizationpolicy" (list . "customer-experience.authorizationpolicy") }}
---
{{- end -}}

{{- define "customer-experience.podDisruptionBudget" -}}
{{- end -}}

{{- if and (.Values.podDisruptionBudget.enabled) (ne .Values.env "dev") -}}
{{- include "go-app-lib.podDisruptionBudget" (list . "customer-experience.podDisruptionBudget") }}
{{- end }}