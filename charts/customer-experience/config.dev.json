{"name": "customer-experience Service", "serviceName": "customer-experience", "host": "0.0.0.0", "port": 8080, "env": "dev", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "timeoutInMs": 100000, "verificationProvider": {"serviceName": "verification-provider", "baseURL": "http://customer-experience.onboarding.svc.cluster.local"}, "applicationService": {"serviceName": "application-service", "baseURL": "https://backend.dev.g-bank.app/application-service"}, "customerMaster": {"serviceName": "customer-master", "baseURL": "http://customer-master.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "ekycService": {"serviceName": "ekyc-service", "baseURL": "http://ekyc-service.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 30000}}, "perfiosAdapterClient": {"serviceName": "perfios-adapter", "baseURL": "http://perfios-adapter.lending-platform.svc.cluster.local", "circuitBreaker": {"timeout": 1000}}, "ekybService": {"serviceName": "ekyb-service", "baseURL": "http://ekyb-service.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 1000}}, "gdProxy": {"serviceName": "gd-proxy", "baseURL": "http://gd-proxy.fintrust.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "accountService": {"serviceName": "account-service", "baseURL": "http://account-service.core-banking.svc.cluster.local"}, "pigeon": {"serviceName": "pigeon", "baseURL": "http://pigeon.pigeon.svc.cluster.local"}, "referralEngine": {"serviceName": "referral-engine", "baseURL": "http://referral-engine.loyalty.svc.cluster.local"}, "odyssey": {"serviceName": "odyssey", "baseURL": "http://odyssey.loyalty.svc.cluster.local"}, "xm-fileservice": {"baseURL": "http://xm-fileservice.xm-fileservice.svc.cluster.local"}, "riskBroker": {"serviceName": "risk-broker", "baseURL": "http://risk-broker.fintrust.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "griffinClient": {"serviceName": "griffin", "baseURL": "http://gd-proxy.fintrust.svc.cluster.local"}, "grab-id": {"serviceName": "grab-id", "serviceKey": "{{ GRAB_ID_SERVICE_KEY }}", "baseURL": "http://grab-id.identity.svc.cluster.local"}, "partner-grab-id": {"serviceName": "partner-grab-id", "baseURL": "https://partner-api.stg-myteksi.com/grabid", "circuitBreaker": {"timeout": 1000}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/customer_experience?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800ms"}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/customer_experience?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800ms"}, "masterCircuitBreaker": {"timeoutInMs": 1000, "maxConcurrentReq": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000, "maxConcurrentReq": 1000}}}, "kafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "customer-experience", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest"}, "cbKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "cb-customer-experience", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest"}, "craStreamConfig": {"streamID": "dev_customer_risk_assessment", "topic": "dev-customer-risk-assessment", "dtoName": "CustomerRiskAssessment"}, "experimentFeatureConfig": {"LENDING_IN_HOUSE_WORKFLOW": {"salt": "f8fa89d9-142c-4eb7-b7ae-d49bb7645a0d", "percentage": 100, "cacheDurationInMinutes": 30, "whitelistedUserIDs": ["b85212ec-1998-4ab5-8cad-35ce0590ff54"], "blacklistedUserIDs": ["fb66ebc3-0476-4510-8731-40a975733c4a"]}}, "docUploadStreamConfig": {"streamID": "dev_document_detail_event", "topic": "dev-document-detail-event", "dtoName": "DocumentDetail"}, "profileUpdateCRAStreamConfig": {"streamID": "dev_profile_update_customer_risk_assessment", "topic": "dev-profile-update-customer-risk-assessment", "dtoName": "CustomerRiskAssessment", "isConsumptionDisabled": true}, "nameScreeningWebhookStreamConfig": {"streamID": "dev_name_screening", "topic": "dev-name-screening", "dtoName": "NameScreening"}, "applicationExpiredStreamConfig": {"streamID": "dev_application_expired", "topic": "dev-application-expired", "dtoName": "ApplicationExpiry"}, "customerDeletionStreamConfig": {"streamID": "dev_customer_deletion", "topic": "dev-customer-deletion", "dtoName": "CustomerDeletion"}, "accountCreatedStreamConfig": {"streamID": "dev_account_created", "topic": "dev-account-created", "dtoName": "AccountCreated"}, "paymentScreeningWebhookStreamConfig": {"streamID": "dev_payment_screening", "topic": "dev-payment-screening", "dtoName": "TransactionScreening"}, "loanApplicationStreamConfig": {"streamID": "dev_loan_app_lifecycle_event", "topic": "dev-loan-app-lifecycle-event", "dtoName": "LoanAppLifecycleEvent"}, "incomeDerivationEventStreamConfig": {"streamID": "dev_income-derivation-event", "topic": "dev-income-derivation-event", "dtoName": "IncomeDerivationEvent"}, "ApplicationStatusTransitionStreamConfig": {"streamID": "dev_application_status_update_transition", "topic": "dev-application-status-update-transition", "dtoName": "ApplicationStatusTransition"}, "depositsAccountDetailStreamConfig": {"streamID": "publisher.depositsAccountDetailPublisher", "topic": "dev-deposits-account-detail-event", "dtoName": "AccountDetail"}, "customerClosingStreamConfig": {"streamID": "dev_customer_closing", "topic": "dev-customer-closing", "dtoName": "CustomerClosing"}, "auditLogStreamConfig": {"streamID": "customer-journal-audit-log", "topic": "dev-audit-log", "dtoName": "AuditLog"}, "referralEventStreamConfig": {"streamID": "dev_referral_event", "topic": "dev-referral-event", "dtoName": "ReferralEvent"}, "customerJournalStreamConfig": {"streamID": "customer-journal-audit_log", "topic": "dev-audit-log", "dtoName": "AuditLog"}, "rewardsGatewayTransactionStreamConfig": {"streamID": "dev_rewards_gateway_tx", "topic": "dev-rewards-gateway-tx", "dtoName": "RewardsGatewayTx"}, "accountPendingActionStreamConfig": {"streamID": "dev_account_pending_action_event", "topic": "dev-account-pending-action-event", "dtoName": "AccountPendingAction"}, "lendingFinexusUpdateStreamConfig": {"streamID": "dev_lending_finexus_update", "topic": "dev-finexus-update", "dtoName": "LendingFinexusUpdate"}, "businessStatusStreamConfig": {"streamID": "dev_business_status", "topic": "dev-business-status", "dtoName": "BusinessStatus"}, "invalidateEmailStreamConfig": {"streamID": "dev_invalidate_email", "topic": "dev-invalidate-email", "dtoName": "InvalidateEmail"}, "depositsAccountCreatedEventStreamConfig": {"streamID": "dev_deposits_account_created_event", "topic": "dev-deposits-account-created-event", "dtoName": "DepositsAccountCreateEvent"}, "nameScreeningResultStreamConfig": {"streamID": "name_screening_result", "topic": "dev-name-screening-result", "dtoName": "NameScreeningResult"}, "documentDetailEventStreamConfig": {"streamID": "dev_document_detail_event", "topic": "dev-document-detail-event", "dtoName": "DocumentDetail"}, "ekycStreamConfig": {"streamID": "dev_ekyc_workflow_completion", "topic": "dev-ekyc-workflow-completion", "dtoName": "Ekyc"}, "s3Config": {"bucketName": "dev-backend-customer-experience-dakota-s3", "sseEncryption": true}, "additionalForms": {"onboardingFIN": ["ea5b0bff-db20-11eb-9004-06813e1cbdae"]}, "indexes": {"collectionPoints": {"v1": {"notificationPreferences": {"collectionPointID": "notification_consent_collection_id", "preferences": [{"purpose": "Promos", "subtitle": "Personalised marketing content, and updates on all the cool stuff we're up to", "options": ["Notification", "Email"]}]}, "dataSharingPreferences": {"collectionPointID": "data_sharing_consent_collection_id", "preferences": [{"purpose": "Third party data sharing consent", "subtitle": "I agree to have my data shared with third parties to personalise my experience as per <a href='https://www.gxbank.my/data-privacy-policy'>Data Privacy Policy</a>", "options": ["Third party"]}, {"purpose": "Future credit product offers consent", "subtitle": "I'm interested in future credit related products and acknowledge to receive credit-related product offers. <a href='gxbankdev://open?screenType=future_credit_terms'>See full terms here</a>", "options": ["Future credit product offers"]}]}}}, "collectionPointsI18n": {"en": {"v1": {"notificationPreferences": {"collectionPointID": "notification_consent_collection_id", "preferences": [{"purpose": "Promos", "subtitle": "Personalised marketing content, and updates on all the cool stuff we're up to", "options": ["Notification", "Email"]}]}, "dataSharingPreferences": {"collectionPointID": "data_sharing_consent_collection_id", "preferences": [{"purpose": "Third party data sharing consent", "subtitle": "I agree to have my data shared with third parties to personalise my experience as per <a href='https://www.gxbank.my/data-privacy-policy'>Data Privacy Policy</a>", "options": ["Third party"]}, {"purpose": "Credit product offers consent", "subtitle": "I’m interested in credit-related products and acknowledge to receive offers related to them. <a href='gxbankdev://open?screenType=future_credit_terms'>See full terms here</a>", "options": ["Credit product offers"]}]}}}, "ms": {"v1": {"notificationPreferences": {"collectionPointID": "notification_consent_collection_id", "preferences": [{"purpose": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> pemasaran yang berse<PERSON><PERSON>n dengan anda dan kemas kini terkini tentang aktiviti menarik", "options": ["Notif<PERSON><PERSON>", "E-mel"]}]}, "dataSharingPreferences": {"collectionPointID": "data_sharing_consent_collection_id", "preferences": [{"purpose": "Persetujuan kongsi data dengan pihak ketiga", "subtitle": "Saya bersetuju untuk berkongsi data saya dengan pihak ketiga untuk mempersonalisikan pengalaman saya mengikut <a href='https://www.gxbank.my/data-privacy-policy'>Dasar Privasi Data</a>", "options": ["Kongsi dengan pihak ketiga"]}, {"purpose": "<PERSON><PERSON><PERSON><PERSON><PERSON> tawaran produk kredit", "subtitle": "<PERSON><PERSON> berm<PERSON>t dengan produk berkaitan kredit dan ingin menerima tawaran berkena<PERSON>ya. <a href='gxbankdev://open?screenType=future_credit_terms'><PERSON>hat terma penuh di sini</a>", "options": ["<PERSON><PERSON><PERSON> produk kredit"]}]}}}}, "duitnowConsent": {"v1": {"dataSharingPreferences": {"collectionPointID": "duit_now_consent_collection_id", "preferences": [{"purpose": "DuitNow consent", "subtitle": "By continuing, you agree to these <a href='https://staging.gxb.com.my/docs/general/duitnow_terms_and_conditions.pdf'>Terms & Conditions</a>", "options": ["DuitNow", "DuitNowTransfer"]}]}}}, "iso-country-to-iso-code": {"v1": {"Afghanistan": "AF", "Aland Islands": "AX", "Albania": "AL", "Algeria": "DZ", "American Samoa": "AS", "Andorra": "AD", "Angola": "AO", "Anguilla": "AI", "Antarctica": "AQ", "Antigua and Barbuda": "AG", "Argentina": "AR", "Armenia": "AM", "Aruba": "AW", "Australia": "AU", "Austria": "AT", "Azerbaijan": "AZ", "Bahamas": "BS", "Bahrain": "BH", "Bangladesh": "BD", "Barbados": "BB", "Belarus": "BY", "Belgium": "BE", "Belize": "BZ", "Benin": "BJ", "Bermuda": "BM", "Bhutan": "BT", "Bolivia": "BO", "Bonaire, Sint Eustatius and Saba": "BQ", "Bosnia and Herzegovina": "BA", "Botswana": "BW", "Bouvet Island": "BV", "Brazil": "BR", "British Indian Ocean Territory": "IO", "Brunei Darussalam": "BN", "Bulgaria": "BG", "Burkina Faso": "BF", "Burundi": "BI", "Cambodia": "KH", "Cameroon": "CM", "Canada": "CA", "Cape Verde": "CV", "Cayman Islands": "KY", "Central African Republic": "CF", "Chad": "TD", "Chile": "CL", "China": "CN", "Christmas Island": "CX", "Cocos (Keeling) Islands": "CC", "Colombia": "CO", "Comoros": "KM", "Congo": "CG", "Congo, The Democratic Republic of": "CD", "Cook Islands": "CK", "Costa Rica": "CR", "Cote d'Ivoire": "CI", "Croatia": "HR", "Cuba": "CU", "Curaçao": "CW", "Cyprus": "CY", "Czechia": "CZ", "Denmark": "DK", "Djibouti": "DJ", "Dominica": "DM", "Dominican Republic": "DO", "Ecuador": "EC", "Egypt": "EG", "El Salvador": "SV", "Equatorial Guinea": "GQ", "Eritrea": "ER", "Estonia": "EE", "Ethiopia": "ET", "Falkland Islands (Malvinas)": "FK", "Faroe Islands": "FO", "Fiji": "FJ", "Finland": "FI", "France": "FR", "French Guiana": "GF", "French Polynesia": "PF", "French Southern Territories": "TF", "Gabon": "GA", "Gambia": "GM", "Georgia": "GE", "Germany": "DE", "Ghana": "GH", "Gibraltar": "GI", "Greece": "GR", "Greenland": "GL", "Grenada": "GD", "Guadeloupe": "GP", "Guam": "GU", "Guatemala": "GT", "Guernsey": "GG", "Guinea": "GN", "Guinea-Bissau": "GW", "Guyana": "GY", "Haiti": "HT", "Heard and Mc Donald Islands": "HM", "Holy See (Vatican City State)": "VA", "Honduras": "HN", "Hong Kong": "HK", "Hungary": "HU", "Iceland": "IS", "India": "IN", "Indonesia": "ID", "Iran, Islamic Republic of": "IR", "Iraq": "IQ", "Ireland": "IE", "Isle of Man": "IM", "Israel": "IL", "Italy": "IT", "Jamaica": "JM", "Japan": "JP", "Jersey": "JE", "Jordan": "JO", "Kazakstan": "KZ", "Kenya": "KE", "Kiribati": "KI", "Korea, Democratic People's Republic of": "KP", "Korea, Republic of": "KR", "Kosovo (temporary code)": "XK", "Kuwait": "KW", "Kyrgyzstan": "KG", "Lao, People's Democratic Republic": "LA", "Latvia": "LV", "Lebanon": "LB", "Lesotho": "LS", "Liberia": "LR", "Libyan Arab Jamahiriya": "LY", "Liechtenstein": "LI", "Lithuania": "LT", "Luxembourg": "LU", "Macao": "MO", "Macedonia, The Former Yugoslav Republic Of": "MK", "Madagascar": "MG", "Malawi": "MW", "Malaysia": "MY", "Maldives": "MV", "Mali": "ML", "Malta": "MT", "Marshall Islands": "MH", "Martinique": "MQ", "Mauritania": "MR", "Mauritius": "MU", "Mayotte": "YT", "Mexico": "MX", "Micronesia, Federated States of": "FM", "Moldova, Republic of": "MD", "Monaco": "MC", "Mongolia": "MN", "Montenegro": "ME", "Montserrat": "MS", "Morocco": "MA", "Mozambique": "MZ", "Myanmar": "MM", "Namibia": "nan", "Nauru": "NR", "Nepal": "NP", "Netherlands": "NL", "Netherlands Antilles": "AN", "New Caledonia": "NC", "New Zealand": "NZ", "Nicaragua": "NI", "Niger": "NE", "Nigeria": "NG", "Niue": "NU", "Norfolk Island": "NF", "Northern Mariana Islands": "MP", "Norway": "NO", "Oman": "OM", "Pakistan": "PK", "Palau": "PW", "Palestinian Territory, Occupied": "PS", "Panama": "PA", "Papua New Guinea": "PG", "Paraguay": "PY", "Peru": "PE", "Philippines": "PH", "Pitcairn": "PN", "Poland": "PL", "Portugal": "PT", "Puerto Rico": "PR", "Qatar": "QA", "Republic of Serbia": "RS", "Reunion": "RE", "Romania": "RO", "Russia Federation": "RU", "Rwanda": "RW", "Saint Barthélemy": "BL", "Saint Helena": "SH", "Saint Kitts & Nevis": "KN", "Saint Lucia": "LC", "Saint Martin": "MF", "Saint Pierre and Miquelon": "PM", "Saint Vincent and the Grenadines": "VC", "Samoa": "WS", "San Marino": "SM", "Sao Tome and Principe": "ST", "Saudi Arabia": "SA", "Senegal": "SN", "Serbia and Montenegro": "CS", "Seychelles": "SC", "Sierra Leone": "SL", "Singapore": "SG", "Sint Maarten": "SX", "Slovakia": "SK", "Slovenia": "SI", "Solomon Islands": "SB", "Somalia": "SO", "South Africa": "ZA", "South Georgia & The South Sandwich Islands": "GS", "South Sudan": "SS", "Spain": "ES", "Sri Lanka": "LK", "Sudan": "SD", "Suriname": "SR", "Svalbard and Jan Mayen": "SJ", "Swaziland": "SZ", "Sweden": "SE", "Switzerland": "CH", "Syrian Arab Republic": "SY", "Taiwan, Province of China": "TW", "Tajikistan": "TJ", "Tanzania, United Republic of": "TZ", "Thailand": "TH", "Timor-Leste": "TL", "Togo": "TG", "Tokelau": "TK", "Tonga": "TO", "Trinidad and Tobago": "TT", "Tunisia": "TN", "Turkey": "TR", "Turkmenistan": "TM", "Turks and Caicos Islands": "TC", "Tuvalu": "TV", "Uganda": "UG", "Ukraine": "UA", "United Arab Emirates": "AE", "United Kingdom": "GB", "United States": "US", "United States Minor Outlying Islands": "UM", "Uruguay": "UY", "Uzbekistan": "UZ", "Vanuatu": "VU", "Venezuela": "VE", "Vietnam": "VN", "Virgin Islands, British": "VG", "Virgin Islands, U.S.": "VI", "Wallis and Futuna": "WF", "Western Sahara": "EH", "Yemen": "YE", "Zambia": "ZM", "Zimbabwe": "ZW"}}, "iso-country-to-iso-code-alpha-3": {"v1": {"Afghanistan": "AFG", "Aland Islands": "ALA", "Albania": "ALB", "Algeria": "DZA", "American Samoa": "ASM", "Andorra": "AND", "Angola": "AGO", "Anguilla": "AIA", "Antarctica": "ATA", "Antigua and Barbuda": "ATG", "Argentina": "ARG", "Armenia": "ARM", "Aruba": "ABW", "Australia": "AUS", "Austria": "AUT", "Azerbaijan": "AZE", "Bahamas": "BHS", "Bahrain": "BHR", "Bangladesh": "BGD", "Barbados": "BRB", "Belarus": "BLR", "Belgium": "BEL", "Belize": "BLZ", "Benin": "BEN", "Bermuda": "BMU", "Bhutan": "BTN", "Bolivia (Plurinational State of)": "BOL", "Bonaire, Sint Eustatius and Saba": "BES", "Bosnia and Herzegovina": "BIH", "Botswana": "BWA", "Bouvet Island": "BVT", "Brazil": "BRA", "British Indian Ocean Territory": "IOT", "Brunei Darussalam": "BRN", "Bulgaria": "BGR", "Burkina Faso": "BFA", "Burundi": "BDI", "Cabo Verde": "CPV", "Cambodia": "KHM", "Cameroon": "CMR", "Canada": "CAN", "Cayman Islands": "CYM", "Central African Republic": "CAF", "Chad": "TCD", "Chile": "CHL", "China": "CHN", "Christmas Island": "CXR", "Cocos (Keeling) Islands": "CCK", "Colombia": "COL", "Comoros": "COM", "Congo": "COG", "Congo, Democratic Republic of the": "COD", "Cook Islands": "COK", "Costa Rica": "CRI", "Côte d'Ivoire": "CIV", "Croatia": "HRV", "Cuba": "CUB", "Curaçao": "CUW", "Cyprus": "CYP", "Czechia": "CZE", "Denmark": "DNK", "Djibouti": "DJI", "Dominica": "DMA", "Dominican Republic": "DOM", "Ecuador": "ECU", "Egypt": "EGY", "El Salvador": "SLV", "Equatorial Guinea": "GNQ", "Eritrea": "ERI", "Estonia": "EST", "Eswatini": "SWZ", "Ethiopia": "ETH", "Falkland Islands (Malvinas)": "FLK", "Faroe Islands": "FRO", "Fiji": "FJI", "Finland": "FIN", "France": "FRA", "French Guiana": "GUF", "French Polynesia": "PYF", "French Southern Territories": "ATF", "Gabon": "GAB", "Gambia": "GMB", "Georgia": "GEO", "Germany": "DEU", "Ghana": "GHA", "Gibraltar": "GIB", "Greece": "GRC", "Greenland": "GRL", "Grenada": "GRD", "Guadeloupe": "GLP", "Guam": "GUM", "Guatemala": "GTM", "Guernsey": "GGY", "Guinea": "GIN", "Guinea-Bissau": "GNB", "Guyana": "GUY", "Haiti": "HTI", "Heard Island and McDonald Islands": "HMD", "Holy See": "VAT", "Honduras": "HND", "Hong Kong": "HKG", "Hungary": "HUN", "Iceland": "ISL", "India": "IND", "Indonesia": "IDN", "Iran (Islamic Republic of)": "IRN", "Iraq": "IRQ", "Ireland": "IRL", "Isle of Man": "IMN", "Israel": "ISR", "Italy": "ITA", "Jamaica": "JAM", "Japan": "JPN", "Jersey": "JEY", "Jordan": "JOR", "Kazakhstan": "KAZ", "Kenya": "KEN", "Kiribati": "KIR", "Korea (Democratic People's Republic of)": "PRK", "Korea, Republic of": "KOR", "Kuwait": "KWT", "Kyrgyzstan": "KGZ", "Lao People's Democratic Republic": "LAO", "Latvia": "LVA", "Lebanon": "LBN", "Lesotho": "LSO", "Liberia": "LBR", "Libya": "LBY", "Liechtenstein": "LIE", "Lithuania": "LTU", "Luxembourg": "LUX", "Macao": "MAC", "Madagascar": "MDG", "Malawi": "MWI", "Malaysia": "MYS", "Maldives": "MDV", "Mali": "MLI", "Malta": "MLT", "Marshall Islands": "MHL", "Martinique": "MTQ", "Mauritania": "MRT", "Mauritius": "MUS", "Mayotte": "MYT", "Mexico": "MEX", "Micronesia (Federated States of)": "FSM", "Moldova, Republic of": "MDA", "Monaco": "MCO", "Mongolia": "MNG", "Montenegro": "MNE", "Montserrat": "MSR", "Morocco": "MAR", "Mozambique": "MOZ", "Myanmar": "MMR", "Namibia": "NAM", "Nauru": "NRU", "Nepal": "NPL", "Netherlands": "NLD", "New Caledonia": "NCL", "New Zealand": "NZL", "Nicaragua": "NIC", "Niger": "NER", "Nigeria": "NGA", "Niue": "NIU", "Norfolk Island": "NFK", "North Macedonia": "MKD", "Northern Mariana Islands": "MNP", "Norway": "NOR", "Oman": "OMN", "Pakistan": "PAK", "Palau": "PLW", "Palestine, State of": "PSE", "Panama": "PAN", "Papua New Guinea": "PNG", "Paraguay": "PRY", "Peru": "PER", "Philippines": "PHL", "Pitcairn": "PCN", "Poland": "POL", "Portugal": "PRT", "Puerto Rico": "PRI", "Qatar": "QAT", "Réunion": "REU", "Romania": "ROU", "Russian Federation": "RUS", "Rwanda": "RWA", "Saint Barthélemy": "BLM", "Saint Helena, Ascension and Tristan da Cunha": "SHN", "Saint Kitts and Nevis": "KNA", "Saint Lucia": "LCA", "Saint Martin (French part)": "MAF", "Saint Pierre and Miquelon": "SPM", "Saint Vincent and the Grenadines": "VCT", "Samoa": "WSM", "San Marino": "SMR", "Sao Tome and Principe": "STP", "Saudi Arabia": "SAU", "Senegal": "SEN", "Serbia": "SRB", "Seychelles": "SYC", "Sierra Leone": "SLE", "Singapore": "SGP", "Sint Maarten (Dutch part)": "SXM", "Slovakia": "SVK", "Slovenia": "SVN", "Solomon Islands": "SLB", "Somalia": "SOM", "South Africa": "ZAF", "South Georgia and theSouth Sandwich Islands": "SGS", "South Sudan": "SSD", "Spain": "ESP", "Sri Lanka": "LKA", "Sudan": "SDN", "Suriname": "SUR", "Svalbard and Jan Mayen": "SJM", "Sweden": "SWE", "Switzerland": "CHE", "Syrian Arab Republic": "SYR", "Taiwan, Province of China": "TWN", "Tajikistan": "TJK", "Tanzania, United Republic of": "TZA", "Thailand": "THA", "Timor-Leste": "TLS", "Togo": "TGO", "Tokelau": "TKL", "Tonga": "TON", "Trinidad and Tobago": "TTO", "Tunisia": "TUN", "Turkey": "TUR", "Turkmenistan": "TKM", "Turks and Caicos Islands": "TCA", "Tuvalu": "TUV", "Uganda": "UGA", "Ukraine": "UKR", "United Arab Emirates": "ARE", "United Kingdom of Great Britain and Northern Ireland": "GBR", "United States of America": "USA", "United States Minor Outlying Islands": "UMI", "Uruguay": "URY", "Uzbekistan": "UZB", "Vanuatu": "VUT", "Venezuela (Bolivarian Republic of)": "VEN", "Viet Nam": "VNM", "Virgin Islands (British)": "VGB", "Virgin Islands (U.S.)": "VIR", "Wallis and Futuna": "WLF", "Western Sahara": "ESH", "Yemen": "YEM", "Zambia": "ZMB", "Zimbabwe": "ZWE"}}, "iso-code-to-iso-country": {"v1": {"AF": "Afghanistan", "AX": "Aland Islands", "AL": "Albania", "DZ": "Algeria", "AS": "American Samoa", "AD": "Andorra", "AO": "Angola", "AI": "<PERSON><PERSON><PERSON>", "AQ": "Antarctica", "AG": "Antigua and Barbuda", "AR": "Argentina", "AM": "Armenia", "AW": "Aruba", "AU": "Australia", "AT": "Austria", "AZ": "Azerbaijan", "BS": "Bahamas", "BH": "Bahrain", "BD": "Bangladesh", "BB": "Barbados", "BY": "Belarus", "BE": "Belgium", "BZ": "Belize", "BJ": "Benin", "BM": "Bermuda", "BT": "Bhutan", "BO": "Bolivia", "BQ": "Bonaire, Sint Eustatius and Saba", "BA": "Bosnia and Herzegovina", "BW": "Botswana", "BV": "Bouvet Island", "BR": "Brazil", "IO": "British Indian Ocean Territory", "BN": "Brunei Darussalam", "BG": "Bulgaria", "BF": "Burkina Faso", "BI": "Burundi", "KH": "Cambodia", "CM": "Cameroon", "CA": "Canada", "CV": "Cape Verde", "KY": "Cayman Islands", "CF": "Central African Republic", "TD": "Chad", "CL": "Chile", "CN": "China", "CX": "Christmas Island", "CC": "Cocos (Keeling) Islands", "CO": "Colombia", "KM": "Comoros", "CG": "Congo", "CD": "Congo, The Democratic Republic of", "CK": "Cook Islands", "CR": "Costa Rica", "CI": "Cote d'Ivoire", "HR": "Croatia", "CU": "Cuba", "CW": "Curaçao", "CY": "Cyprus", "CZ": "Czechia", "DK": "Denmark", "DJ": "Djibouti", "DM": "Dominica", "DO": "Dominican Republic", "EC": "Ecuador", "EG": "Egypt", "SV": "El Salvador", "GQ": "Equatorial Guinea", "ER": "Eritrea", "EE": "Estonia", "ET": "Ethiopia", "FK": "Falkland Islands (Malvinas)", "FO": "Faroe Islands", "FJ": "Fiji", "FI": "Finland", "FR": "France", "GF": "French Guiana", "PF": "French Polynesia", "TF": "French Southern Territories", "GA": "Gabon", "GM": "Gambia", "GE": "Georgia", "DE": "Germany", "GH": "Ghana", "GI": "Gibraltar", "GR": "Greece", "GL": "Greenland", "GD": "Grenada", "GP": "Guadeloupe", "GU": "Guam", "GT": "Guatemala", "GG": "Guernsey", "GN": "Guinea", "GW": "Guinea-Bissau", "GY": "Guyana", "HT": "Haiti", "HM": "Heard and Mc Donald Islands", "VA": "Holy See (Vatican City State)", "HN": "Honduras", "HK": "Hong Kong", "HU": "Hungary", "IS": "Iceland", "IN": "India", "ID": "Indonesia", "IR": "Iran, Islamic Republic of", "IQ": "Iraq", "IE": "Ireland", "IM": "Isle of Man", "IL": "Israel", "IT": "Italy", "JM": "Jamaica", "JP": "Japan", "JE": "Jersey", "JO": "Jordan", "KZ": "Kazakstan", "KE": "Kenya", "KI": "Kiribati", "KP": "Korea, Democratic People's Republic of", "KR": "Korea, Republic of", "XK": "Kosovo (temporary code)", "KW": "Kuwait", "KG": "Kyrgyzstan", "LA": "Lao, People's Democratic Republic", "LV": "Latvia", "LB": "Lebanon", "LS": "Lesotho", "LR": "Liberia", "LY": "Libyan Arab Jam<PERSON>riya", "LI": "Liechtenstein", "LT": "Lithuania", "LU": "Luxembourg", "MO": "Macao", "MK": "Macedonia, The Former Yugoslav Republic Of", "MG": "Madagascar", "MW": "Malawi", "MY": "Malaysia", "MV": "Maldives", "ML": "Mali", "MT": "Malta", "MH": "Marshall Islands", "MQ": "Martinique", "MR": "Mauritania", "MU": "Mauritius", "YT": "Mayotte", "MX": "Mexico", "FM": "Micronesia, Federated States of", "MD": "Moldova, Republic of", "MC": "Monaco", "MN": "Mongolia", "ME": "Montenegro", "MS": "Montserrat", "MA": "Morocco", "MZ": "Mozambique", "MM": "Myanmar", "nan": "Namibia", "NR": "Nauru", "NP": "Nepal", "NL": "Netherlands", "AN": "Netherlands Antilles", "NC": "New Caledonia", "NZ": "New Zealand", "NI": "Nicaragua", "NE": "Niger", "NG": "Nigeria", "NU": "Niue", "NF": "Norfolk Island", "MP": "Northern Mariana Islands", "NO": "Norway", "OM": "Oman", "PK": "Pakistan", "PW": "<PERSON><PERSON>", "PS": "Palestinian Territory, Occupied", "PA": "Panama", "PG": "Papua New Guinea", "PY": "Paraguay", "PE": "Peru", "PH": "Philippines", "PN": "Pitcairn", "PL": "Poland", "PT": "Portugal", "PR": "Puerto Rico", "QA": "Qatar", "RS": "Republic of Serbia", "RE": "Reunion", "RO": "Romania", "RU": "Russia Federation", "RW": "Rwanda", "BL": "<PERSON>", "SH": "Saint Helena", "KN": "Saint Kitts & Nevis", "LC": "Saint Lucia", "MF": "Saint <PERSON>", "PM": "Saint Pierre and Miquelon", "VC": "Saint Vincent and the Grenadines", "WS": "Samoa", "SM": "San Marino", "ST": "Sao Tome and Principe", "SA": "Saudi Arabia", "SN": "Senegal", "CS": "Serbia and Montenegro", "SC": "Seychelles", "SL": "Sierra Leone", "SG": "Singapore", "SX": "Sint Maarten", "SK": "Slovakia", "SI": "Slovenia", "SB": "Solomon Islands", "SO": "Somalia", "ZA": "South Africa", "GS": "South Georgia & The South Sandwich Islands", "SS": "South Sudan", "ES": "Spain", "LK": "Sri Lanka", "SD": "Sudan", "SR": "Suriname", "SJ": "Svalbard and <PERSON>", "SZ": "Swaziland", "SE": "Sweden", "CH": "Switzerland", "SY": "Syrian Arab Republic", "TW": "Taiwan, Province of China", "TJ": "Tajikistan", "TZ": "Tanzania, United Republic of", "TH": "Thailand", "TL": "Timor-Leste", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinidad and Tobago", "TN": "Tunisia", "TR": "Turkey", "TM": "Turkmenistan", "TC": "Turks and Caicos Islands", "TV": "Tuvalu", "UG": "Uganda", "UA": "Ukraine", "AE": "United Arab Emirates", "GB": "United Kingdom", "US": "United States", "UM": "United States Minor Outlying Islands", "UY": "Uruguay", "UZ": "Uzbekistan", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Vietnam", "VG": "Virgin Islands, British", "VI": "Virgin Islands, U.S.", "WF": "Wallis and Futuna", "EH": "Western Sahara", "YE": "Yemen", "ZM": "Zambia", "ZW": "Zimbabwe"}}, "iso-code-to-iso-country-alpha-3": {"v1": {"AFG": "Afghanistan", "ALA": "Aland Islands", "ALB": "Albania", "DZA": "Algeria", "ASM": "American Samoa", "AND": "Andorra", "AGO": "Angola", "AIA": "<PERSON><PERSON><PERSON>", "ATA": "Antarctica", "ATG": "Antigua and Barbuda", "ARG": "Argentina", "ARM": "Armenia", "ABW": "Aruba", "AUS": "Australia", "AUT": "Austria", "AZE": "Azerbaijan", "BHS": "Bahamas", "BHR": "Bahrain", "BGD": "Bangladesh", "BRB": "Barbados", "BLR": "Belarus", "BEL": "Belgium", "BLZ": "Belize", "BEN": "Benin", "BMU": "Bermuda", "BTN": "Bhutan", "BOL": "Bolivia (Plurinational State of)", "BES": "Bonaire, Sint Eustatius and Saba", "BIH": "Bosnia and Herzegovina", "BWA": "Botswana", "BVT": "Bouvet Island", "BRA": "Brazil", "IOT": "British Indian Ocean Territory", "BRN": "Brunei Darussalam", "BGR": "Bulgaria", "BFA": "Burkina Faso", "BDI": "Burundi", "CPV": "Cabo Verde", "KHM": "Cambodia", "CMR": "Cameroon", "CAN": "Canada", "CYM": "Cayman Islands", "CAF": "Central African Republic", "TCD": "Chad", "CHL": "Chile", "CHN": "China", "CXR": "Christmas Island", "CCK": "Cocos (Keeling) Islands", "COL": "Colombia", "COM": "Comoros", "COG": "Congo", "COD": "Congo, Democratic Republic of the", "COK": "Cook Islands", "CRI": "Costa Rica", "CIV": "Côte d'Ivoire", "HRV": "Croatia", "CUB": "Cuba", "CUW": "Curaçao", "CYP": "Cyprus", "CZE": "Czechia", "DNK": "Denmark", "DJI": "Djibouti", "DMA": "Dominica", "DOM": "Dominican Republic", "ECU": "Ecuador", "EGY": "Egypt", "SLV": "El Salvador", "GNQ": "Equatorial Guinea", "ERI": "Eritrea", "EST": "Estonia", "SWZ": "<PERSON><PERSON><PERSON><PERSON>", "ETH": "Ethiopia", "FLK": "Falkland Islands (Malvinas)", "FRO": "Faroe Islands", "FJI": "Fiji", "FIN": "Finland", "FRA": "France", "GUF": "French Guiana", "PYF": "French Polynesia", "ATF": "French Southern Territories", "GAB": "Gabon", "GMB": "Gambia", "GEO": "Georgia", "DEU": "Germany", "GHA": "Ghana", "GIB": "Gibraltar", "GRC": "Greece", "GRL": "Greenland", "GRD": "Grenada", "GLP": "Guadeloupe", "GUM": "Guam", "GTM": "Guatemala", "GGY": "Guernsey", "GIN": "Guinea", "GNB": "Guinea-Bissau", "GUY": "Guyana", "HTI": "Haiti", "HMD": "Heard Island and McDonald Islands", "VAT": "Holy See", "HND": "Honduras", "HKG": "Hong Kong", "HUN": "Hungary", "ISL": "Iceland", "IND": "India", "IDN": "Indonesia", "IRN": "Iran (Islamic Republic of)", "IRQ": "Iraq", "IRL": "Ireland", "IMN": "Isle of Man", "ISR": "Israel", "ITA": "Italy", "JAM": "Jamaica", "JPN": "Japan", "JEY": "Jersey", "JOR": "Jordan", "KAZ": "Kazakhstan", "KEN": "Kenya", "KIR": "Kiribati", "PRK": "Korea (Democratic People's Republic of)", "KOR": "Korea, Republic of", "KWT": "Kuwait", "KGZ": "Kyrgyzstan", "LAO": "Lao People's Democratic Republic", "LVA": "Latvia", "LBN": "Lebanon", "LSO": "Lesotho", "LBR": "Liberia", "LBY": "Libya", "LIE": "Liechtenstein", "LTU": "Lithuania", "LUX": "Luxembourg", "MAC": "Macao", "MDG": "Madagascar", "MWI": "Malawi", "MYS": "Malaysia", "MDV": "Maldives", "MLI": "Mali", "MLT": "Malta", "MHL": "Marshall Islands", "MTQ": "Martinique", "MRT": "Mauritania", "MUS": "Mauritius", "MYT": "Mayotte", "MEX": "Mexico", "FSM": "Micronesia (Federated States of)", "MDA": "Moldova, Republic of", "MCO": "Monaco", "MNG": "Mongolia", "MNE": "Montenegro", "MSR": "Montserrat", "MAR": "Morocco", "MOZ": "Mozambique", "MMR": "Myanmar", "NAM": "Namibia", "NRU": "Nauru", "NPL": "Nepal", "NLD": "Netherlands", "NCL": "New Caledonia", "NZL": "New Zealand", "NIC": "Nicaragua", "NER": "Niger", "NGA": "Nigeria", "NIU": "Niue", "NFK": "Norfolk Island", "MKD": "North Macedonia", "MNP": "Northern Mariana Islands", "NOR": "Norway", "OMN": "Oman", "PAK": "Pakistan", "PLW": "<PERSON><PERSON>", "PSE": "Palestine, State of", "PAN": "Panama", "PNG": "Papua New Guinea", "PRY": "Paraguay", "PER": "Peru", "PHL": "Philippines", "PCN": "Pitcairn", "POL": "Poland", "PRT": "Portugal", "PRI": "Puerto Rico", "QAT": "Qatar", "REU": "Réunion", "ROU": "Romania", "RUS": "Russian Federation", "RWA": "Rwanda", "BLM": "<PERSON>", "SHN": "Saint Helena, Ascension and Tristan <PERSON>ha", "KNA": "Saint Kitts and Nevis", "LCA": "Saint Lucia", "MAF": "<PERSON> (French part)", "SPM": "Saint Pierre and Miquelon", "VCT": "Saint Vincent and the Grenadines", "WSM": "Samoa", "SMR": "San Marino", "STP": "Sao Tome and Principe", "SAU": "Saudi Arabia", "SEN": "Senegal", "SRB": "Serbia", "SYC": "Seychelles", "SLE": "Sierra Leone", "SGP": "Singapore", "SXM": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "SVK": "Slovakia", "SVN": "Slovenia", "SLB": "Solomon Islands", "SOM": "Somalia", "ZAF": "South Africa", "SGS": "South Georgia and the South Sandwich Islands", "SSD": "South Sudan", "ESP": "Spain", "LKA": "Sri Lanka", "SDN": "Sudan", "SUR": "Suriname", "SJM": "Svalbard and <PERSON>", "SWE": "Sweden", "CHE": "Switzerland", "SYR": "Syrian Arab Republic", "TWN": "Taiwan, Province of China", "TJK": "Tajikistan", "TZA": "Tanzania, United Republic of", "THA": "Thailand", "TLS": "Timor-Leste", "TGO": "Togo", "TKL": "Tokelau", "TON": "Tonga", "TTO": "Trinidad and Tobago", "TUN": "Tunisia", "TUR": "Turkey", "TKM": "Turkmenistan", "TCA": "Turks and Caicos Islands", "TUV": "Tuvalu", "UGA": "Uganda", "UKR": "Ukraine", "ARE": "United Arab Emirates", "GBR": "United Kingdom of Great Britain and Northern Ireland", "USA": "United States of America", "UMI": "United States Minor Outlying Islands", "URY": "Uruguay", "UZB": "Uzbekistan", "VUT": "Vanuatu", "VEN": "Venezuela (Bolivarian Republic of)", "VNM": "Viet Nam", "VGB": "Virgin Islands (British)", "VIR": "Virgin Islands (U.S.)", "WLF": "Wallis and Futuna", "ESH": "Western Sahara", "YEM": "Yemen", "ZMB": "Zambia", "ZWE": "Zimbabwe"}}, "state-city-MY": {"v1": [{"key": "Wp Kuala Lumpur", "value": "WP Kuala Lumpur", "cities": [{"key": "Kuala Lumpur", "value": "Kuala Lumpur", "postcodes": ["50000", "50050", "50088", "50100", "50150", "50200", "50250", "50300", "50350", "50400", "50450", "50460", "50470", "50480", "50490", "50500", "50502", "50504", "50505", "50506", "50507", "50508", "50510", "50512", "50514", "50515", "50519", "50530", "50532", "50534", "50536", "50538", "50540", "50544", "50546", "50548", "50550", "50551", "50552", "50554", "50556", "50560", "50562", "50564", "50566", "50568", "50572", "50578", "50580", "50582", "50586", "50588", "50590", "50592", "50594", "50598", "50600", "50603", "50604", "50608", "50609", "50612", "50614", "50620", "50621", "50622", "50623", "50626", "50632", "50634", "50640", "50644", "50646", "50648", "50650", "50652", "50653", "50658", "50660", "50662", "50664", "50670", "50672", "50673", "50676", "50677", "50678", "50680", "50682", "50684", "50688", "50694", "50695", "50700", "50702", "50704", "50706", "50708", "50710", "50712", "50714", "50716", "50718", "50720", "50722", "50724", "50726", "50728", "50730", "50732", "50734", "50735", "50736", "50738", "50740", "50742", "50744", "50746", "50748", "50750", "50752", "50754", "50756", "50758", "50760", "50762", "50764", "50766", "50768", "50770", "50772", "50774", "50776", "50778", "50780", "50782", "50784", "50786", "50788", "50790", "50792", "50794", "50796", "50798", "50800", "50802", "50804", "50806", "50808", "50810", "50812", "50814", "50816", "50818", "50901", "50902", "50903", "50904", "50905", "50906", "50907", "50908", "50909", "50910", "50911", "50912", "50913", "50914", "50915", "50916", "50917", "50918", "50919", "50920", "50921", "50922", "50923", "50924", "50925", "50926", "50927", "50928", "50929", "50930", "50931", "50932", "50933", "50934", "50935", "50936", "50937", "50938", "50939", "50940", "50941", "50942", "50943", "50944", "50945", "50946", "50947", "50948", "50949", "50950", "50988", "50989", "50990", "51000", "51100", "51200", "51700", "51990", "52000", "52100", "52200", "52700", "53000", "53100", "53200", "53300", "53700", "53800", "53990", "54000", "54100", "54200", "55000", "55100", "55188", "55200", "55300", "55700", "55710", "55720", "55900", "55902", "55904", "55906", "55908", "55910", "55912", "55914", "55916", "55918", "55920", "55922", "55924", "55926", "55928", "55930", "55932", "55934", "55990", "56000", "56100", "57000", "57100", "57700", "57990", "58000", "58100", "58200", "58700", "58990", "59000", "59100", "59200", "59700", "59800", "59990", "60000"]}, {"key": "Setapak", "value": "Setapak", "postcodes": []}]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "cities": [{"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["82100"]}, {"key": "<PERSON>r <PERSON>", "value": "<PERSON>r <PERSON>", "postcodes": ["86100", "86107", "06150"]}, {"key": "Ayer Tawar 2", "value": "Ayer Tawar 2", "postcodes": ["81920", "81927"]}, {"key": "Bandar Penawar", "value": "Bandar Penawar", "postcodes": ["81930", "81937"]}, {"key": "Bandar Tenggara", "value": "Bandar Tenggara", "postcodes": ["81440", "81447"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["85100", "85109"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["83000", "83007", "83009", "83010"]}, {"key": "Bekok", "value": "Bekok", "postcodes": ["86500"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["82200", "82207"]}, {"key": "Bukit Gambir", "value": "Bukit Gambir", "postcodes": ["84800"]}, {"key": "Bukit Pasir", "value": "Bukit Pasir", "postcodes": ["84300"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["85400"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["86900"]}, {"key": "Gelang <PERSON>", "value": "Gelang <PERSON>", "postcodes": ["81550", "81557"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["84700"]}, {"key": "Gugusan Tai<PERSON>", "value": "Gugusan Tai<PERSON>", "postcodes": ["81450"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["85200"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["80000", "80050", "80057", "80100", "80150", "80200", "80250", "80300", "80350", "80357", "80359", "80400", "80500", "80505", "80506", "80508", "80514", "80516", "80519", "80534", "80536", "80538", "80542", "80544", "80546", "80554", "80558", "80560", "80564", "80568", "80578", "80586", "80590", "80592", "80594", "80596", "80598", "80600", "80604", "80608", "80620", "80622", "80628", "80644", "80648", "80662", "80664", "80670", "80672", "80673", "80676", "80690", "80700", "80710", "80720", "80730", "80900", "80902", "80904", "80906", "80908", "80988", "80990", "81100", "81107", "81200", "81300", "81307", "81310", "81551", "81552", "81556"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["86700", "86707"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["86000", "86007", "86009"]}, {"key": "Kota Tinggi", "value": "Kota Tinggi", "postcodes": ["81900", "81907"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["82300"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["81000", "81007", "81009"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["85300", "85307", "85309"]}, {"key": "Layang-Layang", "value": "Layang-Layang", "postcodes": ["81850"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["81750", "81757", "81759"]}, {"key": "Mersing", "value": "Mersing", "postcodes": ["86800", "86807", "86810", "86888"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["84000", "84007", "84200"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": []}, {"key": "Pagoh", "value": "Pagoh", "postcodes": ["84600"]}, {"key": "Paloh", "value": "Paloh", "postcodes": ["86600"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["84500"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["84150", "84157"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["86400", "86407"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["83500"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["81700", "81707"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["81500", "81507"]}, {"key": "Pengerang", "value": "Pengerang", "postcodes": ["81600"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["82000", "82007", "82009"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["86300"]}, {"key": "Rengit", "value": "Rengit", "postcodes": ["83100"]}, {"key": "Segamat", "value": "Segamat", "postcodes": ["85000", "85007"]}, {"key": "Semerah", "value": "Semerah", "postcodes": ["83600"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["81400", "81407", "81409"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["83200"]}, {"key": "Seri Gading", "value": "Seri Gading", "postcodes": ["83300", "83307"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["83400", "83407"]}, {"key": "Simpang Rengam", "value": "Simpang Rengam", "postcodes": ["86200", "86207"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["84400", "84407"]}, {"key": "Tangkak", "value": "Tangkak", "postcodes": ["84900", "84907", "84909"]}, {"key": "Tioman", "value": "Tioman", "postcodes": []}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": []}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["81800", "81807", "81809"]}, {"key": "<PERSON>", "value": "<PERSON>", "postcodes": ["83700", "83707"]}]}, {"key": "Kedah", "value": "Kedah", "cities": [{"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["05000", "05050", "05100", "05150", "05200", "05250", "05300", "05350", "05400", "05460", "05500", "05502", "05503", "05504", "05505", "05506", "05508", "05512", "05514", "05516", "05517", "05518", "05520", "05532", "05534", "05536", "05538", "05550", "05551", "05552", "05556", "05558", "05560", "05564", "05576", "05578", "05580", "05582", "05586", "05590", "05592", "05594", "05600", "05604", "05610", "05612", "05614", "05620", "05621", "05622", "05626", "05628", "05630", "05632", "05644", "05646", "05660", "05661", "05664", "05670", "05672", "05673", "05674", "05675", "05676", "05680", "05690", "05696", "05700", "05710", "05720", "05990", "06250", "06509", "06550", "06570"]}, {"key": "<PERSON>r <PERSON>", "value": "<PERSON>r <PERSON>", "postcodes": ["86100", "86107", "06150"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["09100", "09109", "09120", "09130"]}, {"key": "Bandar Baharu", "value": "Bandar Baharu", "postcodes": ["14390", "34950"]}, {"key": "Bandar Bahru", "value": "Bandar Bahru", "postcodes": []}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["08100", "08107", "08109", "08110"]}, {"key": "Bukit Kayu <PERSON>", "value": "Bukit Kayu <PERSON>", "postcodes": ["06050"]}, {"key": "Changloon", "value": "Changloon", "postcodes": ["06010"]}, {"key": "Gurun", "value": "Gurun", "postcodes": ["08300", "08307", "08800"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["08320", "08700"]}, {"key": "Jitra", "value": "Jitra", "postcodes": ["06000", "06007", "06009"]}, {"key": "Karangan", "value": "Karangan", "postcodes": ["09500", "09700", "09709"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["13200", "13210", "13220", "06200", "06207", "06209"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["06100"]}, {"key": "Kota Kuala Muda", "value": "Kota Kuala Muda", "postcodes": ["08500", "08507", "08509"]}, {"key": "Kota Sarang Semut", "value": "Kota Sarang Semut", "postcodes": ["06800"]}, {"key": "Kuala Kedah", "value": "Kuala Kedah", "postcodes": ["06600"]}, {"key": "Kuala Ketil", "value": "Kuala Ketil", "postcodes": ["09300", "09309", "09310"]}, {"key": "Kuala Nerang", "value": "Kuala Nerang", "postcodes": ["06300", "06309"]}, {"key": "Kuala Pegang", "value": "Kuala Pegang", "postcodes": ["09110"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["09000", "09007", "09009", "09010", "09020", "09090"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["09200"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["06500", "06507"]}, {"key": "Lang<PERSON><PERSON>", "value": "Lang<PERSON><PERSON>", "postcodes": ["07000", "07007", "07009"]}, {"key": "Lunas", "value": "Lunas", "postcodes": ["09600"]}, {"key": "Merbok", "value": "Merbok", "postcodes": ["08400", "08407", "08409"]}, {"key": "Padang Serai", "value": "Padang Serai", "postcodes": ["09400", "09409", "09410"]}, {"key": "Pendang", "value": "Pendang", "postcodes": ["06700", "06707", "06709", "06710", "06720", "06750"]}, {"key": "Pokok Sena", "value": "Pokok Sena", "postcodes": ["06350", "06400", "06760"]}, {"key": "Serdang", "value": "Serdang", "postcodes": ["43400", "09800", "09809", "09810"]}, {"key": "Sik", "value": "Sik", "postcodes": ["08200", "08210"]}, {"key": "Simpang Empat", "value": "Simpang Empat", "postcodes": ["06650", "06660"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["08000", "08007", "08009", "08010", "08600"]}, {"key": "Yan", "value": "Yan", "postcodes": ["06900", "06910"]}]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "cities": [{"key": "Ayer <PERSON>", "value": "Ayer <PERSON>", "postcodes": ["17700"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["16020", "16050", "16070", "16090", "16300", "16309", "16310"]}, {"key": "Cherang Ruku", "value": "Cherang Ruku", "postcodes": ["16700"]}, {"key": "Dabong", "value": "Dabong", "postcodes": ["18200"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["18300", "18307"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["17600", "17609"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["16500"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["16450"]}, {"key": "Kota Bharu", "value": "Kota Bharu", "postcodes": ["15000", "15050", "15100", "15150", "15159", "15200", "15300", "15350", "15400", "15500", "15502", "15503", "15504", "15505", "15506", "15508", "15512", "15514", "15516", "15517", "15518", "15519", "15520", "15524", "15529", "15532", "15534", "15536", "15538", "15540", "15546", "15548", "15550", "15551", "15552", "15556", "15558", "15560", "15564", "15570", "15572", "15576", "15578", "15582", "15586", "15590", "15592", "15594", "15596", "15600", "15604", "15608", "15609", "15612", "15614", "15616", "15622", "15623", "15626", "15628", "15630", "15632", "15634", "15644", "15646", "15648", "15658", "15660", "15661", "15664", "15670", "15672", "15673", "15674", "15675", "15676", "15680", "15690", "15710", "15720", "15730", "15740", "15988", "15990", "16010", "16100", "16109", "16150"]}, {"key": "Kuala Balah", "value": "Kuala Balah", "postcodes": ["17610"]}, {"key": "Kuala Krai", "value": "Kuala Krai", "postcodes": ["18000", "18007", "18009", "18050"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["18500", "18509"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["16400"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["17000", "17007", "17009", "17010", "17020", "17030", "17040", "17050", "17060", "17070"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["16800"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["16600"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["17200", "17207", "34140"]}, {"key": "Selising", "value": "Selising", "postcodes": ["16810"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["17500", "17507", "17509", "17510"]}, {"key": "Temangan", "value": "Temangan", "postcodes": ["18400"]}, {"key": "Tumpat", "value": "Tumpat", "postcodes": ["16080", "16200", "16210"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["16040", "16250"]}]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "cities": [{"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["78000", "78007", "78009"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["77100", "77109"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["75450", "75459", "75460"]}, {"key": "Bemban", "value": "Bemban", "postcodes": ["77200"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["76100", "76109"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["77000", "77007", "77008", "77009"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["76200"]}, {"key": "Kuala Sungai Baru", "value": "Kuala Sungai Baru", "postcodes": ["78200"]}, {"key": "Lubok China", "value": "Lubok China", "postcodes": ["78100"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["78300", "78307", "78309"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["75000", "75050", "75100", "75150", "75200", "75250", "75260", "75300", "75350", "75400", "75460", "75500", "75502", "75503", "75504", "75505", "75506", "75508", "75510", "75512", "75514", "75516", "75517", "75518", "75519", "75532", "75536", "75538", "75540", "75542", "75546", "75550", "75551", "75552", "75560", "75564", "75566", "75570", "75572", "75576", "75578", "75582", "75584", "75586", "75590", "75592", "75594", "75596", "75600", "75604", "75606", "75608", "75609", "75612", "75620", "75622", "75623", "75626", "75628", "75630", "75632", "75646", "75648", "75664", "75670", "75672", "75673", "75674", "75676", "75690", "75700", "75710", "75720", "75730", "75740", "75750", "75760", "75900", "75902", "75904", "75906", "75908", "75910", "75912", "75914", "75916", "75918", "75990", "76100", "76400", "76450"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["77300", "77307", "77309"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["77500", "77507", "77509"]}, {"key": "Sungai Ram<PERSON>i", "value": "Sungai Ram<PERSON>i", "postcodes": ["77400", "77409"]}, {"key": "Sungai Udang", "value": "Sungai Udang", "postcodes": ["76300"]}, {"key": "Tanjong Kling", "value": "Tanjong Kling", "postcodes": ["76400", "76409"]}]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "cities": [{"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["72100", "72107", "72109", "72120"]}, {"key": "Bandar Baru Enstek", "value": "Bandar Baru Enstek", "postcodes": []}, {"key": "Bandar Bar<PERSON>", "value": "Bandar Bar<PERSON>", "postcodes": []}, {"key": "Bandar Seri Jempol", "value": "Bandar Seri Jempol", "postcodes": ["72120", "72127", "72129"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["72200", "72207", "72209"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["73400", "73409", "73420", "73480"]}, {"key": "Gemenche<PERSON>", "value": "Gemenche<PERSON>", "postcodes": ["73200", "73207", "73209", "73300", "73309"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["73100", "73109"]}, {"key": "Kota", "value": "Kota", "postcodes": ["71350", "71359"]}, {"key": "Kuala Klawang", "value": "Kuala Klawang", "postcodes": ["71600", "71650", "71659"]}, {"key": "Kuala Pilah", "value": "Kuala Pilah", "postcodes": ["72000", "72007", "72009", "72500", "72507", "72509"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["71800", "71900", "71907", "71909"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["71150", "71159"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["71700", "71707", "71709", "71750", "71759"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["71800", "71807", "71809"]}, {"key": "Port Dickson", "value": "Port Dickson", "postcodes": ["71000", "71007", "71009", "71010", "71960"]}, {"key": "Pusat  Bandar <PERSON>long", "value": "Pusat  Bandar <PERSON>long", "postcodes": ["73430", "73440", "73450", "73460", "73470"]}, {"key": "Rantau", "value": "Rantau", "postcodes": ["71100", "71200", "71209"]}, {"key": "Re<PERSON><PERSON>", "value": "Re<PERSON><PERSON>", "postcodes": ["71300", "71309", "71400", "71409"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["73500", "73507", "73509"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["70000", "70100", "70200", "70300", "70400", "70450", "70500", "70502", "70503", "70504", "70505", "70506", "70508", "70512", "70516", "70517", "70518", "70532", "70534", "70536", "70538", "70540", "70546", "70548", "70550", "70551", "70558", "70560", "70564", "70570", "70572", "70576", "70578", "70582", "70586", "70590", "70592", "70594", "70596", "70600", "70604", "70606", "70608", "70609", "70610", "70612", "70620", "70626", "70628", "70632", "70634", "70644", "70646", "70648", "70658", "70664", "70670", "70672", "70673", "70674", "70676", "70690", "70700", "70710", "70720", "70730", "70740", "70750", "70990", "71450", "71459", "71770", "71950"]}, {"key": "Simpang Durian", "value": "Simpang Durian", "postcodes": ["72400", "72409"]}, {"key": "Simpang Pertang", "value": "Simpang Pertang", "postcodes": ["72300", "72307", "72309"]}, {"key": "<PERSON>", "value": "<PERSON>", "postcodes": ["71050", "71059", "71250", "71259"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["73000", "73007", "73009", "73010"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["71500", "71509", "71550", "71559"]}]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "cities": [{"key": "Balok", "value": "Balok", "postcodes": ["26150", "26190", "26197"]}, {"key": "Bandar Pusat  Jengka", "value": "Bandar Pusat  Jengka", "postcodes": ["26400", "26407", "26409", "26410", "26430", "26450", "26460"]}, {"key": "Bandar <PERSON>", "value": "Bandar <PERSON>", "postcodes": ["26700", "26900", "26907"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["27300", "27307", "27310"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["28700", "28707", "28709", "28730", "28740", "28750"]}, {"key": "Brinchang", "value": "Brinchang", "postcodes": ["39100", "39107"]}, {"key": "Bukit <PERSON>", "value": "Bukit <PERSON>", "postcodes": ["49000"]}, {"key": "Bukit Goh", "value": "Bukit Goh", "postcodes": ["26050", "26057", "26090"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["28100"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["26690"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["27030", "27037"]}, {"key": "<PERSON>", "value": "<PERSON>", "postcodes": ["27400"]}, {"key": "Gambang", "value": "Gambang", "postcodes": ["26300", "26309", "26320", "26330", "26340", "26350", "26360", "26370"]}, {"key": "Genting Highlands", "value": "Genting Highlands", "postcodes": ["69000", "69009"]}, {"key": "Jaya Gading", "value": "Jaya Gading", "postcodes": []}, {"key": "Jerantut", "value": "Jerantut", "postcodes": ["27000", "27007", "27009", "27010", "27020", "27040", "27050", "27060", "27070", "27090", "27150"]}, {"key": "Karak", "value": "Karak", "postcodes": ["28600", "28609", "28610", "28620"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["28340", "28380", "28387"]}, {"key": "Kuala Krau", "value": "Kuala Krau", "postcodes": ["28050", "28057", "28059"]}, {"key": "Kuala Lipis", "value": "Kuala Lipis", "postcodes": ["27200", "27207", "27209", "27210"]}, {"key": "Kuala Rompin", "value": "Kuala Rompin", "postcodes": ["26800", "26807", "26809", "26810", "26820"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["25000", "25050", "25100", "25150", "25200", "25250", "25300", "25350", "25500", "25502", "25503", "25504", "25505", "25506", "25508", "25509", "25512", "25514", "25516", "25517", "25518", "25520", "25524", "25529", "25532", "25534", "25536", "25538", "25540", "25546", "25548", "25550", "25551", "25552", "25556", "25558", "25560", "25564", "25570", "25576", "25578", "25582", "25584", "25586", "25590", "25592", "25594", "25596", "25598", "25600", "25604", "25606", "25608", "25609", "25612", "25614", "25620", "25622", "25626", "25628", "25630", "25632", "25644", "25646", "25648", "25656", "25660", "25661", "25662", "25670", "25672", "25673", "25674", "25676", "25690", "25700", "25709", "25710", "25720", "25730", "25740", "25750", "25990", "26010", "26040", "26060", "26070", "26077", "26080", "26100", "26140"]}, {"key": "La<PERSON><PERSON>", "value": "La<PERSON><PERSON>", "postcodes": ["28500", "28509"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["28800"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["26500", "26507", "26509"]}, {"key": "Mentakab", "value": "Mentakab", "postcodes": ["28400", "28407", "28409"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["26700", "26707", "26709"]}, {"key": "Padang Tengku", "value": "Padang Tengku", "postcodes": ["27100", "27109"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["26600", "26607", "26609", "26610", "26620", "26630", "26640", "26650", "26660", "26680"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["27500", "27600", "27607", "27609", "27610", "27620", "27630"]}, {"key": "<PERSON>let", "value": "<PERSON>let", "postcodes": ["39200"]}, {"key": "Sega", "value": "Sega", "postcodes": ["27660"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["27650"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["26200", "26250"]}, {"key": "Sungai Ruan", "value": "Sungai Ruan", "postcodes": []}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["39000", "39007", "39009", "39010"]}, {"key": "Temerloh", "value": "Temerloh", "postcodes": ["28000", "28007", "28009", "28010", "28020", "28030", "28040"]}, {"key": "Triang", "value": "Triang", "postcodes": ["28300", "28309", "28310", "28320", "28330"]}]}, {"key": "Penang", "value": "<PERSON><PERSON><PERSON>", "cities": [{"key": "Ayer <PERSON>", "value": "Ayer <PERSON>", "postcodes": ["11500"]}, {"key": "Balik Pulau", "value": "Balik Pulau", "postcodes": ["11000", "11010", "11020"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["11100"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["11960"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["11900", "11910", "11920", "11950"]}, {"key": "Bukit Mertajam", "value": "Bukit Mertajam", "postcodes": ["14000", "14007", "14009", "14020"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["12000", "12100", "12200", "12300", "12700", "12710", "12720", "12990", "13000", "13009", "13020", "13050", "13400", "13409", "13800"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["11700"]}, {"key": "George Town", "value": "George Town", "postcodes": []}, {"key": "Jelutong", "value": "Jelutong", "postcodes": ["11600", "11609"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["13200", "13210", "13220", "06200", "06207", "06209"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["14400"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["14300", "14310", "14320"]}, {"key": "Penaga", "value": "Penaga", "postcodes": ["13100", "13110"]}, {"key": "Penang Hill", "value": "Penang Hill", "postcodes": ["11300"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["13600", "13700"]}, {"key": "Permatang Pauh", "value": "Permatang Pauh", "postcodes": ["13500"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["10000", "10050", "10100", "10150", "10200", "10250", "10300", "10350", "10400", "10450", "10460", "10470", "10500", "10502", "10503", "10504", "10505", "10506", "10508", "10512", "10514", "10516", "10518", "10524", "10534", "10538", "10540", "10542", "10546", "10550", "10551", "10552", "10558", "10560", "10564", "10566", "10570", "10576", "10578", "10582", "10590", "10592", "10593", "10594", "10596", "10600", "10604", "10609", "10610", "10612", "10620", "10622", "10626", "10628", "10634", "10646", "10648", "10660", "10661", "10662", "10670", "10672", "10673", "10674", "10676", "10690", "10700", "10710", "10720", "10730", "10740", "10750", "10760", "10770", "10780", "10790", "10800", "10810", "10820", "10830", "10840", "10850", "10910", "10920", "10990", "11050", "11060", "11400", "11409"]}, {"key": "Simpang Ampat", "value": "Simpang Ampat", "postcodes": ["14100", "14110", "14120", "02700", "02707", "02709", "02800"]}, {"key": "Sungai <PERSON>aw<PERSON>", "value": "Sungai <PERSON>aw<PERSON>", "postcodes": ["14200"]}, {"key": "Tanjong Bungah", "value": "Tanjong Bungah", "postcodes": ["11200"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["13300", "13310"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": []}]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "cities": [{"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["32400", "32407"]}, {"key": "Bagan <PERSON>", "value": "Bagan <PERSON>", "postcodes": ["36100"]}, {"key": "Bagan Serai", "value": "Bagan Serai", "postcodes": ["34300", "34307", "34310"]}, {"key": "Bandar Seri Iskandar", "value": "Bandar Seri Iskandar", "postcodes": ["32610"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["31000", "31007", "31009"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["34500", "34510", "34520"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["35950"]}, {"key": "Bidor", "value": "Bidor", "postcodes": ["35500", "35507"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["32600"]}, {"key": "B<PERSON><PERSON>", "value": "B<PERSON><PERSON>", "postcodes": ["32700"]}, {"key": "Chang<PERSON> Jering", "value": "Chang<PERSON> Jering", "postcodes": ["34850"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["32500"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["31200"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["35300"]}, {"key": "Chender<PERSON>lai", "value": "Chender<PERSON>lai", "postcodes": ["36600"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["36750"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["33600"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["33300", "33307", "33310", "33320"]}, {"key": "<PERSON><PERSON>g", "value": "<PERSON><PERSON>g", "postcodes": ["31600", "31610"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["36400"]}, {"key": "Intan", "value": "Intan", "postcodes": []}, {"key": "Ipoh", "value": "Ipoh", "postcodes": ["30000", "30010", "30020", "30100", "30200", "30250", "30300", "30350", "30450", "30502", "30503", "30504", "30505", "30506", "30508", "30510", "30512", "30516", "30517", "30518", "30519", "30520", "30524", "30532", "30534", "30536", "30540", "30542", "30546", "30548", "30550", "30551", "30552", "30554", "30556", "30560", "30564", "30570", "30576", "30580", "30582", "30586", "30590", "30592", "30594", "30596", "30600", "30604", "30606", "30609", "30610", "30612", "30614", "30620", "30621", "30622", "30626", "30628", "30630", "30632", "30634", "30644", "30646", "30648", "30656", "30658", "30660", "30662", "30664", "30668", "30670", "30673", "30674", "30676", "30690", "30700", "30710", "30720", "30730", "30740", "30750", "30760", "30770", "30780", "30790", "30800", "30810", "30820", "30830", "30840", "30900", "30902", "30904", "30906", "30908", "30910", "30912", "30988", "30990", "31350", "31400", "31407", "31409", "31450", "31500", "31650"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["31850", "45800"]}, {"key": "Kampar", "value": "Kampar", "postcodes": ["31900", "31907", "31909", "31910", "31950"]}, {"key": "Kampung Gajah", "value": "Kampung Gajah", "postcodes": ["36800"]}, {"key": "Kampung Kepayang", "value": "Kampung Kepayang", "postcodes": ["31300"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["34600", "34607"]}, {"key": "Kuala Kangsar", "value": "Kuala Kangsar", "postcodes": ["33000", "33007", "33009", "33010", "33020", "33030", "33040"]}, {"key": "Kuala Kurau", "value": "Kuala Kurau", "postcodes": ["34350"]}, {"key": "Kuala Sepetang", "value": "Kuala Sepetang", "postcodes": ["34650"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["32900"]}, {"key": "Langkap", "value": "Langkap", "postcodes": ["36700"]}, {"key": "Lenggong", "value": "Lenggong", "postcodes": ["33400", "33410", "33420"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["32200"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["31700"]}, {"key": "Mambang <PERSON>", "value": "Mambang <PERSON>", "postcodes": []}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["33800"]}, {"key": "Matang", "value": "Matang", "postcodes": ["34750"]}, {"key": "Padang Rengas", "value": "Padang Rengas", "postcodes": ["33700"]}, {"key": "<PERSON>g<PERSON>", "value": "<PERSON>g<PERSON>", "postcodes": ["32300"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["34900"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["32800"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["34200"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["33100"]}, {"key": "Pusing", "value": "Pusing", "postcodes": ["31550", "31560"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["17200", "17207", "34140"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["33500"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["34100", "34120", "34130"]}, {"key": "Se<PERSON>ko<PERSON>", "value": "Se<PERSON>ko<PERSON>", "postcodes": ["32600", "36200", "36207", "36209"]}, {"key": "Seri <PERSON>", "value": "Seri <PERSON>", "postcodes": ["32040"]}, {"key": "Simpang", "value": "Simpang", "postcodes": ["34700"]}, {"key": "Simpang Ampat Semanggol", "value": "Simpang Ampat Semanggol", "postcodes": ["34400"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["32000", "32020"]}, {"key": "Slim River", "value": "Slim River", "postcodes": ["35800", "35820"]}, {"key": "Sungai Siput", "value": "Sungai Siput", "postcodes": ["31050", "31100"]}, {"key": "<PERSON><PERSON>mu<PERSON>", "value": "<PERSON><PERSON>mu<PERSON>", "postcodes": ["36300", "36307", "36309"]}, {"key": "Sungkai", "value": "Sungkai", "postcodes": ["35600"]}, {"key": "Taiping", "value": "Taiping", "postcodes": ["34000", "34007", "34008", "34009"]}, {"key": "Tanjong Malim", "value": "Tanjong Malim", "postcodes": ["35900", "35907", "35909"]}, {"key": "Tanjong Piandang", "value": "Tanjong Piandang", "postcodes": ["34250"]}, {"key": "Tanjong Rambutan", "value": "Tanjong Rambutan", "postcodes": ["31250"]}, {"key": "Tanjong Tualang", "value": "Tanjong Tualang", "postcodes": ["31800"]}, {"key": "Tapah", "value": "Tapah", "postcodes": ["35000", "35007", "35009"]}, {"key": "Tapah Road", "value": "Tapah Road", "postcodes": ["35400"]}, {"key": "Teluk Intan", "value": "Teluk Intan", "postcodes": ["36000", "36007", "36008", "36009", "36020", "36110"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["35350"]}, {"key": "Tldm Lumut", "value": "Tldm Lumut", "postcodes": []}, {"key": "Trolak", "value": "Trolak", "postcodes": ["35700"]}, {"key": "Trong", "value": "Trong", "postcodes": ["34800"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["31750"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["36500"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["31150"]}]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "cities": [{"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["02600", "02607", "02609"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["02200"]}, {"key": "Kangar", "value": "Kangar", "postcodes": ["01000", "01007", "01009", "01500", "01502", "01503", "01504", "01505", "01506", "01508", "01512", "01514", "01516", "01517", "01518", "01524", "01529", "01532", "01538", "01540", "01542", "01546", "01550", "01551", "01556", "01560", "01564", "01570", "01572", "01576", "01578", "01582", "01586", "01590", "01592", "01594", "01596", "01598", "01600", "01604", "01606", "01608", "01609", "01610", "01612", "01614", "01620", "01622", "01626", "01628", "01630", "01632", "01634", "01644", "01646", "01648", "01660", "01664", "01670", "01672", "01673", "01674", "01676", "01680", "01694", "01990", "02400", "02450", "02500"]}, {"key": "Kuala Perlis", "value": "Kuala Perlis", "postcodes": ["02000"]}, {"key": "Padang Besar", "value": "Padang Besar", "postcodes": ["02100"]}, {"key": "Simpang Ampat", "value": "Simpang Ampat", "postcodes": ["14100", "14110", "14120", "02700", "02707", "02709", "02800"]}]}, {"key": "Sabah", "value": "Sabah", "cities": [{"key": "Beaufort", "value": "Beaufort", "postcodes": ["89800", "89807", "89808", "89809"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["90100", "90107", "90108", "90109"]}, {"key": "Beverly", "value": "Beverly", "postcodes": ["88700"]}, {"key": "Bongawan", "value": "Bongawan", "postcodes": ["89700", "89707", "89708", "89709"]}, {"key": "Inanam", "value": "Inanam", "postcodes": ["88857"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["89000", "89007", "89008", "89009"]}, {"key": "Kota Belud", "value": "Kota Belud", "postcodes": ["89150", "89157", "89158", "89159"]}, {"key": "Kota Kinabalu", "value": "Kota Kinabalu", "postcodes": ["88000", "88100", "88200", "88300", "88400", "88450", "88460", "88500", "88502", "88504", "88505", "88506", "88508", "88510", "88512", "88514", "88516", "88518", "88520", "88526", "88527", "88532", "88534", "88538", "88540", "88546", "88550", "88551", "88552", "88554", "88556", "88558", "88560", "88564", "88566", "88568", "88570", "88572", "88576", "88580", "88582", "88590", "88592", "88594", "88596", "88598", "88600", "88604", "88606", "88608", "88609", "88612", "88614", "88617", "88618", "88620", "88621", "88622", "88624", "88626", "88630", "88632", "88634", "88644", "88646", "88648", "88656", "88662", "88670", "88672", "88673", "88675", "88676", "88680", "88690", "88757", "88758", "88759", "88760", "88761", "88762", "88763", "88764", "88765", "88766", "88767", "88768", "88769", "88770", "88771", "88772", "88773", "88774", "88775", "88776", "88777", "88778", "88779", "88780", "88781", "88782", "88783", "88784", "88785", "88786", "88787", "88788", "88789", "88790", "88800", "88801", "88802", "88803", "88804", "88805", "88806", "88807", "88808", "88809", "88810", "88811", "88812", "88813", "88814", "88815", "88816", "88817", "88818", "88819", "88820", "88821", "88822", "88823", "88824", "88825", "88826", "88827", "88828", "88829", "88830", "88831", "88832", "88833", "88834", "88835", "88836", "88837", "88838", "88839", "88840", "88841", "88842", "88843", "88844", "88845", "88846", "88847", "88848", "88849", "88850", "88851", "88852", "88853", "88854", "88855", "88860", "88861", "88862", "88863", "88864", "88865", "88866", "88867", "88868", "88869", "88870", "88871", "88872", "88873", "88874", "88875", "88900", "88901", "88902", "88903", "88904", "88905", "88906", "88988", "88990", "88991", "88992", "88993", "88994", "88995", "88996", "88997", "88998", "88999"]}, {"key": "Kota Kinabatangan", "value": "Kota Kinabatangan", "postcodes": ["90200", "90208"]}, {"key": "Kota Marudu", "value": "Kota Marudu", "postcodes": ["89100", "89107", "89108", "89109", "89130", "89137", "89138", "89139"]}, {"key": "Kuala Penyu", "value": "Kuala Penyu", "postcodes": ["89740", "89747", "89748", "89749"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["89050", "89057", "89058", "89059"]}, {"key": "Kunak", "value": "Kunak", "postcodes": ["91200", "91207", "91208", "91209"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["91100", "91107", "91109", "91110", "91111", "91112", "91113", "91114", "91115", "91116", "91117", "91118", "91119", "91120", "91121", "91122", "91123", "91124", "91125", "91126", "91127", "91128", "91150"]}, {"key": "Likas", "value": "Likas", "postcodes": ["88856"]}, {"key": "Membakut", "value": "Membakut", "postcodes": ["89720", "89727", "89728", "89729"]}, {"key": "Menumbok", "value": "Menumbok", "postcodes": ["89760", "89767", "89768", "89769"]}, {"key": "Nabawan", "value": "Nabawan", "postcodes": ["89950", "89957", "89958", "89959"]}, {"key": "Pamol", "value": "Pamol", "postcodes": ["90400"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["89600", "89607", "89608", "89609"]}, {"key": "Penampang", "value": "Penampang", "postcodes": ["89500", "89507", "89508", "89509"]}, {"key": "Putatan", "value": "Putatan", "postcodes": ["88721", "88722", "88723", "88724", "88725"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["89300", "89307", "89308", "89309", "89328", "89329", "89330", "89337", "89338", "89339"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["90000", "90008", "90009", "90300", "90307", "90308", "90508", "90509", "90700", "90701", "90702", "90703", "90704", "90705", "90706", "90707", "90708", "90709", "90710", "90711", "90712", "90713", "90714", "90715", "90716", "90717", "90718", "90719", "90720", "90721", "90722", "90723", "90724", "90725", "90726", "90727", "90728", "90729", "90730", "90731", "90732", "90733", "90734", "90735", "90736", "90737", "90738", "90739", "90740", "90741"]}, {"key": "Semporna", "value": "Semporna", "postcodes": ["91300", "91307", "91308", "91309"]}, {"key": "Sipitang", "value": "Sipitang", "postcodes": ["89850", "89857", "89858", "89859"]}, {"key": "Tambunan", "value": "Tambunan", "postcodes": ["89650", "89657", "89658", "89659"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["89250", "89257", "89258", "89259"]}, {"key": "Tanjung A<PERSON>", "value": "Tanjung A<PERSON>", "postcodes": ["88858", "88859"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["91000", "91007", "91008", "91009", "91010", "91011", "91012", "91013", "91014", "91015", "91016", "91017", "91018", "91019", "91020", "91021", "91022", "91023", "91024", "91025", "91026", "91027", "91028", "91029", "91030", "91031", "91032", "91033", "91034", "91035", "91036", "91037", "91038", "91039", "91040", "91041", "91042", "91043", "91044", "91045", "91046", "91047", "91048", "91049", "91050", "91051", "91052", "91053", "91054", "91055", "91056"]}, {"key": "Tenom", "value": "Tenom", "postcodes": ["89900", "89907", "89908", "89909"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["89200", "89207", "89208", "89209"]}]}, {"key": "Sarawak", "value": "Sarawak", "cities": [{"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["94600", "94607"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["96350"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["98050", "98057", "98058", "98059", "98060"]}, {"key": "Bau", "value": "Bau", "postcodes": ["94000", "94007", "94009"]}, {"key": "Bekenu", "value": "Bekenu", "postcodes": ["98150", "98157", "98159"]}, {"key": "Belaga", "value": "Belaga", "postcodes": ["96900", "96950"]}, {"key": "Belawai", "value": "Belawai", "postcodes": ["96150"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["95700", "95707", "95709"]}, {"key": "Bintangor", "value": "Bintangor", "postcodes": ["96500", "96507", "96508", "96509"]}, {"key": "Bintulu", "value": "Bintulu", "postcodes": ["97000", "97007", "97008", "97009", "97010", "97011", "97012", "97013", "97014", "97015", "97300", "97307"]}, {"key": "Dalat", "value": "Dalat", "postcodes": ["96300", "96307", "96309"]}, {"key": "Dar<PERSON>", "value": "Dar<PERSON>", "postcodes": ["96200"]}, {"key": "Debak", "value": "Debak", "postcodes": ["95500"]}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postcodes": ["95800", "95807"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["96600", "96609"]}, {"key": "Kabong", "value": "Kabong", "postcodes": ["94650"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["96700", "96707", "96709"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["96800", "96807", "96809"]}, {"key": "Kota Samarahan", "value": "Kota Samarahan", "postcodes": ["94300"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["93000", "93010", "93050", "93100", "93107", "93150", "93200", "93250", "93257", "93300", "93307", "93350", "93400", "93407", "93450", "93457", "93500", "93501", "93502", "93503", "93504", "93505", "93506", "93507", "93508", "93514", "93516", "93517", "93518", "93519", "93520", "93527", "93529", "93532", "93540", "93550", "93551", "93552", "93554", "93556", "93558", "93560", "93564", "93566", "93570", "93572", "93576", "93578", "93582", "93586", "93590", "93592", "93594", "93596", "93600", "93601", "93604", "93606", "93608", "93609", "93610", "93612", "93614", "93618", "93619", "93620", "93626", "93628", "93632", "93634", "93648", "93658", "93660", "93661", "93662", "93668", "93670", "93672", "93675", "93677", "93690", "93694", "93700", "93702", "93704", "93706", "93708", "93710", "93712", "93714", "93716", "93718", "93720", "93722", "93724", "93726", "93728", "93730", "93732", "93734", "93736", "93738", "93740", "93742", "93744", "93746", "93748", "93750", "93752", "93754", "93756", "93758", "93760", "93762", "93764", "93900", "93902", "93904", "93906", "93908", "93910", "93912", "93914", "93916", "93990"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["98850", "98857", "98859"]}, {"key": "Limbang", "value": "Limbang", "postcodes": ["98700", "98707", "98708", "98709"]}, {"key": "Lingga", "value": "Lingga", "postcodes": ["94900"]}, {"key": "<PERSON>", "value": "<PERSON>", "postcodes": ["98300"]}, {"key": "Lubok Antu", "value": "Lubok Antu", "postcodes": ["95900"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["94111", "94500", "94507", "94509"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["98100", "98107", "98109"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["96250"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["98000", "98007", "98008", "98009"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["96400", "96407", "96410"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["98750"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["98200", "98207"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["94950"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["95300"]}, {"key": "Saratok", "value": "Saratok", "postcodes": ["95400", "95407", "95409"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["96100", "96107", "96108", "96109", "96510"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["97100"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["94850"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["94700", "94707", "94709", "94750", "94760"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["96000", "96007", "96008", "96009", "96010"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["94200"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["94800", "94807", "94809"]}, {"key": "Song", "value": "Song", "postcodes": ["96850", "96857"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["95600"]}, {"key": "<PERSON>", "value": "<PERSON>", "postcodes": ["95000", "95007", "95008", "95009"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["98800"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["97200"]}]}, {"key": "Selangor", "value": "Selangor", "cities": [{"key": "Ampang", "value": "Ampang", "postcodes": ["68000"]}, {"key": "Bandar <PERSON>", "value": "Bandar <PERSON>", "postcodes": ["43600", "43650", "43657", "43659"]}, {"key": "Bandar Puncak Alam", "value": "Bandar Puncak Alam", "postcodes": ["42300"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": []}, {"key": "Banting", "value": "Banting", "postcodes": ["42700"]}, {"key": "Batang Berjuntai", "value": "Batang Berjuntai", "postcodes": []}, {"key": "Batang Kali", "value": "Batang Kali", "postcodes": ["44300"]}, {"key": "Batu Caves", "value": "Batu Caves", "postcodes": ["68100"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["43700"]}, {"key": "Bukit Rotan", "value": "Bukit Rotan", "postcodes": ["45700"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["43200", "43207"]}, {"key": "Cyberjaya", "value": "Cyberjaya", "postcodes": ["63000", "63100"]}, {"key": "Dengkil", "value": "Dengkil", "postcodes": ["43800", "43807"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["43100"]}, {"key": "Jen<PERSON><PERSON>", "value": "Jen<PERSON><PERSON>", "postcodes": ["42600", "42610"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["31850", "45800"]}, {"key": "Kajang", "value": "Kajang", "postcodes": ["43000", "43007", "43009", "43558"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["42200"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["44100"]}, {"key": "Klang", "value": "Klang", "postcodes": ["41000", "41050", "41100", "41150", "41200", "41250", "41300", "41400", "41506", "41560", "41586", "41672", "41700", "41710", "41720", "41900", "41902", "41904", "41906", "41908", "41910", "41912", "41914", "41916", "41918", "41990", "42100"]}, {"key": "Klia", "value": "Klia", "postcodes": []}, {"key": "Kuala Kubu Baru", "value": "Kuala Kubu Baru", "postcodes": ["44000", "44010", "44020", "44110"]}, {"key": "Kuala Selangor", "value": "Kuala Selangor", "postcodes": ["45000"]}, {"key": "Pelabuhan Klang", "value": "Pelabuhan Klang", "postcodes": ["42000", "42009"]}, {"key": "Petaling Jaya", "value": "Petaling Jaya", "postcodes": ["46000", "46050", "46100", "46150", "46200", "46300", "46350", "46400", "46506", "46547", "46549", "46551", "46564", "46582", "46598", "46662", "46667", "46668", "46672", "46675", "46700", "46710", "46720", "46730", "46740", "46750", "46760", "46770", "46780", "46781", "46782", "46783", "46784", "46785", "46786", "46787", "46788", "46789", "46790", "46791", "46792", "46793", "46794", "46795", "46796", "46797", "46798", "46799", "46800", "46801", "46802", "46803", "46804", "46805", "46806", "46860", "46870", "46960", "46962", "46964", "46966", "46968", "46970", "46972", "46974", "46976", "46978", "47300", "47301", "47307", "47400", "47410", "47800", "47810", "47820", "47830"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["47100", "47107", "47109", "47110", "47120", "47130", "47140", "47150", "47160", "47170", "47180", "47190"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["42960"]}, {"key": "Pulau <PERSON>dah", "value": "Pulau <PERSON>dah", "postcodes": ["42920"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["42940"]}, {"key": "Rasa", "value": "Rasa", "postcodes": ["44200"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["48000", "48010", "48020", "48050", "48300"]}, {"key": "Sabak <PERSON>", "value": "Sabak <PERSON>", "postcodes": ["45200", "45207"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["45400"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["43500"]}, {"key": "Sepang", "value": "Sepang", "postcodes": ["43900"]}, {"key": "Serdang", "value": "Serdang", "postcodes": ["43400", "09800", "09809", "09810"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["48200"]}, {"key": "Seri Kembangan", "value": "Seri Kembangan", "postcodes": ["43300"]}, {"key": "<PERSON>", "value": "<PERSON>", "postcodes": ["40000", "40100", "40150", "40160", "40170", "40200", "40300", "40400", "40450", "40460", "40470", "40500", "40502", "40503", "40504", "40505", "40512", "40517", "40520", "40529", "40542", "40548", "40550", "40551", "40560", "40564", "40570", "40572", "40576", "40578", "40582", "40590", "40592", "40594", "40596", "40598", "40604", "40607", "40608", "40610", "40612", "40620", "40622", "40626", "40632", "40646", "40648", "40660", "40664", "40670", "40672", "40673", "40674", "40675", "40676", "40680", "40690", "40700", "40702", "40704", "40706", "40708", "40710", "40712", "40714", "40716", "40718", "40720", "40722", "40724", "40726", "40728", "40730", "40732", "40800", "40802", "40804", "40806", "40808", "40810"]}, {"key": "Subang Airport", "value": "Subang Airport", "postcodes": []}, {"key": "Subang Jaya", "value": "Subang Jaya", "postcodes": ["46150", "47200", "47500", "47507", "47600", "47610", "47620", "47630", "47640", "47650"]}, {"key": "Sungai <PERSON>", "value": "Sungai <PERSON>", "postcodes": ["45100"]}, {"key": "Sungai Besar", "value": "Sungai Besar", "postcodes": ["45300"]}, {"key": "Sungai Buloh", "value": "Sungai Buloh", "postcodes": ["40160", "47000"]}, {"key": "Sungai Pelek", "value": "Sungai Pelek", "postcodes": ["43950"]}, {"key": "Tanjong Karang", "value": "Tanjong Karang", "postcodes": ["45500", "45507"]}, {"key": "Tanjong Sepat", "value": "Tanjong Sepat", "postcodes": ["42800"]}, {"key": "Telok Panglima Garang", "value": "Telok Panglima Garang", "postcodes": ["42500", "42507", "42509"]}]}, {"key": "Terengganu", "value": "Terengganu", "cities": [{"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["21800", "21810", "21820"]}, {"key": "Al Muktatfi Bill<PERSON>", "value": "Al Muktatfi Bill<PERSON>", "postcodes": ["23400"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["24000", "24050"]}, {"key": "Bukit Besi", "value": "Bukit Besi", "postcodes": ["23200"]}, {"key": "Bukit Payong", "value": "Bukit Payong", "postcodes": ["21400"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["24060"]}, {"key": "Chalok", "value": "Chalok", "postcodes": ["21400", "21450"]}, {"key": "C<PERSON><PERSON>", "value": "C<PERSON><PERSON>", "postcodes": ["24000", "24007", "24009"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["23000", "23007", "23009", "23050"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["22000", "22010", "22020"]}, {"key": "Kampung Raja", "value": "Kampung Raja", "postcodes": ["22200"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["24200", "24207", "24209"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["24300"]}, {"key": "Ketengah Jaya", "value": "Ketengah Jaya", "postcodes": ["23300"]}, {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["24100", "24107", "24109"]}, {"key": "Kuala Berang", "value": "Kuala Berang", "postcodes": ["21700"]}, {"key": "Kuala Besut", "value": "Kuala Besut", "postcodes": ["22300", "22307", "22309"]}, {"key": "Kuala Terengganu", "value": "Kuala Terengganu", "postcodes": ["20000", "20050", "20100", "20200", "20300", "20400", "20500", "20502", "20503", "20504", "20505", "20506", "20508", "20512", "20514", "20516", "20517", "20518", "20519", "20520", "20532", "20534", "20536", "20538", "20540", "20542", "20546", "20548", "20550", "20551", "20552", "20554", "20556", "20560", "20564", "20566", "20568", "20570", "20572", "20576", "20578", "20582", "20586", "20590", "20592", "20594", "20596", "20598", "20600", "20604", "20606", "20608", "20609", "20610", "20612", "20614", "20618", "20620", "20622", "20626", "20628", "20630", "20632", "20646", "20648", "20656", "20658", "20660", "20661", "20662", "20664", "20668", "20670", "20672", "20673", "20674", "20675", "20676", "20680", "20690", "20698", "20700", "20710", "20720", "20900", "20902", "20904", "20906", "20908", "20910", "20912", "20914", "20916", "20918", "20920", "20922", "20924", "20926", "20928", "20930", "20990", "21000", "21009", "21010", "21020", "21030", "21040", "21060", "21070", "21080", "21090", "21100", "21109", "21200", "21209", "21220", "21300", "21309", "21400"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["21600", "21610"]}, {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["23100"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "postcodes": ["22100", "22107", "22109", "22110", "22120"]}, {"key": "Sung<PERSON> Tong", "value": "Sung<PERSON> Tong", "postcodes": ["21500"]}]}, {"key": "Wp <PERSON><PERSON>", "value": "WP Labuan", "cities": [{"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "postcodes": ["87000", "87010", "87011", "87012", "87013", "87014", "87015", "87016", "87017", "87018", "87019", "87020", "87021", "87022", "87023", "87024", "87025", "87026", "87027", "87028", "87029", "87030", "87031", "87032", "87033"]}]}, {"key": "Wp Putrajaya", "value": "WP Putrajaya", "cities": [{"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "postcodes": ["62000", "62007", "62050", "62100", "62150", "62200", "62250", "62300", "62502", "62504", "62505", "62506", "62510", "62512", "62514", "62516", "62517", "62518", "62519", "62520", "62522", "62524", "62526", "62527", "62530", "62532", "62536", "62540", "62542", "62546", "62550", "62551", "62564", "62570", "62574", "62576", "62582", "62584", "62590", "62592", "62594", "62596", "62600", "62602", "62604", "62605", "62606", "62607", "62612", "62616", "62618", "62620", "62622", "62623", "62624", "62628", "62630", "62632", "62640", "62644", "62648", "62652", "62654", "62662", "62668", "62670", "62674", "62675", "62676", "62677", "62686", "62690", "62692"]}]}]}, "state-iso-3-to-state-key": {"JHR": "<PERSON><PERSON>", "KDH": "Kedah", "KTN": "<PERSON><PERSON><PERSON>", "MLK": "<PERSON><PERSON>", "NSN": "<PERSON><PERSON><PERSON>", "PHG": "<PERSON><PERSON>", "PNG": "<PERSON><PERSON><PERSON>", "PRK": "<PERSON><PERSON>", "PLS": "<PERSON><PERSON>", "SBH": "Sabah", "SWK": "Sarawak", "SGR": "Selangor", "TRG": "Terengganu", "KUL": "WP Kuala Lumpur", "LBN": "WP Labuan", "PJY": "WP Putrajaya"}, "biz-financial-information": {"v2.0.0": {"employmentType": [{"key": "SELF_EMPLOYED", "applicationKey": "embeddedForm.financial.selfEmployed", "value": "Self-Employed"}, {"key": "PUBLIC_EMPLOYEE", "applicationKey": "embeddedForm.financial.publicEmployee", "value": "Government Employee / Civil Servant"}, {"key": "PRIVATE_EMPLOYEE", "applicationKey": "embeddedForm.financial.privateEmployee", "value": "Private Sector Employee"}, {"key": "RETIRED", "applicationKey": "embeddedForm.financial.retired", "value": "Retired / Pensioner"}, {"key": "STUDENT", "applicationKey": "embeddedForm.financial.student", "value": "Student"}, {"key": "UNEMPLOYED", "applicationKey": "embeddedForm.financial.unemployed", "value": "Unemployed / House Wife / House Husband"}], "monthlyIncome": [{"key": "01", "value": "Below RM2,000"}, {"key": "02", "value": "RM2,000 to RM2,999"}, {"key": "03", "value": "RM3,000 to RM4,999"}, {"key": "04", "value": "RM5,000 to RM6,999"}, {"key": "05", "value": "RM7,000 to RM8,999"}, {"key": "06", "value": "RM9,000 to RM10,999"}, {"key": "07", "value": "RM11,000 to RM14,999"}, {"key": "08", "value": "RM15,000 to RM24,999"}, {"key": "09", "value": "Above RM25,000"}], "privateEmployee": [{"key": "B15", "value": "Administrators/Clerks"}, {"key": "B52", "value": "Agent (Insurance,Commercial)"}, {"key": "B32", "value": "Analyst"}, {"key": "B21", "value": "Architects"}, {"key": "B43", "value": "Artist/Actors/Performers"}, {"key": "B54", "value": "Athletes/Sports Person"}, {"key": "B60", "value": "Attendants/Stewards"}, {"key": "B42", "value": "Authors/Journalists/Linguists/Writers/Translators"}, {"key": "B62", "value": "Beauticians/Hairdressers"}, {"key": "B04", "value": "Business Development Executives and Managers"}, {"key": "B73", "value": "Business Owner"}, {"key": "B53", "value": "Buyers/Purchaser"}, {"key": "B66", "value": "Carpenters/Plumbers/Mechanics/Repairers"}, {"key": "B63", "value": "Cashiers"}, {"key": "B16", "value": "Chef/Cooks/Bakers"}, {"key": "B17", "value": "Chemists"}, {"key": "B64", "value": "Child Care Workers"}, {"key": "B69", "value": "Cleaners/Helpers"}, {"key": "B55", "value": "Coaches/Trainers/Instructors"}, {"key": "B67", "value": "Construction Workers"}, {"key": "B58", "value": "Consultants"}, {"key": "B72", "value": "Content Creator/KOLs"}, {"key": "B07", "value": "Customers and Operations Executives and Managers"}, {"key": "B34", "value": "Database Analyst/Scientist/Administrators"}, {"key": "B51", "value": "Dealers/Brokers"}, {"key": "B27", "value": "Dentists"}, {"key": "B22", "value": "Designers"}, {"key": "B33", "value": "Developers/Programmers"}, {"key": "B25", "value": "Doctors"}, {"key": "B24", "value": "Drivers"}, {"key": "B36", "value": "Economists"}, {"key": "B20", "value": "Engineer"}, {"key": "B65", "value": "Factory Worker"}, {"key": "B19", "value": "Farming,Forestry and Fisheries Advisers/Workers"}, {"key": "B09", "value": "Finance Executives and Managers"}, {"key": "B70", "value": "Gig Worker/Freelancers"}, {"key": "B08", "value": "Human Resource Executives and Managers"}, {"key": "B10", "value": "Information Technology Executives and Managers"}, {"key": "B71", "value": "Investor"}, {"key": "B35", "value": "Lawyers/Judges"}, {"key": "B12", "value": "Legal Executives and Managers"}, {"key": "B01", "value": "Legislators"}, {"key": "B02", "value": "Managing Directors and Chief Executives"}, {"key": "B18", "value": "Mathematicians,Actuaries and Statisticians"}, {"key": "B45", "value": "Musicians/Singers/Composers"}, {"key": "B26", "value": "Nurse/Midwifery"}, {"key": "B29", "value": "Optician/Optometrist/Ophthalmologist"}, {"key": "B28", "value": "Pharmacists"}, {"key": "B38", "value": "Philosophers,Historians and Political Scientists"}, {"key": "B56", "value": "Photographer"}, {"key": "B23", "value": "Pilots"}, {"key": "B14", "value": "Planners"}, {"key": "B46", "value": "Producers/Directors (Entertainment)"}, {"key": "B06", "value": "Product Management Executives"}, {"key": "B05", "value": "Project Executives and Managers"}, {"key": "B39", "value": "Psychologists"}, {"key": "B59", "value": "Receptionist"}, {"key": "B41", "value": "Religious Professionals"}, {"key": "B11", "value": "Risk Managements Executives and Managers"}, {"key": "B03", "value": "Sales and Marketing Executives and Managers"}, {"key": "B31", "value": "Sales Person/Marketers"}, {"key": "B13", "value": "Scientist"}, {"key": "B57", "value": "Secretaries"}, {"key": "B49", "value": "Social Cultural/Worker/Counsellor"}, {"key": "B40", "value": "Social Work and Counseling Professionals"}, {"key": "B37", "value": "Sociologists,Anthropologists and Related Professionals"}, {"key": "B68", "value": "Tailors/Dressmakers"}, {"key": "B30", "value": "Teachers/Lecturers"}, {"key": "B47", "value": "Technician/Electricians"}, {"key": "B50", "value": "Therapists"}, {"key": "B61", "value": "Waiters and Bartenders"}, {"key": "B74", "value": "Others"}], "publicEmployee": [{"key": "A01", "value": "Armed Forces"}, {"key": "A02", "value": "Civil Defences"}, {"key": "A03", "value": "Customs and Border Inspectors"}, {"key": "A04", "value": "Firefighters"}, {"key": "A05", "value": "Immigration or Custom Officers"}, {"key": "A08", "value": "Police Inspectors and Detectives"}, {"key": "A07", "value": "Police Officers"}, {"key": "A06", "value": "Prison Guards"}, {"key": "A09", "value": "Regulatory Governments"}, {"key": "A10", "value": "Taxation and Excise Officials"}, {"key": "A11", "value": "Other Government Officials"}], "selfEmployed": [{"key": "B15", "value": "Administrators/Clerks"}, {"key": "B52", "value": "Agent (Insurance,Commercial)"}, {"key": "B32", "value": "Analyst"}, {"key": "B21", "value": "Architects"}, {"key": "B43", "value": "Artist/Actors/Performers"}, {"key": "B54", "value": "Athletes/Sports Person"}, {"key": "B60", "value": "Attendants/Stewards"}, {"key": "B42", "value": "Authors/Journalists/Linguists/Writers/Translators"}, {"key": "B62", "value": "Beauticians/Hairdressers"}, {"key": "B04", "value": "Business Development Executives and Managers"}, {"key": "B73", "value": "Business Owner"}, {"key": "B53", "value": "Buyers/Purchaser"}, {"key": "B66", "value": "Carpenters/Plumbers/Mechanics/Repairers"}, {"key": "B63", "value": "Cashiers"}, {"key": "B16", "value": "Chef/Cooks/Bakers"}, {"key": "B17", "value": "Chemists"}, {"key": "B64", "value": "Child Care Workers"}, {"key": "B69", "value": "Cleaners/Helpers"}, {"key": "B55", "value": "Coaches/Trainers/Instructors"}, {"key": "B67", "value": "Construction Workers"}, {"key": "B58", "value": "Consultants"}, {"key": "B72", "value": "Content Creator/KOLs"}, {"key": "B07", "value": "Customers and Operations Executives and Managers"}, {"key": "B34", "value": "Database Analyst/Scientist/Administrators"}, {"key": "B51", "value": "Dealers/Brokers"}, {"key": "B27", "value": "Dentists"}, {"key": "B22", "value": "Designers"}, {"key": "B33", "value": "Developers/Programmers"}, {"key": "B25", "value": "Doctors"}, {"key": "B24", "value": "Drivers"}, {"key": "B36", "value": "Economists"}, {"key": "B20", "value": "Engineer"}, {"key": "B65", "value": "Factory Worker"}, {"key": "B19", "value": "Farming,Forestry and Fisheries Advisers/Workers"}, {"key": "B09", "value": "Finance Executives and Managers"}, {"key": "B70", "value": "Gig Worker/Freelancers"}, {"key": "B08", "value": "Human Resource Executives and Managers"}, {"key": "B10", "value": "Information Technology Executives and Managers"}, {"key": "B71", "value": "Investor"}, {"key": "B35", "value": "Lawyers/Judges"}, {"key": "B12", "value": "Legal Executives and Managers"}, {"key": "B01", "value": "Legislators"}, {"key": "B02", "value": "Managing Directors and Chief Executives"}, {"key": "B18", "value": "Mathematicians,Actuaries and Statisticians"}, {"key": "B45", "value": "Musicians/Singers/Composers"}, {"key": "B26", "value": "Nurse/Midwifery"}, {"key": "B29", "value": "Optician/Optometrist/Ophthalmologist"}, {"key": "B28", "value": "Pharmacists"}, {"key": "B38", "value": "Philosophers,Historians and Political Scientists"}, {"key": "B56", "value": "Photographer"}, {"key": "B23", "value": "Pilots"}, {"key": "B14", "value": "Planners"}, {"key": "B46", "value": "Producers/Directors (Entertainment)"}, {"key": "B06", "value": "Product Management Executives"}, {"key": "B05", "value": "Project Executives and Managers"}, {"key": "B39", "value": "Psychologists"}, {"key": "B59", "value": "Receptionist"}, {"key": "B41", "value": "Religious Professionals"}, {"key": "B11", "value": "Risk Managements Executives and Managers"}, {"key": "B03", "value": "Sales and Marketing Executives and Managers"}, {"key": "B31", "value": "Sales Person/Marketers"}, {"key": "B13", "value": "Scientist"}, {"key": "B57", "value": "Secretaries"}, {"key": "B49", "value": "Social Cultural/Worker/Counsellor"}, {"key": "B40", "value": "Social Work and Counseling Professionals"}, {"key": "B37", "value": "Sociologists,Anthropologists and Related Professionals"}, {"key": "B68", "value": "Tailors/Dressmakers"}, {"key": "B30", "value": "Teachers/Lecturers"}, {"key": "B47", "value": "Technician/Electricians"}, {"key": "B50", "value": "Therapists"}, {"key": "B61", "value": "Waiters and Bartenders"}, {"key": "B74", "value": "Others"}], "natureOfBusiness": [{"key": "01", "value": "Agriculture, Farming, Fishing", "deprecated": false}, {"key": "02", "value": "Agriculture - Palm Oil", "deprecated": false}, {"key": "03", "value": "Forestry And Logging", "deprecated": false}, {"key": "04", "value": "Production Of B<PERSON>ve <PERSON> (Oyster, Mussel), <PERSON><PERSON><PERSON> Lings, Shrimp Post-Larvae, Fish Fry And Fingerlings", "deprecated": false}, {"key": "05", "value": "Mining And Quarrying", "deprecated": false}, {"key": "06", "value": "Mining Of Uranium And Thorium Ores", "deprecated": false}, {"key": "07", "value": "Production And Manufacturing", "deprecated": false}, {"key": "08", "value": "Production And Manufacturing - Palm Oil", "deprecated": false}, {"key": "09", "value": "Production And Manufacturing - Alcohol, Spirits & Tobacco Products", "deprecated": false}, {"key": "10", "value": "Manufacture Of Wood Charcoal", "deprecated": false}, {"key": "11", "value": "Production And Manufacturing-Coke Oven Products", "deprecated": false}, {"key": "12", "value": "Production And Manufacturing - Basic Organic Chemicals", "deprecated": false}, {"key": "13", "value": "Production And Manufacturing-Manufacture Of Basic Iron And Steel", "deprecated": false}, {"key": "14", "value": "Production And Manufacturing-Tin Smelting", "deprecated": false}, {"key": "15", "value": "Manufacture Of Weapons And Amunition", "deprecated": false}, {"key": "16", "value": "Production And Manufacturing Of Watches, Clocks & Parts", "deprecated": false}, {"key": "17", "value": "Manufacture Of Military Fighting Vehicles", "deprecated": false}, {"key": "18", "value": "Manufacture Of Jewellery, Bijouterie And Related Articles", "deprecated": false}, {"key": "19", "value": "Repair Services", "deprecated": false}, {"key": "20", "value": "Electricity, Gas, Steam & Air Con Supply", "deprecated": false}, {"key": "21", "value": "Water Supply; Sewerage, Waste Management And Remediation Activities", "deprecated": false}, {"key": "22", "value": "Construction", "deprecated": false}, {"key": "23", "value": "Construction-Surface Work On Streets, Roads, Highways, Bridges Or Tunnels", "deprecated": false}, {"key": "24", "value": "Retail/Wholesale Trade", "deprecated": false}, {"key": "25", "value": "Maintenance And Repair Of Motor Vehicles", "deprecated": false}, {"key": "26", "value": "Retail/Wholesale Of Alcohol & Tobacco", "deprecated": false}, {"key": "27", "value": "Retail/Wholesale Of Antiques,Watches, Clocks And Jewellery", "deprecated": false}, {"key": "28", "value": "Retail Sale In Non-Specialized Stores", "deprecated": false}, {"key": "29", "value": "Food and beverage", "deprecated": false}, {"key": "30", "value": "Transportation & Storage", "deprecated": false}, {"key": "31", "value": "Accomodation", "deprecated": false}, {"key": "32", "value": "Pubs, Bars, Discotheques, Coffee Houses, Cocktail Lounges, Night Clubs And Karaoke", "deprecated": false}, {"key": "33", "value": "Media, Broadcasting And Publication", "deprecated": false}, {"key": "34", "value": "Telecommunications", "deprecated": false}, {"key": "35", "value": "Information Service,Computer Programming, Consultancy And Related Activities", "deprecated": false}, {"key": "36", "value": "Financial Services Activities", "deprecated": false}, {"key": "37", "value": "Trusts, Funds And Similar Financial Entities", "deprecated": false}, {"key": "38", "value": "Licensed Money Lending,Pawnshops, Gold Buillon, Money Changing Services", "deprecated": false}, {"key": "39", "value": "Insurance/Takaful, Reinsurance/Retakaful And Pension Funding", "deprecated": false}, {"key": "40", "value": "Activities Auxiliary To Financial Service Activities", "deprecated": false}, {"key": "41", "value": "Real Estate", "deprecated": false}, {"key": "42", "value": "Legal Activities", "deprecated": false}, {"key": "43", "value": "Accounting, Bookkeeping And Auditing Activities; Tax Consultancy", "deprecated": false}, {"key": "44", "value": "Management Consultancy Activities", "deprecated": false}, {"key": "45", "value": "Architectural And Engineering Activities And Related Technical Consultancy", "deprecated": false}, {"key": "46", "value": "Research And Experimental Development On Natural Sciences, Social Sciences, Humanities And Engineering", "deprecated": false}, {"key": "47", "value": "Research And Development On Social Sciences", "deprecated": false}, {"key": "48", "value": "Advertising And Market Research", "deprecated": false}, {"key": "49", "value": "Other Professional, Scientific And Technical Activities", "deprecated": false}, {"key": "50", "value": "Rental And Leasing Activities", "deprecated": false}, {"key": "51", "value": "Employment Activities", "deprecated": false}, {"key": "52", "value": "Travel Agency, Tour Operator, Reservation Service And Related Activities", "deprecated": false}, {"key": "53", "value": "Security And Investigation Activities", "deprecated": false}, {"key": "54", "value": "Services To Building And Landscape Activities", "deprecated": false}, {"key": "55", "value": "Office Administrative, Office Support And Other Business Support Activities", "deprecated": false}, {"key": "56", "value": "Administration Of The State And The Economic And Social Policy Of The Community", "deprecated": false}, {"key": "57", "value": "Education", "deprecated": false}, {"key": "58", "value": "Health & Residential Care Activities", "deprecated": false}, {"key": "59", "value": "Maternity Home Services (Outside Hospital)", "deprecated": false}, {"key": "60", "value": "Orphanages, Welfare Homes Services, Other Residential Care Activities", "deprecated": false}, {"key": "61", "value": "Other Social Work Activities Without Accommodation N.E.C", "deprecated": false}, {"key": "62", "value": "Creative, Arts And Entertainment Activities", "deprecated": false}, {"key": "63", "value": "Libraries, Archives, Museums And Other Cultural Activities", "deprecated": false}, {"key": "64", "value": "Gambling And Betting Activities", "deprecated": false}, {"key": "65", "value": "Sports Activities", "deprecated": false}, {"key": "66", "value": "Amusement And Recreation Activities", "deprecated": false}, {"key": "67", "value": "Operation Of Recreational Transport Facilities", "deprecated": false}, {"key": "68", "value": "Amusement And Recreation Activities - Others", "deprecated": false}, {"key": "69", "value": "Operation Of Discotheques And Dance Floors", "deprecated": false}, {"key": "70", "value": "Activities Of Membership Organizations", "deprecated": false}, {"key": "71", "value": "Repair Of Computers And Communication Equipment", "deprecated": false}, {"key": "72", "value": "Repair Of Personal And Household Goods", "deprecated": false}, {"key": "73", "value": "Repair Of Personal And Household Goods-Others", "deprecated": false}, {"key": "74", "value": "Other Personal Service Activities", "deprecated": false}, {"key": "75", "value": "Activities Of Sauna, Steam Baths, Massage Salons", "deprecated": false}, {"key": "76", "value": "Astrological And Spiritualists' Activities", "deprecated": false}, {"key": "77", "value": "Social Activities Such As Escort Services, Dating Services, Services Of Marriage Bureaux", "deprecated": false}, {"key": "78", "value": "Activities Of Households As Employers Of Domestic Personnel", "deprecated": false}, {"key": "79", "value": "Undifferentiated Goods-And Services-Producing Activities Of Private Households For Own Use", "deprecated": false}, {"key": "80", "value": "Activities Of Extraterritorial Organizations And Bodies", "deprecated": false}, {"key": "81", "value": "Nec - Not Elsewhere Categorised", "deprecated": false}, {"key": "82", "value": "Sea And Coastal Water Transport", "deprecated": false}, {"key": "83", "value": "Provision of services to the community as a whole", "deprecated": false}]}}, "retail-financial-information": {"v1.3.0": {"employmentType": [{"key": "SELF_EMPLOYED", "applicationKey": "embeddedForm.financial.selfEmployed", "value": "Self-Employed"}, {"key": "PUBLIC_EMPLOYEE", "applicationKey": "embeddedForm.financial.publicEmployee", "value": "Government Employee / Civil Servant"}, {"key": "PRIVATE_EMPLOYEE", "applicationKey": "embeddedForm.financial.privateEmployee", "value": "Private Sector Employee"}, {"key": "RETIRED", "applicationKey": "embeddedForm.financial.retired", "value": "Retired / Pensioner"}, {"key": "STUDENT", "applicationKey": "embeddedForm.financial.student", "value": "Student"}, {"key": "UNEMPLOYED", "applicationKey": "embeddedForm.financial.unemployed", "value": "Unemployed / House Wife / House Husband"}], "monthlyIncome": [{"key": "01", "value": "Below RM2,000"}, {"key": "02", "value": "RM2,000 to RM2,999"}, {"key": "03", "value": "RM3,000 to RM4,999"}, {"key": "04", "value": "RM5,000 to RM6,999"}, {"key": "05", "value": "RM7,000 to RM8,999"}, {"key": "06", "value": "RM9,000 to RM10,999"}, {"key": "07", "value": "RM11,000 to RM14,999"}, {"key": "08", "value": "RM15,000 to RM24,999"}, {"key": "09", "value": "Above RM25,000"}], "privateEmployee": [{"key": "B15", "value": "Administrators/Clerks"}, {"key": "B52", "value": "Agent (Insurance,Commercial)"}, {"key": "B32", "value": "Analyst"}, {"key": "B21", "value": "Architects"}, {"key": "B43", "value": "Artist/Actors/Performers"}, {"key": "B54", "value": "Athletes/Sports Person"}, {"key": "B60", "value": "Attendants/Stewards"}, {"key": "B42", "value": "Authors/Journalists/Linguists/Writers/Translators"}, {"key": "B62", "value": "Beauticians/Hairdressers"}, {"key": "B04", "value": "Business Development Executives and Managers"}, {"key": "B73", "value": "Business Owner"}, {"key": "B53", "value": "Buyers/Purchaser"}, {"key": "B66", "value": "Carpenters/Plumbers/Mechanics/Repairers"}, {"key": "B63", "value": "Cashiers"}, {"key": "B16", "value": "Chef/Cooks/Bakers"}, {"key": "B17", "value": "Chemists"}, {"key": "B64", "value": "Child Care Workers"}, {"key": "B69", "value": "Cleaners/Helpers"}, {"key": "B55", "value": "Coaches/Trainers/Instructors"}, {"key": "B67", "value": "Construction Workers"}, {"key": "B58", "value": "Consultants"}, {"key": "B72", "value": "Content Creator/KOLs"}, {"key": "B07", "value": "Customers and Operations Executives and Managers"}, {"key": "B34", "value": "Database Analyst/Scientist/Administrators"}, {"key": "B51", "value": "Dealers/Brokers"}, {"key": "B27", "value": "Dentists"}, {"key": "B22", "value": "Designers"}, {"key": "B33", "value": "Developers/Programmers"}, {"key": "B25", "value": "Doctors"}, {"key": "B24", "value": "Drivers"}, {"key": "B36", "value": "Economists"}, {"key": "B20", "value": "Engineer"}, {"key": "B65", "value": "Factory Worker"}, {"key": "B19", "value": "Farming,Forestry and Fisheries Advisers/Workers"}, {"key": "B09", "value": "Finance Executives and Managers"}, {"key": "B70", "value": "Gig Worker/Freelancers"}, {"key": "B08", "value": "Human Resource Executives and Managers"}, {"key": "B10", "value": "Information Technology Executives and Managers"}, {"key": "B71", "value": "Investor"}, {"key": "B35", "value": "Lawyers/Judges"}, {"key": "B12", "value": "Legal Executives and Managers"}, {"key": "B01", "value": "Legislators"}, {"key": "B02", "value": "Managing Directors and Chief Executives"}, {"key": "B18", "value": "Mathematicians,Actuaries and Statisticians"}, {"key": "B45", "value": "Musicians/Singers/Composers"}, {"key": "B26", "value": "Nurse/Midwifery"}, {"key": "B29", "value": "Optician/Optometrist/Ophthalmologist"}, {"key": "B28", "value": "Pharmacists"}, {"key": "B38", "value": "Philosophers,Historians and Political Scientists"}, {"key": "B56", "value": "Photographer"}, {"key": "B23", "value": "Pilots"}, {"key": "B14", "value": "Planners"}, {"key": "B46", "value": "Producers/Directors (Entertainment)"}, {"key": "B06", "value": "Product Management Executives"}, {"key": "B05", "value": "Project Executives and Managers"}, {"key": "B39", "value": "Psychologists"}, {"key": "B59", "value": "Receptionist"}, {"key": "B41", "value": "Religious Professionals"}, {"key": "B11", "value": "Risk Managements Executives and Managers"}, {"key": "B03", "value": "Sales and Marketing Executives and Managers"}, {"key": "B31", "value": "Sales Person/Marketers"}, {"key": "B13", "value": "Scientist"}, {"key": "B57", "value": "Secretaries"}, {"key": "B49", "value": "Social Cultural/Worker/Counsellor"}, {"key": "B40", "value": "Social Work and Counseling Professionals"}, {"key": "B37", "value": "Sociologists,Anthropologists and Related Professionals"}, {"key": "B68", "value": "Tailors/Dressmakers"}, {"key": "B30", "value": "Teachers/Lecturers"}, {"key": "B47", "value": "Technician/Electricians"}, {"key": "B50", "value": "Therapists"}, {"key": "B61", "value": "Waiters and Bartenders"}, {"key": "B74", "value": "Others"}], "publicEmployee": [{"key": "A01", "value": "Armed Forces"}, {"key": "A02", "value": "Civil Defences"}, {"key": "A03", "value": "Customs and Border Inspectors"}, {"key": "A04", "value": "Firefighters"}, {"key": "A05", "value": "Immigration or Custom Officers"}, {"key": "A08", "value": "Police Inspectors and Detectives"}, {"key": "A07", "value": "Police Officers"}, {"key": "A06", "value": "Prison Guards"}, {"key": "A09", "value": "Regulatory Governments"}, {"key": "A10", "value": "Taxation and Excise Officials"}, {"key": "A11", "value": "Other Government Officials"}], "selfEmployed": [{"key": "B15", "value": "Administrators/Clerks"}, {"key": "B52", "value": "Agent (Insurance,Commercial)"}, {"key": "B32", "value": "Analyst"}, {"key": "B21", "value": "Architects"}, {"key": "B43", "value": "Artist/Actors/Performers"}, {"key": "B54", "value": "Athletes/Sports Person"}, {"key": "B60", "value": "Attendants/Stewards"}, {"key": "B42", "value": "Authors/Journalists/Linguists/Writers/Translators"}, {"key": "B62", "value": "Beauticians/Hairdressers"}, {"key": "B04", "value": "Business Development Executives and Managers"}, {"key": "B73", "value": "Business Owner"}, {"key": "B53", "value": "Buyers/Purchaser"}, {"key": "B66", "value": "Carpenters/Plumbers/Mechanics/Repairers"}, {"key": "B63", "value": "Cashiers"}, {"key": "B16", "value": "Chef/Cooks/Bakers"}, {"key": "B17", "value": "Chemists"}, {"key": "B64", "value": "Child Care Workers"}, {"key": "B69", "value": "Cleaners/Helpers"}, {"key": "B55", "value": "Coaches/Trainers/Instructors"}, {"key": "B67", "value": "Construction Workers"}, {"key": "B58", "value": "Consultants"}, {"key": "B72", "value": "Content Creator/KOLs"}, {"key": "B07", "value": "Customers and Operations Executives and Managers"}, {"key": "B34", "value": "Database Analyst/Scientist/Administrators"}, {"key": "B51", "value": "Dealers/Brokers"}, {"key": "B27", "value": "Dentists"}, {"key": "B22", "value": "Designers"}, {"key": "B33", "value": "Developers/Programmers"}, {"key": "B25", "value": "Doctors"}, {"key": "B24", "value": "Drivers"}, {"key": "B36", "value": "Economists"}, {"key": "B20", "value": "Engineer"}, {"key": "B65", "value": "Factory Worker"}, {"key": "B19", "value": "Farming,Forestry and Fisheries Advisers/Workers"}, {"key": "B09", "value": "Finance Executives and Managers"}, {"key": "B70", "value": "Gig Worker/Freelancers"}, {"key": "B08", "value": "Human Resource Executives and Managers"}, {"key": "B10", "value": "Information Technology Executives and Managers"}, {"key": "B71", "value": "Investor"}, {"key": "B35", "value": "Lawyers/Judges"}, {"key": "B12", "value": "Legal Executives and Managers"}, {"key": "B01", "value": "Legislators"}, {"key": "B02", "value": "Managing Directors and Chief Executives"}, {"key": "B18", "value": "Mathematicians,Actuaries and Statisticians"}, {"key": "B45", "value": "Musicians/Singers/Composers"}, {"key": "B26", "value": "Nurse/Midwifery"}, {"key": "B29", "value": "Optician/Optometrist/Ophthalmologist"}, {"key": "B28", "value": "Pharmacists"}, {"key": "B38", "value": "Philosophers,Historians and Political Scientists"}, {"key": "B56", "value": "Photographer"}, {"key": "B23", "value": "Pilots"}, {"key": "B14", "value": "Planners"}, {"key": "B46", "value": "Producers/Directors (Entertainment)"}, {"key": "B06", "value": "Product Management Executives"}, {"key": "B05", "value": "Project Executives and Managers"}, {"key": "B39", "value": "Psychologists"}, {"key": "B59", "value": "Receptionist"}, {"key": "B41", "value": "Religious Professionals"}, {"key": "B11", "value": "Risk Managements Executives and Managers"}, {"key": "B03", "value": "Sales and Marketing Executives and Managers"}, {"key": "B31", "value": "Sales Person/Marketers"}, {"key": "B13", "value": "Scientist"}, {"key": "B57", "value": "Secretaries"}, {"key": "B49", "value": "Social Cultural/Worker/Counsellor"}, {"key": "B40", "value": "Social Work and Counseling Professionals"}, {"key": "B37", "value": "Sociologists,Anthropologists and Related Professionals"}, {"key": "B68", "value": "Tailors/Dressmakers"}, {"key": "B30", "value": "Teachers/Lecturers"}, {"key": "B47", "value": "Technician/Electricians"}, {"key": "B50", "value": "Therapists"}, {"key": "B61", "value": "Waiters and Bartenders"}, {"key": "B74", "value": "Others"}], "natureOfBusiness": [{"key": "01", "value": "Agriculture, Forestry, Logging, Farming", "deprecated": false}, {"key": "02", "value": "Entertainment", "deprecated": true}, {"key": "03", "value": "Transport services", "deprecated": false}, {"key": "04", "value": "Business", "deprecated": false}, {"key": "05", "value": "Construction/Real Estate", "deprecated": false}, {"key": "06", "value": "Education", "deprecated": false}, {"key": "07", "value": "Finance", "deprecated": false}, {"key": "08", "value": "Insurance", "deprecated": false}, {"key": "09", "value": "Travel and tourism", "deprecated": false}, {"key": "10", "value": "Health and Medical", "deprecated": false}, {"key": "11", "value": "Hotel", "deprecated": false}, {"key": "12", "value": "Household", "deprecated": false}, {"key": "13", "value": "Production and Manufacturing", "deprecated": false}, {"key": "14", "value": "Information Technology", "deprecated": false}, {"key": "15", "value": "Legal", "deprecated": false}, {"key": "16", "value": "Oil and Gas", "deprecated": false}, {"key": "17", "value": "Restaurant, food, beverages, and bars", "deprecated": true}, {"key": "18", "value": "Repair Services", "deprecated": false}, {"key": "19", "value": "Retail/Wholesale Trade", "deprecated": false}, {"key": "20", "value": "Tobacco", "deprecated": false}, {"key": "21", "value": "Marketing and Advertising", "deprecated": false}, {"key": "22", "value": "Utility (Electricity, gas, steam, air-conditioning, water supply, waste)", "deprecated": false}, {"key": "23", "value": "Non-profit", "deprecated": false}, {"key": "24", "value": "Chemical", "deprecated": false}, {"key": "25", "value": "Sports", "deprecated": false}, {"key": "26", "value": "Defense/Government", "deprecated": true}, {"key": "27", "value": "Others", "deprecated": false}, {"key": "28", "value": "Government (Others)", "deprecated": false}, {"key": "29", "value": "Government (Defense)", "deprecated": false}, {"key": "30", "value": "Entertainment (Others)", "deprecated": false}, {"key": "31", "value": "Entertainment (Casino, karaoke lounge, night clubs and bars)", "deprecated": false}, {"key": "32", "value": "Restaurant, Food and Beverages", "deprecated": false}]}}}, "texts": {"referralTextContent": "Hey, I use GXBank to help me save, spend and grow my money without any fees. Sign up now and earn daily interest on your savings account and pockets!\n\nJoin now with my Referral Code:\n{code}\n\n%s", "referralTextContent_ms": "<PERSON>, dengan GXBank memang senang nak urus dan kembangkan duit tanpa sebarang caj. <PERSON><PERSON> sekarang dan dapatkan faedah harian daripada akaun dan poket simpanan anda. Mudah sahaja!\n\nKod Referral saya:\n{code}\n\n%s", "retailLobbyScreenV2": {"title": {"en": "Unlock a better banking experience", "ms": "<PERSON>uka kunci pengalaman perbankan yang lebih baik"}, "list": [{"id": 0, "isAvailable": true, "key": "savings", "title": {"en": "GX Savings Account", "ms": "Akaun Simpanan GX"}, "subtitle": {"en": "Earn up to 2% p.a., with no lock-in.", "ms": "Dapatkan sehingga 2% p.a., tanpa <PERSON>h terikat."}, "info": {"en": "Insured up to RM250k by PIDM", "ms": "Diinsuranskan sehingga RM250k oleh PIDM"}}, {"id": 1, "isAvailable": true, "key": "flexiCredit", "title": {"en": "FlexiCredit", "ms": "KreditFleksi"}, "subtitle": {"en": "Borrow anytime with no fees.", "ms": "<PERSON>njam bila-bila masa tanpa yuran."}, "info": {"en": "Includes GX Savings Account setup for financial planning", "ms": "Termasuk persediaan Akaun Simpanan GX untuk perancangan kewangan"}}]}, "cvpScreen": {"title": {"en": "We're joining forces to grow your money", "ms": "<PERSON><PERSON><PERSON> untuk gandakan duit anda."}, "list": [{"id": 0, "title": {"en": "<PERSON><PERSON>n 1.5x GrabRewards points", "ms": "1.5x mata ganjaran GrabRewards"}, "subtitle": {"en": "When you pay for Grab services. T&Cs apply", "ms": "Pembayaran perkhidmatan Grab. Tertakluk T&S"}}]}, "downloadScreen": {"title": {"en": "Download GXBank app now and do more with your money!", "ms": "Muat turun aplikasi GXBank sekarang dan kembangkan duit anda"}}, "primaryPaymentScreen": {"title": {"en": "Earn 1.5x GrabRewards points when you pay with GX on Grab"}}, "bizSolePropDocuments": {"list": [{"subtitle": {"en": "Your MyKad", "ms": "<PERSON><PERSON><PERSON> anda"}}, {"subtitle": {"en": "Your Business Registration Number (BRN)", "ms": "Nombor Pendaftaran <PERSON>an (BRN) anda"}}]}, "bizSolePropCriteria": {"list": [{"subtitle": "texts.bizSolePropCriteria.list[0].subtitle"}, {"subtitle": "texts.bizSolePropCriteria.list[1].subtitle"}, {"subtitle": "texts.bizSolePropCriteria.list[2].subtitle"}, {"subtitle": "texts.bizSolePropCriteria.list[3].subtitle"}, {"subtitle": "texts.bizSolePropCriteria.list[4].subtitle"}]}, "bizSolePropTncLinks": {"list": [{"subtitle": "texts.bizSolePropTncLinks.list[0].subtitle"}, {"subtitle": "texts.bizSolePropTncLinks.list[1].subtitle"}, {"subtitle": "texts.bizSolePropTncLinks.list[2].subtitle"}, {"subtitle": "texts.bizSolePropTncLinks.list[3].subtitle"}]}, "bizFlexiLoanAndDepositAccount_offerScreen": {"title": {"en": "Get the best of both worlds for your business!", "ms": "Dapatkan yang terbaik dari kedua-dua dunia untuk perniagaan anda!"}, "list": [{"id": 0, "isAvailable": true, "key": "bizFlexiloan", "title": {"en": "Biz FlexiLoan", "ms": "<PERSON><PERSON><PERSON> Fle<PERSON>"}, "amount": "RM95,000.00 (testing, this is a hardcoded value)", "subtitle": {"en": "Borrow at 5% p.a. (10% p.a. EIR)", "ms": "Pinjaman pada kadar 5% p.a. (10% p.a. EIR)"}}, {"id": 1, "isAvailable": true, "key": "depositAccount", "title": {"en": "Deposit account", "ms": "Akaun deposit"}, "subtitle": {"en": "Earn 2.5% p.a. interest on savings that is added to your account daily", "ms": "Dapatkan 2.5% p.a. faedah pada simpanan yang ditambah ke akaun anda setiap hari"}}], "finePrint": {"en": "No interest is charged until you drawdown.", "ms": "Tiada faedah dikenakan se<PERSON>ga anda membuat pengeluaran."}}, "bizFlexiLoan_purposeOfLoan": {"title": {"en": "Purpose of loan", "ms": "lorem ipsum"}, "subtitle": {"en": "What are you using the funds for?", "ms": "lorem ipsum"}, "options": [{"key": 1, "title": {"en": "Operating expenditure", "ms": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "subtitle": {"en": "Day-to-day business expenses e.g. Salary, rent, utilities, inventory", "ms": "<PERSON><PERSON><PERSON><PERSON><PERSON> harian per<PERSON>an se<PERSON>i <PERSON>, se<PERSON>, util<PERSON>, inventori"}}, {"key": 2, "title": {"en": "Other expenditure", "ms": "<PERSON><PERSON><PERSON><PERSON><PERSON> lain"}, "subtitle": {"en": "Any other business-related expense", "ms": "<PERSON><PERSON><PERSON> perbelan<PERSON>an lain yang berkaitan dengan perniagaan"}}], "finePrint": {"en": "No interest is charged until you drawdown.", "ms": "Tiada faedah dikenakan se<PERSON>ga anda membuat pengeluaran."}}, "bizVerificationMethod": {"options": [{"method": "documentUpload", "isEnabled": true}, {"method": "premisVideo", "isEnabled": true}]}}, "featureFlags": {"napierAlwaysHappyPath": true, "useSyncCasaAccountCreationFlow": true, "disableEmbeddedFirstFundNotification": false, "fuzzyNamecheckAlwaysHappyPath": false, "fuzzyNamecheckForNADVerifiedAlwaysHappyPath": true, "ecddAlwaysHappyPath": false, "canSkipFirstFund": true, "canSkipMsmeFirstFund": true, "defaultNADStatus": "BANK_VERIFIED", "skipCreatingAppianCase": false, "skipFinIDCache": true, "disableAccountDeletion": false, "enableStandaloneCVPCampaign": true, "enableInternalNRICRiskCheck": true, "skipUpdateNapierStatusAfterOnboarding": true, "disableDailyBatchScreening": true, "enableCRAv2": true, "enableGetCASAAccount": true, "enableBizGetCASAAccount": false, "bypassMsmeLoanCreation": false, "ignoreMsmeLoanCreationErrors": false, "ignoreMsmeGetLoanOfferErrors": false, "enableRetailLendingInHouseWorkflow": true, "enableRetailLendingIEMFlow": true, "disableGrabmexPreSubmitCheck": false, "allowEtbSkipFirstFund": true, "hideReferralRewardsAmount": false, "enableUpdateEmailToIdExp": true, "enableProcessingSoftRejectFromLoanApp": false, "enableValidateCtosReportEligibility": true, "enableGetProfileIDFromHeader": false, "enableOpenBizWhitelistCheck": false, "enableRGONB1453": true, "enableServingContentWithPhrase": true, "enableEtbHoldCodeCheck": false, "forceNTBLendingWorkflow": true, "enableGetReferralCodeFromReferralEngine": true, "enableGetTotalRewardsFromOdyssey": true, "enableProcessReferralSourceFromReferralEngine": true}, "mockResponses": {"eir": 6.5}, "environment": "dev", "napierCredentials": {"username": "{{ napier_username }}", "password": "{{ napier_password }}"}, "partnerGrabIDCredentials": {"clientID": "{{ partner_grabid_client_id }}", "clientSecret": "{{ partner_grabid_client_secret }}", "partnerID": "8eff7445-b0c5-4c76-9435-901f5441f033"}, "napierWebhookCredentials": {"username": "{{ napier_webhook_username }}", "password": "{{ napier_webhook_password }}"}, "napierRPTMapping": {"Internal List - Internal List - Director Groups": "directorGroup", "Internal List - Internal List - Senior Management Groups": "seniorManagementGroup", "Internal List - Internal List - Key Credit Approver Groups": "keyCreditApproverGroup", "major stake entity group": "majorStakeEntityGroup", "substantial Shareholder Group": "substantialShareholderGroup", "Internal List - Internal List - Related Corporation Group": "relatedCorporationGroup", "other related party group": "otherRelatedPartyGroup"}, "redisConfig": {"addr": "clustercfg.dbmy-dev-backend-ec-cust-experience.o9f56r.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 3, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 3, "writeTimeoutInSec": 3, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}, "napierClient": {"serviceName": "napier", "baseURL": "https://stag.gxbank.napier.cloud", "circuitBreaker": {"timeout": 20000, "max_concurrent_requests": 50}}, "appianClient": {"serviceName": "<PERSON><PERSON>", "baseURL": "https://dbmydev.gforce.g-bank.app", "circuitBreaker": {"timeout": 20000, "max_concurrent_requests": 50}}, "whitelistService": {"serviceName": "whitelist-service", "baseURL": "http://whitelist-service.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 1000}}, "loanExpClient": {"serviceName": "loan-experience", "baseURL": "http://loan-exp.lending-platform.svc.cluster.local", "circuitBreaker": {"timeout": 1500}}, "loanAppClient": {"serviceName": "loan-app", "baseURL": "http://loan-app.lending-platform.svc.cluster.local", "circuitBreaker": {"timeout": 1500}}, "hermesClient": {"serviceName": "hermes", "baseURL": "http://hermes.hermes.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "paymentExperience": {"serviceName": "payment-experience", "baseURL": "http://payment-experience.payments.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "pairingService": {"serviceName": "pairing-service", "baseURL": "http://pairing-service.payments.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "identityExperience": {"serviceName": "id-exp", "baseURL": "http://id-exp.identity.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "customerExperience": {"serviceName": "customer-experience", "baseURL": "http://customer-experience.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 1000}}, "finIdClient": {"serviceName": "fin-id", "baseURL": "https://partner-api.stg-myteksi.com", "circuitBreaker": {"timeout": 11000}}, "appianCredentials": {"clientID": "{{ appian_client_id }}", "clientSecret": "{{ appian_client_secret }}"}, "appianRedisConfig": {"key": "APPIAN_TOKEN_KEY", "lock": "APPIAN_TOKEN_LOCK"}, "riskMapping": {"True Match <Material Sanctions Risk Event>": "HIGH", "True Match <Material PEP Risk Event>": "HIGH", "True Match <Material Adverse Media Risk Event>": "HIGH", "True Match <Non-material Sanctions Risk Event>": "HIGH", "True Match <Non-material PEP Risk Event>": "HIGH", "True Match <Non-material Adverse Media Risk Event>": "MEDIUM", "True Match <Sanctions Risk Event>": "HIGH", "True Match <PEP Risk Event>": "HIGH", "True Match <Adverse Media Risk Event>": "HIGH"}, "ecddEmailList": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "unidentifiedCountries": {"XF": true, "XG": true, "XI": true, "XH": true, "XJ": true, "XD": true, "XX": true, "XB": true, "XC": true}, "napierRedisConfig": {"key": "NAPIER_TOKEN_KEY", "lock": "NAPIER_TOKEN_LOCK"}, "napierOnboardingSource": ["grab-api grab-api", "gxbank-api api", "Napier System Account"], "workFlowEngineConfig": {"retryEKYC": {"interval": 90, "attempts": 100}, "maxRetryConfig": {"interval": 3600}, "bizStandardRetryConfig": {"interval": 7200, "attempts": 100}}, "exponentialBackoffConfig": {"napierMaxElapsedTime": 30, "napierInitialInterval": 3, "appianMaxElapsedTime": 30, "appianInitialInterval": 3}, "userGeneratedContentConfig": {"underageApplicant": {"title": {"en": "Thank you for your application", "ms": "<PERSON><PERSON> kasih atas permohonan anda"}, "description": {"en": "Unfortunately, we're unable to process applications for Malaysians who are under 18 years old.", "ms": "<PERSON><PERSON>, kami tidak dapat memproses permohonan untuk rakyat Malaysia yang berumur di bawah 18 tahun."}, "allowedActions": [{"key": "faq", "emphasis": "secondary", "title": {"en": "FAQ", "ms": "Soalan Lazim"}, "articlePath": "/article/325"}]}, "ongoingApplication": {"title": {"en": "You have an existing application", "ms": "<PERSON>a telah membuat permohonan"}, "description": {"en": "Continue your application using your mobile number {PHONE_NUMBER} or check our FAQ if you suspect your MyKad has been used to create an account.", "ms": "Teruskan permohonan anda dengan nombor telefon {PHONE_NUMBER} atau semak Soalan Lazim kami jika anda mengesyaki <PERSON> anda telah digunakan untuk membuka akaun."}, "fallbackDescription": {"en": "Continue your application using your mobile number or check our FAQ if you suspect your MyKad has been used to create an account.", "ms": "Teruskan permohonan dengan nombor telefon anda atau semak Soalan Lazim kami jika anda menges<PERSON>ki <PERSON> anda telah digunakan untuk membuka akaun."}, "allowedActions": [{"key": "login", "emphasis": "primary", "title": {"en": "Continue", "ms": "Teruskan"}}, {"key": "faq", "emphasis": "secondary", "title": {"en": "FAQ", "ms": "Soalan Lazim"}, "articlePath": "/article/783"}]}, "duplicateSuccessfulApplication": {"title": {"en": "You already have an account", "ms": "Anda sudah mempunyai akaun"}, "description": {"en": "Log in with your mobile number {PHONE_NUMBER} or check our FAQ if you suspect your MyKad has been used to create an account.", "ms": "Log masuk dengan nombor telefon {PHONE_NUMBER} atau semak Soalan Lazim kami jika anda menges<PERSON>ki <PERSON> anda telah digunakan untuk membuka akaun."}, "fallbackDescription": {"en": "Log in with your mobile number. Visit our FAQ if you suspect someone else is using your MyKad number.", "ms": "Log masuk dengan nombor telefon bimbit anda. Lawati soalan lazim kami jika anda mengesyaki ada orang lain menggunakan Nombor MyKad anda."}, "allowedActions": [{"key": "login", "emphasis": "primary", "title": {"en": "<PERSON><PERSON>", "ms": "Log masuk"}}, {"key": "faq", "emphasis": "secondary", "title": {"en": "FAQ", "ms": "Soalan Lazim"}, "articlePath": "/article/783"}]}, "rejectedApplication": {"title": {"en": "We're unable to create an account with your MyKad number", "ms": "<PERSON><PERSON> tidak dapat membuka akaun dengan nombor MyKad anda"}, "description": {"en": "It seems that your previous application using this ID number was not approved. Check our FAQ if you suspect your MyKad has been used to create an account.", "ms": "Nampaknya permohonan anda sebelum ini menggunakan nombor ID ini tidak diluluskan. Semak Soalan Lazim kami jika anda mengesyaki <PERSON> anda telah digunakan untuk membuka akaun."}, "allowedActions": [{"key": "faq", "emphasis": "secondary", "title": {"en": "FAQ", "ms": "Soalan Lazim"}, "articlePath": "/article/783"}]}, "underageApplicant_biz": {"title": {"en": "Thank you for your application", "ms": "<PERSON><PERSON> kasih atas permohonan anda"}, "description": {"en": "Unfortunately, we're unable to process applications for Malaysians who are under 18 years old.", "ms": "<PERSON><PERSON>, kami tidak dapat memproses permohonan untuk rakyat Malaysia yang berumur di bawah 18 tahun."}, "allowedActions": [{"key": "faq", "emphasis": "secondary", "title": {"en": "FAQ", "ms": "Soalan Lazim"}, "articlePath": "/article/2696"}]}, "ongoingApplication_biz": {"title": {"en": "You have an existing application", "ms": "<PERSON>a telah membuat permohonan"}, "description": {"en": "Continue your application using your mobile number {PHONE_NUMBER} or check our FAQ if you suspect your MyKad has been used to create an account.", "ms": "Teruskan permohonan anda dengan nombor telefon {PHONE_NUMBER} atau semak Soalan Lazim kami jika anda mengesyaki <PERSON> anda telah digunakan untuk membuka akaun."}, "fallbackDescription": {"en": "Continue your application using your mobile number or check our FAQ if you suspect your MyKad has been used to create an account.", "ms": "Teruskan permohonan dengan nombor telefon anda atau semak Soalan Lazim kami jika anda menges<PERSON>ki <PERSON> anda telah digunakan untuk membuka akaun."}, "allowedActions": [{"key": "login", "emphasis": "primary", "title": {"en": "Continue", "ms": "Teruskan"}}, {"key": "faq", "emphasis": "secondary", "title": {"en": "FAQ", "ms": "Soalan Lazim"}, "articlePath": "/article/783"}]}, "duplicateSuccessfulApplication_biz": {"title": {"en": "You already have an account", "ms": "Anda sudah mempunyai akaun"}, "description": {"en": "Log in with your mobile number {PHONE_NUMBER} or check our FAQ if you suspect your MyKad has been used to create an account.", "ms": "Log masuk dengan nombor telefon {PHONE_NUMBER} atau semak Soalan Lazim kami jika anda menges<PERSON>ki <PERSON> anda telah digunakan untuk membuka akaun."}, "fallbackDescription": {"en": "Log in with your mobile number. Visit our FAQ if you suspect someone else is using your MyKad number.", "ms": "Log masuk dengan nombor telefon bimbit anda. Lawati soalan lazim kami jika anda mengesyaki ada orang lain menggunakan Nombor MyKad anda."}, "allowedActions": [{"key": "login", "emphasis": "primary", "title": {"en": "<PERSON><PERSON>", "ms": "Log masuk"}}, {"key": "faq", "emphasis": "secondary", "title": {"en": "FAQ", "ms": "Soalan Lazim"}, "articlePath": "/article/2503"}]}, "rejectedApplication_biz": {"title": {"en": "We're unable to create an account with your MyKad number", "ms": "<PERSON><PERSON> tidak dapat membuka akaun dengan nombor MyKad anda"}, "description": {"en": "It seems that your previous application using this ID number was not approved. Check our FAQ if you suspect your MyKad has been used to create an account.", "ms": "Nampaknya permohonan anda sebelum ini menggunakan nombor ID ini tidak diluluskan. Semak Soalan Lazim kami jika anda mengesyaki <PERSON> anda telah digunakan untuk membuka akaun."}, "allowedActions": [{"key": "faq", "emphasis": "secondary", "title": {"en": "FAQ", "ms": "Soalan Lazim"}, "articlePath": "/article/2503"}]}, "bankStatementRejected_bizOnboarding": {"title": {"en": "Your bank statement didn't meet the required criteria. Please reverify your business again.", "ms": "Penyata bank anda tidak memenuhi kriteria yang ditetapkan. Sila sahkan semula perniagaan anda."}, "description": {"en": "<h4>Reason(s)</h4>   %s", "ms": "<h4>Sebab-sebab</h4> %s"}, "allowedActions": [{"key": "verify", "emphasis": "primary", "title": {"en": "Verify Again", "ms": "Cuba Lagi"}}], "dynamicReasons": {"INSUFFICIENT_TRANSACTIONS": {"en": "• Transaction count or value is below the required threshold <br/>", "ms": "• Nama perniagaan pada penyata bank tidak sepadan dengan nama profil Akaun GX Biz ini <br/>"}, "ACCOUNT_NAME_MISMATCH": {"en": "• The business name on the bank statement doesn't match this GX Biz Account's profile name <br/>", "ms": "• Nama perniagaan pada penyata bank tidak sepadan dengan nama profil Akaun GX Biz ini. <br/>"}}}, "liveVideoRejected_bizOnboarding": {}}, "hedwigNotificationTemplateIds": {"accountRejectedPushTemplateID": "e47ea411-3aba-475e-bc5c-18c73d52f5fb", "accountApprovedPushTemplateID": "be901de3-b74e-42a3-bb93-341989c70935", "applicationRejectedPushTemplateID": "1944a98c-7b0e-41f8-a3b5-838c014fb5b6", "firstFundReminderPushTemplateID": "08f951f2-8c9e-4788-9759-b374953418d0"}, "bizNotificationTemplateIds": {"bizApplicationApprovedPushTemplateID": "business_account_approved", "bizApplicationFirstFundReminderTemplateID": "business_first_fund_reminder", "bizApplicationEkycEkybRetryPushTemplateID": "business_ekyc_rejected", "bizApplicationRejectedPushTemplateID": "business_application_rejected", "bizApplicationClosedEmailTemplateID": "business_account_closed_email", "bizApplicationLoanRejectedPushTemplateID": "business_los_rejected", "bizApplicationLoanApprovedPushTemplateID": "business_los_accepted", "bizApplicationVerificationFailedPushTemplateID": "business_verification_unsuccessful", "bizApplicationVerificationFailedEmailTemplateID": "business_verification_unsuccessful_email"}, "workers": {"notificationWorker": {"delayInSecs": 180, "startImmediately": false, "logTag": "scheduledNotificationQueueWorkerTag", "name": "scheduledNotificationQueueWorker", "lockKey": "scheduledNotificationQueueWorkerLock", "lockDurationInSecs": 300}, "closeAccountWorker": {"delayInSecs": 600, "startImmediately": true, "logTag": "tempCloseAccountWorkerTag", "name": "tempCloseAccountWorker", "lockKey": "tempCloseAccountWorkerLock", "lockDurationInSecs": 1200}}, "notificationsConfig": [{"name": "FirstFundReminder", "window": {"atLeast": "1d", "atMost": "3d"}}], "closeAccountConfig": {"enabled": false, "accounts": [{"bif": "", "customerID": "", "applicationID": ""}]}, "cvpScreensConfig": {"casa": {"durationInSecs": 5, "screens": [{"id": 1, "title": {"en": "Discover the bank that lives in your pocket", "ms": "Terokai bank di dalam poket anda"}}, {"id": 2, "title": {"en": "Supercharge your savings with 2% p.a. daily interest", "ms": "Tingkatkan simpanan anda dengan 2% p.a. faedah harian"}, "subtitle": {"en": "No deposit limit, no lock-in period, and your account is protected by PIDM up to RM250,000 for each depositor. Ready to save? ", "ms": "Tiada had deposit, tiada tempoh diku<PERSON>, dan akaun anda dilindungi oleh PIDM sehingga RM250,000 untuk setiap pendeposit. Sedia untuk menyimpan?"}, "animation": "casa_interest_rate"}]}, "signup-login-default": {"durationInSecs": 5, "screens": [{"id": 1, "title": {"en": "Discover the bank that lives in your pocket", "ms": "Terokai bank di dalam poket anda"}}, {"id": 2, "title": {"en": "Supercharge your savings with 2% p.a. daily interest", "ms": "Tingkatkan simpanan anda dengan 2% p.a. faedah harian"}, "subtitle": {"en": "No deposit limit, no lock-in period, and your account is protected by PIDM up to RM250,000 for each depositor. Ready to save? ", "ms": "Tiada had deposit, tiada tempoh diku<PERSON>, dan akaun anda dilindungi oleh PIDM sehingga RM250,000 untuk setiap pendeposit. Sedia untuk menyimpan?"}, "animation": "casa_interest_rate"}]}, "signup-login-campaign": {"durationInSecs": 5, "screens": [{"id": 1, "title": {"en": "Discover the bank that lives in your pocket", "ms": "Terokai bank di dalam poket anda"}}, {"id": 2, "title": {"en": "Supercharge your savings with 2% p.a. daily interest", "ms": "Tingkatkan simpanan anda dengan 2% p.a. faedah harian"}, "subtitle": {"en": "No deposit limit, no lock-in period, and your account is protected by PIDM up to RM250,000 for each depositor. Ready to save? ", "ms": "Tiada had deposit, tiada tempoh diku<PERSON>, dan akaun anda dilindungi oleh PIDM sehingga RM250,000 untuk setiap pendeposit. Sedia untuk menyimpan?"}, "animation": "casa_interest_rate"}, {"id": 3, "title": {"en": "No branches. No queues.", "ms": "Tak perlu beratur"}, "subtitle": {"en": "All your money things, right in your pocket. We’ve made banking easier, so you can stay in the moment.", "ms": "<PERSON><PERSON><PERSON> ca<PERSON>an. <PERSON><PERSON><PERSON> kew<PERSON>n anda, hanya di dalam poket anda. <PERSON>mi telah jadikan perbankan lebih mudah!"}, "animation": "casa_fund_in"}]}, "signup-login-ntb-lending": {"durationInSecs": 5, "screens": [{"id": 1, "title": {"en": "Discover the bank that lives in your pocket"}}, {"id": 2, "title": {"en": "Credit approval in minutes, cash instantly"}, "subtitle": {"en": "Like a personal loan, but way faster!"}, "animation": "casa_flexiloan"}, {"id": 3, "title": {"en": "Earn 2% p.a. daily interest on your savings"}, "subtitle": {"en": "No deposit limit, no lock-in period, your money will thank you."}, "animation": "casa_interest_rate"}, {"id": 4, "title": {"en": "No branches. No queues"}, "subtitle": {"en": "All your money moves, now at your fingertips."}, "animation": "casa_fund_in"}]}, "onboarding-fund-in-default": {"screens": [{"id": 1, "title": {"en": "Yay, your account is ready.<br/>Add money to your account now!", "ms": "<PERSON><PERSON><PERSON>, akaun anda sedia diguna.<br/><PERSON><PERSON> duit ke dalam akaun sekarang"}, "animation": "casa_fund_in"}]}, "onboarding-fund-in-campaign": {"screens": [{"id": 1, "title": {"en": "Yay, your account is ready.<br/>Add money to <font color='#75F9AA'>activate</font> now!", "ms": "<PERSON><PERSON>, akaun anda sudah dibuka. <br/> Tambah duit untuk <font color='#75F9AA'>aktifkan</font> sekarang!"}, "animation": "casa_fund_in"}]}, "onboarding-referral-intro": {"screens": [{"id": 1, "title": {"en": "Spread the love, spread the word. Refer a friend!", "ms": "<PERSON>a baik kena kongsi. <PERSON><PERSON> ajak rakan join!"}, "subtitle": {"en": "Invite your friends to join GXBank now and help them grow their money with us.", "ms": "<PERSON>an lepaskan peluang untuk bantu rakan anda gandakan duit mereka bersama GXBank."}}, {"id": 2, "title": {"en": "Share your link to your friends", "ms": "Kongsi link kepada rakan anda"}, "subtitle": {"en": "Share your unique link via your preferred channel", "ms": "Kongsi link unik anda melalui saluran pilihan anda"}}, {"id": 3, "title": {"en": "Cheer on every friend who joined", "ms": "<PERSON><PERSON><PERSON> rakan anda"}, "subtitle": {"en": "You helped someone take a step closer to their financial goals and dreams", "ms": "Anda member<PERSON>n peluang kepada kawan anda untuk mencapai kebebasan kewangan!"}}]}, "biz-fresh-entry": {"durationInSecs": 5, "screens": [{"id": 1, "title": {"en": "Simplified financing and flexible repayments, just for your business", "ms": "<PERSON><PERSON><PERSON><PERSON><PERSON> mudah dan pembayaran balik flek<PERSON>bel, khusus untuk per<PERSON>an anda"}, "animation": "biz_repayment"}, {"id": 2, "title": {"en": "Get interest paid to your business every single day", "ms": "Bayaran faedah kepada pernia<PERSON>an anda setiap hari"}, "animation": "biz_daily_interest"}]}, "personal-biz-selection": {"screens": [{"id": 1, "type": "header", "title": {"en": "Welcome!\nSelect an account type to begin.", "ms": "Selamat datang!\nPilih akaun untuk dibuka."}, "subtitle": {"en": "You can apply for only one account, either Personal or Business.", "ms": "Mari terokai akaun perbankan peribadi dan perbankan bisnes kami."}}, {"id": 2, "type": "personalBanking", "title": {"en": "Personal", "ms": "Peribadi"}, "subtitle": {"en": "For individuals and personal finances", "ms": "Untuk individu dan kewangan peribadi"}, "description": {"en": "For individuals and personal finances", "ms": "Untuk individu dan kewangan peribadi"}}, {"id": 3, "type": "businessBanking", "title": {"en": "Business", "ms": "Bisnes"}, "subtitle": {"en": "For SSM-registered sole proprietorships", "ms": "Untuk perniagaan milikan tunggal yang didaftarkan dengan SSM"}, "description": {"en": "For SSM-registered sole proprietorships", "ms": "Untuk perniagaan milikan tunggal yang didaftarkan dengan SSM"}}]}, "personal-biz-selection-etb": {"screens": [{"id": 1, "type": "header", "title": {"en": "Welcome!\nSelect an account type to begin.", "ms": "Selamat datang!\nPilih akaun untuk dibuka."}, "subtitle": {"en": "You can apply for only one account, either Personal or Business.", "ms": "Mari terokai akaun perbankan peribadi dan perbankan bisnes kami."}}, {"id": 2, "type": "personalBanking", "title": {"en": "Personal", "ms": "Peribadi"}, "subtitle": {"en": "For individuals and personal finances", "ms": "Untuk individu dan kewangan peribadi"}, "description": {"en": "For individuals and personal finances", "ms": "Untuk individu dan kewangan peribadi"}}, {"id": 3, "type": "businessBanking", "title": {"en": "Business", "ms": "Bisnes"}, "subtitle": {"en": "For SSM-registered sole proprietorships", "ms": "Untuk perniagaan milikan tunggal yang didaftarkan dengan SSM"}, "description": {"en": "For SSM-registered sole proprietorships", "ms": "Untuk perniagaan milikan tunggal yang didaftarkan dengan SSM"}}, {"id": 4, "title": {"en": "You can only have one Personal account. Business users, stay tuned—Personal accounts will be available soon.", "ms": "<PERSON>a hanya boleh mempunyai satu akaun Peribadi. <PERSON><PERSON><PERSON>, nantikan—<PERSON><PERSON><PERSON> peribadi akan tersedia tidak lama lagi."}, "type": "infoFooter"}]}, "personal-biz-selection-v2": {"screens": [{"id": 1, "type": "header", "title": {"en": "Welcome!\nSelect an account type to begin.", "ms": "Selamat datang!\nPilih akaun untuk dibuka.?"}, "subtitle": {"en": "You can apply for only one account, either Personal or Business", "ms": "Mari terokai akaun perbankan peribadi dan perbankan bisnes kami."}}, {"id": 2, "type": "personalBanking", "title": {"en": "<p style='font-size: 16px;'>Personal</p>", "ms": "<p style='font-size: 16px;'>Peribadi</p>"}, "subtitle": {"en": "For individuals and personal finances", "ms": "Untuk individu dan kewangan peribadi"}}, {"id": 3, "type": "businessBanking", "title": {"en": "<p style='font-size: 16px;'>Business</p>", "ms": "<p style='font-size: 16px;'>Bisnes</p>"}, "subtitle": {"en": "For SSM-registered sole proprietorships", "ms": "Untuk perniagaan milikan tunggal yang didaftarkan dengan SSM"}}]}, "ntb-lending-oa-offered-only": {"screens": [{"id": 1, "type": "savingsHeader", "title": {"en": "Start with GX Savings Account", "ms": "<PERSON><PERSON> dengang A<PERSON>un Simpanan GX"}, "subtitle": {"en": "FlexiCredit is not available for you right now. Kick things off with a GX Savings Account instead!", "ms": "FlexiCredit tidak tersedia untuk anda sekarang. <PERSON><PERSON><PERSON> dengan <PERSON>un Simpanan GX dahulu!"}}, {"id": 2, "type": "savingsContent", "title": {"en": "GX Savings Account", "ms": "Akaun Simpanan GX"}, "subtitle": {"en": "Earn 2% p.a. daily interest on balances in your account", "ms": "Dapatkan 2% p.a. faedah harian pada baki di akaun anda"}}]}, "ntb-lending-oa-offered-only-with-cooldown-period": {"screens": [{"id": 1, "type": "savingsHeader", "title": {"en": "Start with GX Savings Account", "ms": "<PERSON><PERSON> dengang A<PERSON>un Simpanan GX"}, "subtitle": {"en": "FlexiCredit is not available for you right now. Kick things off with a GX Savings Account and try again on %s", "ms": "FlexiCredit tidak tersedia untuk anda sekarang. <PERSON><PERSON><PERSON> dengan <PERSON> Simpanan GX dahulu dan cuba lagi pada %s"}}, {"id": 2, "type": "savingsContent", "title": {"en": "GX Savings Account"}, "subtitle": {"en": "Earn 2% p.a. daily interest on balances in your account", "ms": "Dapatkan 2% p.a. faedah harian pada baki di akaun anda"}}]}, "ntb-lending-both-offered": {"screens": [{"id": 1, "type": "flexiCreditHeader", "title": {"en": "Get more with GX Savings Account and FlexiCredit together", "ms": "<PERSON><PERSON><PERSON> berbaloi dengan Akaun Simpanan GX dan FlexiCredit"}, "subtitle": {"en": "Enjoy at no cost until you borrow", "ms": "<PERSON><PERSON><PERSON> tanpa caj sehingga anda pinjam"}}, {"id": 2, "type": "flexiCreditContent", "title": {"en": "FlexiCredit"}, "subtitle": {"en": "Interest rates from 3.78% p.a. (EIR 6.45% p.a.)", "ms": "Faedah daripada 3.78% p.a. (EIR 6.45% p.a.)"}, "multiSubtitle": {"en": ["Up to RM150,000 available credit", "Flexible borrowing, anytime", "Zero early settlement fees"], "ms": ["Kredit sehingga RM150,000", "<PERSON><PERSON><PERSON> fleksibel, bila-bila sahaja", "Tiada caj penye<PERSON><PERSON>n awal"]}}]}}, "enhancedNameCheckConfig": {"nadVerifiedScoreThreshold": 65, "scoreThreshold": 75, "truncationThreshold": 20, "titles": ["tan sri", "dato seri", "puan seri", "datin seri", "toh puan", "dr", "dato", "datin", "datuk", "mr", "mrs", "ms", "miss", "encik", "puan", "madam", "mdm", "cik", "en", "tuan", "puan"], "specificTitles": ["wan", "nik", "che", "tun", "<PERSON><PERSON><PERSON>", "tunku", "tengku", "raja", "abang", "awang", "<PERSON><PERSON>", "megat", "baba", "haji", "hajjah"], "maleNameComponents": ["bin", "b", "al", "so"], "femaleNameComponents": ["binti", "bt", "ap", "do"], "commonNames": [["muh<PERSON><PERSON>", "moham<PERSON>", "mohammed", "<PERSON>uh<PERSON><PERSON>", "moh<PERSON><PERSON>", "mohamed"], ["nur", "nurul", "noor", "nor"], ["<PERSON>mad", "ahmed"], ["abdul", "<PERSON><PERSON><PERSON><PERSON>", "a<PERSON><PERSON><PERSON>"]], "commonSurnames": ["ang", "aw", "boon", "chai", "chan", "chang", "chee", "chen", "cheng", "cheong", "chew", "chia", "chiang", "chin", "ching", "chng", "chong", "choo", "chow", "chua", "chung", "ee", "fong", "foo", "gan", "goh", "han", "heng", "ho", "hong", "hu", "huang", "hui", "kang", "kee", "khoo", "kim", "koh", "kok", "kong", "kwan", "kwek", "kwok", "lai", "lam", "lau", "law", "lee", "leong", "leow", "li", "liang", "liew", "lim", "lin", "ling", "liu", "loh", "loke", "loo", "low", "lu", "lum", "mok", "neo", "ng", "oh", "ong", "ooh", "ooi", "pang", "peh", "peng", "phua", "png", "poh", "poon", "quek", "seah", "see", "seet", "seng", "seow", "sim", "soh", "song", "soo", "soon", "su", "sun", "tan", "tang", "tay", "tee", "teh", "teng", "teo", "tham", "ting", "toh", "tong", "wan", "wang", "wee", "wei", "wong", "woo", "wu", "xu", "yan", "yang", "yap", "yee", "yeo", "yew", "yip", "yong", "yu", "zhang", "<PERSON><PERSON>", "zheng", "zhou"], "vowels": "<PERSON><PERSON><PERSON><PERSON>", "metricWeights": {"ratio": 0.15, "partial_ratio": 0.12, "token_sort_ratio": 0.18, "token_set_ratio": 0.36, "jaro_winkler_ratio": 0.1, "metaphone_ratio": 0.06, "name_length_ratio": 0.02}, "nameLengthRatios": {"num_words_ratio": 0.25, "num_chars_ratio": 0.25, "num_syll_ratio": 0.25, "word_set_ratio": 0.1, "char_set_ratio": 0.15}, "penaltyWeights": {"gender": 0.75, "commonName": 0.8, "commonSurname": 0.85, "nameOrder": 0.75}}, "lendingConfig": {"maximumFlexiCreditLimit": 15000000, "defaultRequestedLoanAmount": 150000, "failedApplicationNotificationTemplates": {"email": "lending_application_unexpected_error_email", "push_notification": "lending_application_unexpected_error_push"}, "defaultMonthlyIncome": "01", "workFlowEngine": {"retryWithFallbackConfig": {"interval": 3600, "attempts": 24}}, "cdeEmploymentMapping": {"B52": {"employmentType": "SA", "occupationCode": "4"}, "B43": {"employmentType": "SA", "occupationCode": "4"}, "B54": {"employmentType": "SA", "occupationCode": "4"}, "B60": {"employmentType": "SA", "occupationCode": "4"}, "B42": {"employmentType": "SA", "occupationCode": "4"}, "B62": {"employmentType": "SA", "occupationCode": "4"}, "B04": {"employmentType": "SA", "occupationCode": "4"}, "B73": {"employmentType": "SE", "occupationCode": "1"}, "B53": {"employmentType": "SA", "occupationCode": "4"}, "B63": {"employmentType": "SA", "occupationCode": "4"}, "B16": {"employmentType": "SA", "occupationCode": "4"}, "B55": {"employmentType": "SA", "occupationCode": "4"}, "B03": {"employmentType": "SA", "occupationCode": "4"}, "B07": {"employmentType": "SA", "occupationCode": "4"}, "B34": {"employmentType": "SA", "occupationCode": "4"}, "B51": {"employmentType": "SA", "occupationCode": "4"}, "B27": {"employmentType": "SA", "occupationCode": "4"}, "B25": {"employmentType": "SA", "occupationCode": "4"}, "B24": {"employmentType": "SA", "occupationCode": "4"}, "B36": {"employmentType": "SA", "occupationCode": "4"}, "B20": {"employmentType": "SA", "occupationCode": "4"}, "B19": {"employmentType": "SA", "occupationCode": "4"}, "B09": {"employmentType": "SA", "occupationCode": "4"}, "B70": {"employmentType": "GIG", "occupationCode": "1"}, "B08": {"employmentType": "SA", "occupationCode": "4"}, "B10": {"employmentType": "SA", "occupationCode": "4"}, "B71": {"employmentType": "SE", "occupationCode": "3"}, "B35": {"employmentType": "SA", "occupationCode": "4"}, "B12": {"employmentType": "SA", "occupationCode": "4"}, "B01": {"employmentType": "SA", "occupationCode": "4"}, "B02": {"employmentType": "SA", "occupationCode": "4"}, "B18": {"employmentType": "SA", "occupationCode": "4"}, "B45": {"employmentType": "SA", "occupationCode": "4"}, "B26": {"employmentType": "SA", "occupationCode": "4"}, "B28": {"employmentType": "SA", "occupationCode": "4"}, "B23": {"employmentType": "SA", "occupationCode": "4"}, "B14": {"employmentType": "SA", "occupationCode": "4"}, "B06": {"employmentType": "SA", "occupationCode": "4"}, "B05": {"employmentType": "SA", "occupationCode": "4"}, "B39": {"employmentType": "SA", "occupationCode": "4"}, "B11": {"employmentType": "SA", "occupationCode": "4"}, "B31": {"employmentType": "SA", "occupationCode": "4"}, "B13": {"employmentType": "SA", "occupationCode": "4"}, "B57": {"employmentType": "SA", "occupationCode": "4"}, "B49": {"employmentType": "SA", "occupationCode": "4"}, "B40": {"employmentType": "SA", "occupationCode": "4"}, "B37": {"employmentType": "SA", "occupationCode": "4"}, "B30": {"employmentType": "SA", "occupationCode": "4"}, "B47": {"employmentType": "SA", "occupationCode": "4"}, "B50": {"employmentType": "SA", "occupationCode": "4"}, "B61": {"employmentType": "SA", "occupationCode": "4"}, "B74": {"employmentType": "SA", "occupationCode": "4"}, "A01": {"employmentType": "SA", "occupationCode": "4"}, "A02": {"employmentType": "SA", "occupationCode": "4"}, "A03": {"employmentType": "SA", "occupationCode": "4"}, "A04": {"employmentType": "SA", "occupationCode": "4"}, "A05": {"employmentType": "SA", "occupationCode": "4"}, "A08": {"employmentType": "SA", "occupationCode": "4"}, "A07": {"employmentType": "SA", "occupationCode": "4"}, "A06": {"employmentType": "SA", "occupationCode": "4"}, "A09": {"employmentType": "SA", "occupationCode": "4"}, "A10": {"employmentType": "SA", "occupationCode": "4"}, "A11": {"employmentType": "SA", "occupationCode": "4"}, "B15": {"employmentType": "SA", "occupationCode": "4"}, "B32": {"employmentType": "SA", "occupationCode": "4"}, "B64": {"employmentType": "SA", "occupationCode": "4"}, "B65": {"employmentType": "SA", "occupationCode": "4"}, "B66": {"employmentType": "SA", "occupationCode": "4"}, "B67": {"employmentType": "SA", "occupationCode": "4"}, "B68": {"employmentType": "SA", "occupationCode": "4"}, "B69": {"employmentType": "SA", "occupationCode": "4"}, "B72": {"employmentType": "GIG", "occupationCode": "2"}}}, "msicCodeToIndustryMapping": {"01": ["1111", "1112", "1113", "1119", "1120", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1140", "1150", "1160", "1191", "1192", "1193", "1199", "1210", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1231", "1232", "1233", "1239", "1241", "1249", "1251", "1252", "1253", "1259", "1263", "1269", "1271", "1272", "1273", "1279", "1281", "1282", "1283", "1284", "1285", "1289", "1291", "1292", "1293", "1294", "1295", "1296", "1299", "1301", "1302", "1303", "1304", "1411", "1412", "1413", "1420", "1430", "1441", "1442", "1443", "1450", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1499", "1500", "1610", "1620", "1631", "1632", "1633", "1634", "1640", "1701", "1702", "2301", "2302", "2303", "2309", "3111", "3112", "3113", "3114", "3115", "3119", "3121", "3122", "3123", "3124", "3129", "3211", "3213", "3214", "3215", "3216", "3217", "3218", "3219", "3221", "3222", "3223", "3224", "3225", "3229"], "02": ["1261", "1262"], "03": ["2101", "2102", "2103", "2104", "2105", "2201", "2202", "2203", "2204", "2401", "2402"], "04": ["3212"], "05": ["5100", "5200", "6101", "6102", "6103", "6104", "6201", "6202", "6203", "6204", "6205", "7101", "7102", "7291", "7292", "7293", "7294", "7295", "7296", "7297", "7298", "7299", "8101", "8102", "8103", "8104", "8105", "8106", "8107", "8108", "8109", "8911", "8912", "8913", "8914", "8915", "8916", "8917", "8918", "8921", "8922", "8923", "8931", "8932", "8933", "8991", "8992", "8993", "8994", "8995", "8996", "8999", "9101", "9102", "9900"], "06": ["7210"], "07": ["24103", "24202", "24209", "24311", "24312", "24320", "25111", "25112", "25113", "25119", "25120", "25130", "25910", "25920", "25930", "25991", "25992", "25993", "25994", "25999", "26101", "26102", "26103", "26104", "26105", "26109", "26201", "26202", "26300", "26400", "26511", "26512", "26600", "26701", "26702", "26800", "27101", "27102", "27200", "27310", "27320", "27330", "27400", "27500", "27900", "28110", "28120", "28130", "28140", "28150", "28160", "28170", "28180", "28191", "28192", "28199", "28210", "28220", "28230", "28240", "28250", "28260", "28290", "29101", "29102", "29200", "29300", "30110", "30120", "30200", "30300", "30910", "30920", "30990", "31001", "31002", "31003", "31009", "32200", "32300", "32400", "32500", "32901", "32909", "10101", "10102", "10103", "10104", "10109", "10201", "10202", "10203", "10204", "10205", "10301", "10302", "10303", "10304", "10305", "10306", "10404", "10405", "10406", "10407", "10501", "10502", "10509", "10611", "10612", "10613", "10619", "10621", "10622", "10623", "10711", "10712", "10713", "10714", "10721", "10722", "10731", "10732", "10733", "10741", "10742", "10750", "10791", "10792", "10793", "10794", "10795", "10799", "10800", "11041", "11042", "13110", "13120", "13131", "13132", "13139", "13910", "13921", "13922", "13930", "13940", "13990", "14101", "14102", "14103", "14109", "14200", "14300", "15110", "15120", "15201", "15202", "15203", "15209", "16100", "16211", "16212", "16221", "16222", "16230", "16292", "17010", "17020", "17091", "17092", "17093", "17094", "17099", "18110", "18120", "18200", "19201", "19202", "20111", "20113", "20119", "20121", "20129", "20131", "20132", "20133", "20210", "20221", "20222", "20231", "20232", "20291", "20292", "20299", "20300", "21001", "21002", "21003", "21004", "21005", "21006", "21007", "21009", "22111", "22112", "22191", "22192", "22193", "22199", "22201", "22202", "22203", "22204", "22205", "22209", "23101", "23102", "23109", "23911", "23912", "23921", "23929", "23930", "23941", "23942", "23951", "23952", "23953", "23959", "23960", "23990", "24101"], "08": ["10401", "10402", "10403"], "09": ["11010", "11020", "11030", "12000"], "10": ["16291"], "11": ["19100"], "12": ["20112"], "13": ["24102", "24104", "24109"], "14": ["24201"], "15": ["25200"], "17": ["30400"], "18": ["32110", "32120"], "19": ["33110", "33120", "33131", "33132", "33133", "33140", "33150", "33190", "33200"], "20": ["35101", "35102", "35201", "35202", "35203", "35301", "35302", "35303"], "21": ["36001", "36002", "37000", "38111", "38112", "38113", "38114", "38115", "38121", "38122", "38210", "38220", "38301", "38302", "38303", "38304", "38309", "39000"], "22": ["41001", "41002", "41003", "41009", "42101", "42103", "42104", "42105", "42106", "42109", "42201", "42202", "42203", "42204", "42205", "42206", "42207", "42209", "42901", "42902", "42903", "42904", "42905", "42906", "42909", "43110", "43121", "43122", "43123", "43124", "43125", "43126", "43129", "43211", "43212", "43213", "43214", "43215", "43216", "43219", "43221", "43222", "43223", "43224", "43225", "43226", "43227", "43228", "43229", "43291", "43292", "43293", "43294", "43295", "43299", "43301", "43302", "43303", "43304", "43305", "43306", "43307", "43309", "43901", "43902", "43903", "43904", "43905", "43906", "43907", "43909"], "23": ["42102"], "24": ["45101", "45102", "45103", "45104", "45105", "45106", "45109", "45300", "45401", "46100", "46201", "46202", "46203", "46204", "46205", "46209", "46311", "46312", "46313", "46314", "46319", "46321", "46322", "46323", "46324", "46325", "46329", "46411", "46412", "46413", "46414", "46415", "46416", "46417", "46419", "46421", "46422", "46431", "46432", "46433", "46434", "46441", "46442", "46491", "46492", "46493", "46494", "46495", "46496", "46497", "46499", "46510", "46521", "46522", "46531", "46532", "46591", "46592", "46593", "46594", "46595", "46596", "46599", "46611", "46612", "46619", "46621", "46622", "46631", "46632", "46633", "46634", "46635", "46636", "46637", "46639", "46691", "46692", "46693", "46694", "46695", "46696", "46697", "46698", "46699", "46901", "46902", "46909", "47111", "47191", "47192", "47194", "47199", "47211", "47212", "47213", "47214", "47215", "47216", "47217", "47219", "47222", "47411", "47412", "47413", "47420", "47510", "47520", "47531", "47532", "47533", "47591", "47592", "47593", "47594", "47595", "47596", "47597", "47598", "47611", "47612", "47620", "47631", "47632", "47633", "47634", "47635", "47640", "47711", "47712", "47713", "47721", "47722", "47731", "47734", "47735", "47736", "47737", "47738", "47739", "47741", "47742", "47744", "47749", "47820", "47891", "47892", "47893", "47894", "47895", "47911", "47912", "47913", "47914", "47991", "47992", "47999"], "25": ["45201", "45202", "45203", "45204", "45205", "45402", "45403"], "26": ["46326", "46327", "47221", "47230", "47810"], "27": ["26520", "46443", "46444", "47732", "47733", "47743"], "28": ["47112", "47113", "47114", "47193"], "29": ["56102", "56103", "56104", "56105", "56106", "56107", "56210", "56290", "56302", "56303", "56304", "56309"], "30": ["49110", "49120", "49211", "49212", "49221", "49222", "49223", "49224", "49225", "49229", "49230", "49300", "50111", "50112", "50211", "50212", "51101", "51102", "51103", "51201", "51202", "51203", "52100", "52211", "52212", "52213", "52214", "52219", "52221", "52222", "52229", "52231", "52232", "52233", "52234", "52239", "52241", "52249", "52291", "52292", "52299", "53100", "53200"], "31": ["55101", "55102", "55103", "55104", "55105", "55106", "55107", "55108", "55109", "55200", "55900"], "32": ["56101", "56301"], "33": ["58110", "58120", "58130", "58190", "58201", "58202", "58203", "59110", "59120", "59130", "59140", "59200", "60100", "60200"], "34": ["61101", "61102", "61201", "61202", "61300", "61901", "61902", "61903", "61904", "61905", "61909"], "35": ["62010", "62021", "62022", "62091", "62099", "63111", "63112", "63120", "63910", "63990"], "36": ["64110", "64191", "64192", "64193", "64194", "64195", "64199", "64200", "64910", "64921", "64922", "64925", "64929", "64991", "64992", "64993", "64999"], "37": ["64301", "64302", "64303", "64304", "64309"], "38": ["64923", "64924", "66123", "66124", "66125"], "39": ["65111", "65112", "65121", "65122", "65123", "65124", "65125", "65201", "65202", "65203", "65204", "65205", "65206", "65207", "65301", "65302", "66211", "66212", "66221", "66222", "66223", "66224", "66290"], "40": ["66111", "66112", "66113", "66114", "66119", "66121", "66122", "66129", "66191", "66192", "66199", "66301", "66302", "66303"], "41": ["68201", "68202", "68203", "68209", "68101", "68102", "68103", "68104", "68109"], "42": ["69100"], "43": ["69200"], "44": ["70100", "70201", "70202", "70203", "70209"], "45": ["71101", "71102", "71103", "71109", "71200"], "46": ["72101", "72102", "72103", "72104", "72105", "72106", "72109", "72202", "72209"], "47": ["72201"], "48": ["73100", "73200"], "49": ["74101", "74102", "74103", "74109", "74200", "74901", "74902", "74903", "74904", "74905", "74909", "75000"], "50": ["77101", "77102", "77211", "77212", "77213", "77219", "77220", "77291", "77292", "77293", "77294", "77295", "77296", "77297", "77299", "77301", "77302", "77303", "77304", "77305", "77306", "77307", "77309", "77400"], "51": ["78100", "78200", "78300"], "52": ["79110", "79120", "79900"], "53": ["80100", "80200", "80300"], "54": ["81100", "81210", "81291", "81292", "81293", "81294", "81295", "81296", "81297", "81299", "81300"], "55": ["82110", "82191", "82192", "82193", "82194", "82195", "82196", "82199", "82200", "82301", "82302", "82910", "82920", "82990"], "56": ["84111", "84112", "84121", "84122", "84123", "84124", "84125", "84126", "84129", "84131", "84132", "84133", "84134", "84135", "84136", "84137", "84138", "84139"], "57": ["85101", "85102", "85103", "85104", "85211", "85212", "85221", "85222", "85301", "85302", "85411", "85412", "85419", "85421", "85429", "85491", "85492", "85493", "85494", "85499", "85500"], "58": ["86101", "86201", "86202", "86203", "86901", "86902", "86903", "86904", "86905", "86906", "86909", "87101", "87102", "87103", "87201", "87209", "87300"], "59": ["86102"], "60": ["87901", "87902", "87909"], "61": ["88101", "88109", "88901", "88902", "88909"], "62": ["90001", "90002", "90003", "90004", "90005", "90006", "90007", "90009"], "63": ["91011", "91012", "91021", "91022", "91031", "91032"], "64": ["92000"], "65": ["93111", "93112", "93113", "93114", "93115", "93116", "93117", "93118", "93119", "93120", "93191", "93192", "93193", "93199"], "66": ["93210", "93291", "93293", "93297", "93299"], "67": ["93292"], "68": ["93294", "93296"], "69": ["93295"], "70": ["94110", "94120", "94200", "94910", "94920", "94990"], "71": ["95111", "95112", "95113", "95121", "95122", "95123", "95124", "95125", "95126", "95127"], "72": ["95211", "95212", "95213", "95214", "95221", "95222", "95230", "95240", "95291", "95292", "95295", "95296", "95299"], "73": ["95294", "95293"], "74": ["96011", "96012", "96013", "96014", "96020", "96031", "96032", "96033", "96034", "96035", "96094", "96095", "96096", "96097", "96099"], "75": ["96091"], "76": ["96092"], "77": ["96093"], "78": ["97000"], "79": ["98100", "98200"], "80": ["99000"], "82": ["50113", "50121", "50122", "50220"], "83": ["84210", "84220", "84231", "84232", "84233", "84234", "84235", "84236", "84239", "84300"]}, "offerDetails": {"bizFlexiLoan": {"key": "bizFlexiLoan", "title": {"en": "Biz FlexiLoan", "ms": "<PERSON><PERSON><PERSON>", "zh": "商务弹性贷款"}, "subtitle": {"ms": "Pinjam pada %.2f%% setahun (%.2f%% setahun EIR)", "en": "Borrow at %.2f%% p.a. (%.2f%% p.a. EIR)", "zh": "以每年 %.2f%% 的利率借款（每年 %.2f%% 的实际利率）"}}, "bizFlexiLoanSoftReject": {"key": "bizFlexiLoanSoftReject", "subtitle": {"ms": "Pinjam pada %.2f%% setahun (%.2f%% setahun EIR)", "en": "Borrow at %.f%% p.a. (%.f%% p.a. EIR)", "zh": "以每年 %.f%% 的利率借款（每年 %.f%% 的实际利率）"}}, "bizDepositAccount": {"key": "bizDepositAccount", "title": {"en": "<PERSON>iz Account", "ms": "<PERSON><PERSON><PERSON>", "zh": "商务账户"}, "subtitle": {"en": "Earn 2.5% p.a. interest on balances that is added to your account daily", "ms": "Dapatkan faedah 2.5% setahun ke atas baki anda, dikreditkan ke akaun anda setiap hari.", "zh": "每日将余额利息以年利率2.5%计入您的账户"}}, "finePrint": {"en": "No interest is charged until you drawdown.", "ms": "Tiada faedah dikenakan se<PERSON>ga anda membuat pengeluaran.", "zh": "在您提款之前不收取利息。"}}, "offerDetailsAccepted": {"bizFlexiLoan": {"title": {"en": "Your available limit is", "ms": "<PERSON> maksimum anda ialah", "zh": "您的可用限额为"}, "content": {"en": "RM %s", "ms": "RM %s", "zh": "RM %s"}, "subtitle": {"en": "at %.2f%% p.a. flat rate", "ms": "pada kadar tetap %.2f%% setahun", "zh": "年平率为 %.2f%%"}, "subtitleFinePrint": {"isAvailable": true, "content": {"title": {"en": "Flat rate and Effective Interest Rate (EIR)", "ms": "<PERSON><PERSON> rata dan <PERSON><PERSON> (EIR)", "zh": "固定利率和实际利率（EIR）"}, "content": {"en": "Flat rate remains unchanged throughout the repayment tenure, and is calculated for the whole drawdown amount at the beginning of the repayment tenure. For example, the Effective Interest Rate (EIR) equivalent for a flat rate of 3.99% during a 12-month tenure is 7.29%.", "ms": "<PERSON>dar rata kekal tidak berubah sepanjang tempoh pembayaran balik dan dikira untuk jumlah pengeluaran keseluruhan pada permulaan tempoh pembayaran balik. <PERSON><PERSON><PERSON> contoh, <PERSON><PERSON> (EIR) yang bersamaan untuk kadar rata sebanyak 3.99% semasa tempoh 12 bulan ialah 7.29%.", "zh": "固定利率在整个还款期限内保持不变，并在还款期初按整个提款金额计算。例如，12个月期限内3.99%的固定利率相当于7.29%的实际利率（EIR）。"}}}, "finePrint": {"en": "You can only fund in from a personal bank account under your name", "enHtml": "You can only fund in from a <font color='#75F9AA'>personal bank account</font> under your name", "ms": "Anda hanya boleh membiayai dari akaun bank peribadi atas nama anda", "msHtml": "Anda hanya boleh membiayai dari <font color='#75F9AA'>akaun bank peribadi</font> atas nama anda", "zh": "您只能从您名下的个人银行账户中注资", "zhHtml": "您只能从您名下的<font color='#75F9AA'>个人银行账户</font>中注资"}, "activateFinePrint": {"en": "By activating your account, you accept the Biz FlexiLoan offered. No interest is charged until you draw down.", "ms": "Dengan mengaktifkan akaun anda, anda menerima Biz FlexiLoan yang di<PERSON>. Tiada faedah akan dikenakan sehingga anda membuat pengeluaran.", "zh": "通过激活您的账户，您即接受提供的 Biz FlexiLoan。提款之前不会收取任何利息。"}}, "bizDepositAccount": {"title": {"en": "<PERSON><PERSON><PERSON>", "ms": "<PERSON><PERSON><PERSON>", "zh": "赚取"}, "content": {"en": "2.5% p.a.", "ms": "2.5% setahun", "zh": "年利率 1.00%"}, "subtitle": {"en": "interest, paid daily", "ms": "fae<PERSON>, di<PERSON>ar setiap hari", "zh": "利息，每日支付"}, "subtitleFinePrint": {"isAvailable": false, "content": {"title": {"en": "", "ms": "", "zh": ""}, "content": {"en": "", "ms": "", "zh": ""}}}, "finePrint": {"en": "You can only fund in from a personal bank account under your name", "enHtml": "You can only fund in from a <font color='#75F9AA'>personal bank account</font> under your name", "ms": "Anda hanya boleh membiayai dari akaun bank peribadi atas nama anda", "msHtml": "Anda hanya boleh membiayai dari <font color='#75F9AA'>akaun bank peribadi</font> atas nama anda", "zh": "您只能从您名下的个人银行账户中注资", "zhHtml": "您只能从您名下的<font color='#75F9AA'>个人银行账户</font>中注资"}, "activateFinePrint": {"en": "By activating your account, you accept the deposit account offered.", "ms": "Dengan mengaktifkan akaun anda, anda menerima akaun deposit yang dita<PERSON>an.", "zh": "通过激活您的账户，您即接受提供的存款账户。"}}, "retailFlexiLoan": {"headerTitle": {"en": "Your FlexiCredit account is almost ready", "ms": "<PERSON><PERSON><PERSON> anda hampir sedia", "zh": "您的FlexiCredit账户即将准备就绪"}, "headerSubtitle": {"en": "Accept and add money into your GX Savings Account to start using FlexiCredit", "ms": "Te<PERSON> dan tambah wang ke dalam Akaun Simpanan GX anda untuk mula menggunakan FlexiCredit", "zh": "接受并将资金存入您的GX储蓄账户以开始使用FlexiCredit"}, "title": {"en": "Your available FlexiCredit limit is", "ms": "<PERSON> maksimum anda ialah", "zh": "您的可用限额为"}, "content": {"en": "RM %s", "ms": "RM %s", "zh": "RM %s"}, "subtitle": {"en": "at %.2f%% p.a. flat rate", "ms": "pada kadar tetap %.2f%% setahun", "zh": "年平率为 %.2f%%"}, "subtitleFinePrint": {"isAvailable": true, "content": {"title": {"en": "Flat rate and Effective Interest Rate (EIR)", "ms": "<PERSON><PERSON> rata dan <PERSON><PERSON> (EIR)", "zh": "固定利率和实际利率（EIR）"}, "content": {"en": "Flat rate remains unchanged throughout the repayment tenure, and is calculated for the whole drawdown amount at the beginning of the repayment tenure. For example, the Effective Interest Rate (EIR) equivalent for a flat rate of 3.99% during a 12-month tenure is 7.29%.", "ms": "<PERSON>dar rata kekal tidak berubah sepanjang tempoh pembayaran balik dan dikira untuk jumlah pengeluaran keseluruhan pada permulaan tempoh pembayaran balik. <PERSON><PERSON><PERSON> contoh, <PERSON><PERSON> (EIR) yang bersamaan untuk kadar rata sebanyak 3.99% semasa tempoh 12 bulan ialah 7.29%.", "zh": "固定利率在整个还款期限内保持不变，并在还款期初按整个提款金额计算。例如，12个月期限内3.99%的固定利率相当于7.29%的实际利率（EIR）。"}}}, "finePrint": {"en": "You can only fund in from a personal bank account under your name", "ms": "Anda hanya boleh membiayai dari akaun bank peribadi atas nama anda", "zh": "您只能从您名下的个人银行账户中注资"}, "activateFinePrint": {"en": "By accepting, you accept the offered credit limit. No interest is charged until you drawdown.", "ms": "<PERSON><PERSON> menerima, anda menerima had kredit yang dita<PERSON>an. <PERSON>iada faedah akan dikenakan se<PERSON>ga anda membuat pengeluaran.", "zh": "接受即表示您接受提供的信用额度。提款之前不会收取任何利息。"}, "exitTitle": {"en": "Not ready to accept FlexiCredit yet?", "ms": "Belum bersedia menerima FlexiCredit?", "zh": "尚未准备好接受 FlexiCredit?"}, "exitSubtitle": {"en": "That's okay as this offer is valid until %s. Accept it later on the Home screen when you're ready.", "ms": "Tidak masalah! Tawaram ini sah sehingga %s. <PERSON><PERSON><PERSON> nanti di <PERSON>rin <PERSON>a apabila anda bersedia.", "zh": "没关系！此邀请有效期至 %s. 您之后可在主屏幕随时接受它。"}}}, "failFastConfig": {"enabled": true, "nonPiiRequestFields": {"getUserProfileUserID": "********-746e-451e-a28f-23bf0478fb27", "getOnboardingConfigurationUserID": "********-746e-451e-a28f-23bf0478fb27"}}, "uploadFileConfig": {"proofOfStudent": {"maxFileSizeInBytes": "********", "allowedExtensions": ["PDF", "pdf", "jpeg", "JPEG", "JPG", "jpg", "PNG", "png"]}, "proofOfIncome": {"maxFileSizeInBytes": "********", "allowedExtensions": ["PDF", "pdf", "jpeg", "JPEG", "JPG", "jpg", "PNG", "png"]}}, "ekycFailedLimitConfig": {"failedAttemptWindowDays": 14, "maxFailedAttempts": 10}, "ctosExpiryDaysConfig": 365, "bizVerificationFailedLimitConfig": 5, "bizVerificationRules": {"bankStatementRules": [{"ruleName": "Account<PERSON><PERSON>Check", "isDisabled": true}, {"ruleName": "TransactionCheck", "isDisabled": false, "properties": {"minimumTransactionCount": 20, "minimumTransactionAmt": 0.0, "monthsBefore": 1, "lendingMinimumTransactionCount": 20, "lendingMonthsBefore": 3}}, {"ruleName": "FraudSuspiciousBankStatement", "isDisabled": false}, {"ruleName": "FraudAmountBalanceMismatch", "isDisabled": false}, {"ruleName": "FraudSuspiciousAtmWithdrawal", "isDisabled": false}, {"ruleName": "FraudChequeWithdrawalOnHoliday", "isDisabled": false}, {"ruleName": "FraudMajorityRoundFigureTransaction", "isDisabled": false}], "liveVideoRules": []}, "applicationRejectionCheckConfig": {"retailNRICRejectionDays": 14, "recentBRNRejectionDays": 14, "bizNRICRejectionDays": 30}, "phraseConfig": {"enFilePath": "./i18n/dev/en.json", "msFilePath": "./i18n/dev/ms.json"}, "workflowCompatibleAppVersion": {"Post-Application-Submission-V2": [{"deviceOS": "iOS", "minimumAppVersion": "1.0.0"}, {"deviceOS": "Android", "minimumAppVersion": "1.0.0"}]}, "maxApprovedMSMEApplications": 5}