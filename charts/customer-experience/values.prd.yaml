env: prd
configName: config.prd.json
industryConf: industry.csv
industryExemptionConf: industry_exemption.csv
productConf: product.csv
countryRiskConf: countryRiskMapping.csv
employmentConf: employment.prd.csv
envVars:
  MYSQL_HOST: customer-experience-db.onboarding.prd.g-bank.app
  MYSQL_HOST_REPLICA: customer-experience-db.onboarding.prd.g-bank.app
  MYSQL_DB: customer_experience
  SECRET_CONF: /vault/secrets/
  INDUSTRY_CONF: /config_files/industry.csv
  INDUSTRY_EXEMPTION_CONF: /config_files/industry_exemption.csv
  PRODUCT_CONF: /config_files/product.csv
  COUNTRY_RISK_CONF: /config_files/countryRiskMapping.csv
  EMPLOYMENT_CONF: /config_files/employment.csv
  AWS_ROLE_ARN: arn:aws:iam::************:role/AmazonEKSClusterCustomer-ExperienceRole
  AWS_WEB_IDENTITY_TOKEN_FILE: /var/run/secrets/eks.amazonaws.com/serviceaccount/token

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations:
    "eks.amazonaws.com/role-arn": "arn:aws:iam::************:role/AmazonEKSClusterCustomer-ExperienceRole"
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "customer-experience"

podAnnotations:
  vault.hashicorp.com/agent-inject: "true"
  vault.hashicorp.com/agent-init-first: "true"
  vault.hashicorp.com/agent-cache-enable: "true"
  vault.hashicorp.com/role: "customer-experience"
  vault.hashicorp.com/agent-inject-secret-db_creds.json: "database/creds/prd-onboarding-customer-experience-secret-rds-mysql-dba"
  vault.hashicorp.com/agent-inject-template-db_creds.json: |
    {{ with secret "database/creds/prd-onboarding-customer-experience-secret-rds-mysql-dba" -}}
      {
        "MYSQL_USERNAME": "{{ .Data.username }}",
        "MYSQL_PASSWORD": "{{ .Data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-redis_creds.json: "kv/data/security/tf/prd/cust-experience/ec/auth"
  vault.hashicorp.com/agent-inject-template-redis_creds.json: |
    {{ with secret "kv/data/security/tf/prd/cust-experience/ec/auth" -}}
      {
        "REDIS_PASSWORD": "{{ .Data.auth_token }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-customer_experience_napier_creds.json: "kv2/data/onboarding/app/customer-experience/napier"
  vault.hashicorp.com/agent-inject-template-customer_experience_napier_creds.json: |
    {{ with secret "kv2/data/onboarding/app/customer-experience/napier" -}}
      {
        "napier_username": "{{ .Data.data.username }}",
        "napier_password": "{{ .Data.data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-customer_experience_napier_v2_creds.json: "kv2/data/onboarding/app/customer-experience/napier_v2"
  vault.hashicorp.com/agent-inject-template-customer_experience_napier_v2_creds.json: |
    {{ with secret "kv2/data/onboarding/app/customer-experience/napier_v2" -}}
      {
        "napier_v2_username": "{{ .Data.data.username }}",
        "napier_v2_password": "{{ .Data.data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-customer_experience_webhook_creds.json: "kv2/data/onboarding/app/customer-experience/napier_webhook"
  vault.hashicorp.com/agent-inject-template-customer_experience_webhook_creds.json: |
      {{ with secret "kv2/data/onboarding/app/customer-experience/napier_webhook" -}}
        {
          "napier_webhook_username": "{{ .Data.data.username }}",
          "napier_webhook_password": "{{ .Data.data.password }}"
        }
      {{- end }}
  vault.hashicorp.com/agent-inject-secret-customer_experience_onetrust_creds.json: "kv2/data/onboarding/app/customer-experience/onetrust"
  vault.hashicorp.com/agent-inject-template-customer_experience_onetrust_creds.json: |
      {{ with secret "kv2/data/onboarding/app/customer-experience/onetrust" -}}
        {
          "ONETRUST_CLIENT_ID": "{{ .Data.data.clientID }}",
          "ONETRUST_CLIENT_SECRET": "{{ .Data.data.clientSecret }}"
        }
      {{- end }}
  vault.hashicorp.com/agent-inject-secret-customer_experience_onetrust_key.json: "kv2/data/onboarding/app/customer-experience/onetrust_key"
  vault.hashicorp.com/agent-inject-template-customer_experience_onetrust_key.json: |
      {{ with secret "kv2/data/onboarding/app/customer-experience/onetrust_key" -}}
        {
          "ONETRUST_PRIVATE_KEY": "{{ .Data.data.privateKey }}"
        }
      {{- end }}
  vault.hashicorp.com/agent-inject-secret-customer_experience_appian_creds.json: "kv2/data/onboarding/app/customer-experience/appian"
  vault.hashicorp.com/agent-inject-template-customer_experience_appian_creds.json: |
    {{ with secret "kv2/data/onboarding/app/customer-experience/appian" -}}
      {
        "appian_client_id": "{{ .Data.data.appian_client_id }}",
        "appian_client_secret": "{{ .Data.data.appian_client_secret }}"
      }
    {{- end }}

  # name is set to "ce" to prevent "customer_experience_partner_grabid_creds.json": name part must be no more than 63 characters]" error
  vault.hashicorp.com/agent-inject-secret-ce_partner_grabid_creds.json: "kv2/data/onboarding/app/customer-experience/external/partner_grabid"
  vault.hashicorp.com/agent-inject-template-ce_partner_grabid_creds.json: |
    {{ with secret "kv2/data/onboarding/app/customer-experience/external/partner_grabid" -}}
      {
        "partner_grabid_client_id": "{{ .Data.data.partner_grabid_client_id }}",
        "partner_grabid_client_secret": "{{ .Data.data.partner_grabid_client_secret }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-kv2_static_creds.json: kv2/data/onboarding/app/customer-experience/grabid
  vault.hashicorp.com/agent-inject-template-kv2_static_creds.json: |
    {{ with secret "kv2/data/onboarding/app/customer-experience/grabid" -}}
      {
        "GRAB_ID_SERVICE_KEY": "{{ .Data.data.grab_id_service_key }}"
      }
    {{- end }}

gateway:
  enabled: true
  annotations: {}
  hosts:
    - "internal.onboarding.prd.g-bank.app"
  tls: true

authorizationpolicy:
  enabled: true
  rules:
    - sources: [ "cluster.local/ns/istio-ingress/sa/istio-ingressgateway-service-account", "cluster.local/ns/sentry-t6/sa/sentry-t6"]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/api/*", "/swagger", "/health_check" ]
        - methods: [ "PUT" ]
          paths: [ "/api/*" ]
        - methods: [ "POST" ]
          paths: [ "/api/*" ]
        - methods: [ "DELETE" ]
          paths: [ "/api/*" ]
        - methods: [ "PATCH" ]
          paths: [ "/api/*" ]
    - sources: [ "cluster.local/ns/istio-ingress/sa/istio-ingress" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/api/v1/customers/namescreening" ]
    - sources: [ "cluster.local/ns/sentry-t6/sa/sentry-t6" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/api/v1/applications", "/api/v1/applications/*", "/api/v1/me", "/api/v1/onboarding/configurations","/api/v1/indexes/*", "/api/v1/customers/preferences", "/api/v1/customers/preferences?*", "/api/v1/customers/preferences/local", "/api/v1/customers/preferences/local?*", "/api/v1/referral/code", "/api/v1/referral/referee_list", "/api/v1/lending/application-status", "/api/v1/business-profile", "/api/v1/application/*", "/api/v1/documents",
                   "/api/v1/profiles", "/api/v1/customers/agreements" ]
        - methods: [ "PUT" ]
          paths: [ "/api/v1/applications/*", "/api/v1/customers/phone", "/api/v1/customers/phone/*", "/api/v1/customers/flags", "/api/v1/customers/preferences", "/api/v1/customer/mailing", "/api/v1/customers/preferences/local", "/api/v1/customers/agreements" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/applications", "/api/v1/applications/*", "/api/v1/texts", "/api/v1/partner-user-info", "/api/v1/partner-ekyc" , "/api/v1/referral/entry", "/api/v1/lending/applications", "/api/v1/lending/applications/*", "/api/v1/business/verify-brn"]
        - methods: [ "PATCH" ]
          paths: [ "/api/v1/me" , "/api/v1/business-profile"]
    - sources: [ "cluster.local/ns/operations/sa/customer-portal" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/api/v1/audittrail/*", "/api/v2/ecosystemID", "/health_check", "/api/v1/customers/preferred_language", "/api/v1/documents"]
        - methods: [ "POST" ]
          paths: [ "/api/v1/referral/ops/search_referee_list" ]
    - sources: [ "cluster.local/ns/sentry-t6/sa/sentry-t6", "cluster.local/ns/backend-dakota-app-01/sa/customer-portal", "cluster.local/ns/operations/sa/customer-portal" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/api/v1/workflow/event" ]
    - sources: [ "cluster.local/ns/backend-dakota-app-01/sa/aml-service" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/api/v2/ecosystemID", "/health_check" ]
    - sources: [ "cluster.local/ns/onboarding/sa/customer-experience" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/payments/sa/partnerpay-engine" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/first-fund-validation" ]
    - sources: [ "cluster.local/ns/partner-gateway/sa/sentry-partner-t6" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/api/v1/customers/namescreening" ]
        - methods: [ "GET" ]
          paths: [ "/api/v1/indexes/*" ]
    - sources: ["cluster.local/ns/onboarding/sa/customer-experience"]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/api/v1/return-authorize-param" ]
    - sources: [ "cluster.local/ns/pigeon/sa/pigeon"]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/api/v1/partnergrab/*", "/api/v1/customers/preferred_language" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]
    - sources: [ "cluster.local/ns/sentry-t6/sa/sentry-t6" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/api/v1/application/*" ]
        - methods: [ "PUT" ]
          paths: [ "/api/v1/customers/preferred_language" ]
        - methods: ["POST"]
          paths: [ "/api/v1/workflow/events" ]
    - sources: [ "cluster.local/ns/operations/sa/helpctr-exp" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/api/v1/customers/preferred_language" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]
    - sources: [ "cluster.local/ns/fintrust/sa/risk-broker" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/api/v2/ecosystemID", "/api/v1/safeID", "/api/v1/biz/ecosystem-id/*", "/api/v1/biz/bif/*" ]
    - sources: [ "cluster.local/ns/lending-platform/sa/asset-offline" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/api/v2/ecosystemID" ]
    - sources: [ "cluster.local/ns/insurance/sa/insurance-exp" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/api/v2/ecosystemID" ]
    - sources: [ "cluster.local/ns/identity/sa/iam-service" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID", "/api/v1/isValidProfile" ]
    - sources: [ "cluster.local/ns/identity/sa/id-exp" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/api/v1/customers/preferred_language"]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]
    - sources: [ "cluster.local/ns/identity/sa/iam-service" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID", "/api/v1/isValidProfile" ]
    - sources: [ "cluster.local/ns/payments/sa/qr-service", "cluster.local/ns/payments/sa/payment-engine"]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]
    - sources: [ "cluster.local/ns/lending-platform/sa/loan-app" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]
    - sources: [ "cluster.local/ns/lending-platform/sa/loan-exp" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check"]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]
    - sources: [ "cluster.local/ns/core-banking/sa/transaction-history"]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]
    - sources: [ "cluster.local/ns/core-banking/sa/account-service" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]
    - sources: [ "cluster.local/ns/core-banking/sa/deposits-exp" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]
    - sources: [ "cluster.local/ns/core-banking/sa/transaction-statements" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/api/v1/getActiveProfileID" ]

appgateway:
  enabled: false
  annotations: {}
  hosts:
    - "internal.onboarding.prd.g-bank.app"
  tls: true
resources:
  limits:
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 512Mi
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80
