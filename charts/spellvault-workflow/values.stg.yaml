image:
  pullPolicy: IfNotPresent

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""
env: "stg"

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "spellvault-read"

podAnnotations:
  vault.hashicorp.com/agent-inject: "false"
  co.elastic.logs/json.add_error_key: "true"
  co.elastic.logs/json.keys_under_root: "true"
  co.elastic.logs/json.message_key: message
  co.elastic.logs/json.overwrite_keys: "true"
  # https://www.elastic.co/guide/en/beats/filebeat/current/multiline-examples.html
  co.elastic.logs/multiline.type: "pattern"
  co.elastic.logs/multiline.pattern: '^([\d\-\:\+\[\]TZ\.]+(\[?(INFO|WARNING|ERROR|DEBUG|CRITICAL|FATAL)\]?))|(\[?(INFO|WARNING|ERROR|DEBUG|CRITICAL|FATAL)\]?: )'
  co.elastic.logs/multiline.negate: "true"
  co.elastic.logs/multiline.match: "after"

podSecurityContext:
  fsGroup: 65532
securityContext:
  runAsNonRoot: true
  runAsUser: 65532
  runAsGroup: 65532

service:
  type: ClusterIP
  port: 8000

ingress:
  enabled: true
  type: VirtualService
  namespace: istio-ingress
  ingressClassName: dsa-gateway
  #  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
  - spellvault-workflow.dsa.stg.g-bank.app
  path: /
  labels:
    traffic-type: spellvault
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: '2'
    memory: 4Gi
  requests:
    cpu: '2'
    memory: 4Gi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 4
  targetCPUUtilizationPercentage: 60

nodeSelector:
  service: spellvault

tolerations:
  - key: "service"
    operator: "Equal"
    value: "spellvault"
    effect: "NoSchedule"

# affinity:
#   podAntiAffinity:
#     requiredDuringSchedulingIgnoredDuringExecution:
#     - labelSelector:
#         matchExpressions:
#         - key: app
#           operator: In
#           values:
#           - spellvault-workflow
#       topologyKey: "kubernetes.io/hostname"

podSecurityPolicy:
  securityGroupIds:
    - sg-<pod-sg>

config: {}

# restart service every 7 days at 3:20 AM MYT (cron config in UTC)
restarter:
  enabled: true
  schedule: "30 22 * * *"