apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "spellvault-workflow.fullname" . }}
  labels:
  {{- include "spellvault-workflow.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  selector:
    matchLabels:
    {{- include "spellvault-workflow.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "spellvault-workflow.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "spellvault-workflow.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
      - name: {{ .Chart.Name }}
        securityContext:
          {{- toYaml .Values.securityContext | nindent 12 }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        volumeMounts:
          - name: spellvault-workflow-conf
            mountPath: /var/config.json
            subPath: config.json
        ports:
          - name: http
            containerPort: 9999
            protocol: TCP
        livenessProbe:
          httpGet:
            scheme: HTTP
            port: 9999
            path: /health-check
          initialDelaySeconds: 10
          periodSeconds: 20
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 6
        readinessProbe:
          httpGet:
            scheme: HTTP
            port: 9999
            path: /health-check
          initialDelaySeconds: 10
          periodSeconds: 20
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 6
        resources:
          {{- toYaml .Values.resources | nindent 12 }}
      volumes:
      - name: spellvault-workflow-conf
        configMap:
          name: {{ include "spellvault-workflow.fullname" $ }}-conf
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}