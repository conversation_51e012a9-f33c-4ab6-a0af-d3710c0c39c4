{{- if .Values.ingress.enabled -}}
{{- $serviceName := include "spellvault-workflow.fullname" . -}}
{{- $servicePort := .Values.service.port -}}
{{- $ingressPath := .Values.ingress.path -}}
apiVersion: {{ ternary "networking.istio.io/v1beta1" "networking.k8s.io/v1" (eq .Values.ingress.type "VirtualService") }}
kind: {{ .Values.ingress.type }}
metadata:
  name: {{ template "spellvault-workflow.fullname" . }}
  namespace: {{ .Values.ingress.namespace | default .Release.Namespace }}
  labels:
    app: {{ template "spellvault-workflow.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- range $key, $value := .Values.ingress.labels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  annotations:
    {{- range $key, $value := .Values.ingress.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
spec:
{{- if eq .Values.ingress.type "VirtualService" }}
  gateways:
  - {{ .Values.ingress.ingressClassName }}
  hosts:
  {{- range $host := .Values.ingress.hosts }}
  - {{ $host }}
  {{- end }}
  http:
  - match:
    - uri:
        prefix: {{ $ingressPath }}
    route:
    - destination:
        host: {{ $serviceName }}.{{ .Release.Namespace }}.svc.cluster.local
        port:
          number: {{ $servicePort }}
{{- else }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  rules:
  {{- range $host := .Values.ingress.hosts }}
  - host: {{ $host }}
    http:
      paths:
      - path: {{ $ingressPath }}
        backend:
          service:
            name: {{ $serviceName }}
            port:
              number: {{ $servicePort }}
        pathType: "ImplementationSpecific"
  {{- end -}}
  {{- if .Values.ingress.tls }}
  tls:
{{ toYaml .Values.ingress.tls | indent 4 }}
  {{- end -}}
{{- end }}
{{- end -}}