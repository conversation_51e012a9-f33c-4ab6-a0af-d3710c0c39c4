{{- define "transaction-signing.deployment" -}}
{{- end -}}

{{- include "go-app-lib.deployment" (list . "transaction-signing.deployment") }}
---
{{- define "transaction-signing.rollout" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rollout" (list . "transaction-signing.rollout") }}
---
{{- end -}}

{{- define "transaction-signing.rolloutvirtualservice" -}}
{{- end -}}

{{- if and .Values.rollout .Values.argorollouts.rolloutTrafficRoutingEnabled  -}}
{{- include "go-app-lib.rolloutvirtualservice" (list . "transaction-signing.rolloutvirtualservice") }}
---
{{- end -}}

{{- define "transaction-signing.rolloutdestinationrule" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.rolloutdestinationrule" (list . "transaction-signing.rolloutdestinationrule") }}
---
{{- end -}}

{{- define "transaction-signing.analysistemplate" -}}
{{- end -}}

{{- if .Values.rollout -}}
{{- include "go-app-lib.analysistemplate" (list . "transaction-signing.analysistemplate") }}
---
{{- end -}}

{{- define "transaction-signing.service" -}}
{{- end -}}

{{- include "go-app-lib.service" (list . "transaction-signing.service") }}
---
{{- define "transaction-signing.hpa" -}}
{{- end -}}

{{- if .Values.autoscaling.enabled }}
{{- include "go-app-lib.hpa" (list . "transaction-signing.hpa") }}
---
{{- end -}}

{{- define "transaction-signing.ingress" -}}
{{- end -}}

{{- if .Values.ingress.enabled -}}
{{- include "go-app-lib.ingress" (list . "transaction-signing.ingress") }}
---
{{- end -}}

{{- define "transaction-signing.serviceaccount" -}}
{{- end -}}

{{- if .Values.serviceAccount.create -}}
{{- include "go-app-lib.serviceaccount" (list . "transaction-signing.serviceaccount") }}
---
{{- end -}}

{{- define "transaction-signing.gateway" -}}
{{- end -}}

{{- define "transaction-signing.virtualservice" -}}
{{- end -}}

{{- if .Values.gateway.enabled -}}
{{- include "go-app-lib.gateway" (list . "transaction-signing.gateway") }}
---
{{- include "go-app-lib.virtualservice" (list . "transaction-signing.virtualservice") }}
---
{{- end -}}

{{- define "transaction-signing.authorizationpolicy" -}}
{{- end -}}

{{- if .Values.authorizationpolicy.enabled -}}
{{- include "go-app-lib.authorizationpolicy" (list . "transaction-signing.authorizationpolicy") }}
---
{{- end -}}

{{- define "transaction-signing.podDisruptionBudget" -}}
{{- end -}}

{{- if and (.Values.podDisruptionBudget.enabled) (ne .Values.env "dev") -}}
{{- include "go-app-lib.podDisruptionBudget" (list . "transaction-signing.podDisruptionBudget") }}
{{- end }}
---