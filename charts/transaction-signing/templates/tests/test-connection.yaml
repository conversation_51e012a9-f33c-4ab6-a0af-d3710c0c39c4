apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "transaction-signing.fullname" . }}-test-connection"
  labels:
    {{- include "transaction-signing.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: 851255665500.dkr.ecr.ap-southeast-1.amazonaws.com/public-images:busybox-1.33.1
      command: ['wget']
      args: ['{{ include "transaction-signing.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
