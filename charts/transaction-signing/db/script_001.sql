CREATE TABLE `user_public_key` (
   `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
   `user_id` VARCHAR(36) NOT NULL COMMENT 'User ID belonging to the device',
   `device_id` VARCHAR(255) NOT NULL COMMENT 'Device id',
   `public_key` VARCHAR(1024) NOT NULL COMMENT 'Base64 encoded device RSA public key',
   `public_key_id` VARCHAR(32) NOT NULL COMMENT 'Device RSA public key id',
   `public_key_signature` VARCHAR(500) NOT NULL COMMENT 'Public key signature',
   `version` BIGINT UNSIGNED NOT NULL COMMENT 'Public key version',
   `idempotency_key` VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Idempotency key',
   `metadata` JSON DEFAULT NULL COMMENT 'Additional non-essential data relating to device registration',
   `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   <PERSON>IMARY KEY (`id`),
   <PERSON><PERSON><PERSON> `index_created_at` (`created_at`),
   KEY `index_updated_at` (`updated_at`),
   UNIQUE KEY (`user_id`, `device_id`, `version`),
   UNIQUE KEY (`public_key_id`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `signing_attempt` (
   `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
   `signing_id` VARCHAR(36) NOT NULL COMMENT 'Signing id',
   `user_id` VARCHAR(36) NOT NULL COMMENT 'User ID belonging to the device',
   `device_id` VARCHAR(255) NOT NULL COMMENT 'Device id',
   `public_key_id` VARCHAR(32) NOT NULL COMMENT 'Public key id',
   `payload` JSON NOT NULL COMMENT 'Payload',
   `signature` CHAR(96) NOT NULL COMMENT 'Signature',
   `status`  VARCHAR(16) NOT NULL COMMENT 'Status of signing attempt',
   `metadata` JSON DEFAULT NULL COMMENT 'Additional non-essential data relating to signing attempt',
   `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
   PRIMARY KEY (`id`),
   KEY `index_created_at` (`created_at`)
)  DEFAULT CHARSET = utf8mb4
   COLLATE = utf8mb4_unicode_ci;


ALTER TABLE `signing_attempt`
    ADD COLUMN `service_id` VARCHAR(36) NOT NULL COMMENT 'Service ID' AFTER `user_id`;

ALTER TABLE `user_public_key`
    ADD COLUMN `status` VARCHAR(36) NOT NULL COMMENT 'Public key status' AFTER `version`;

-- Deploy transaction-signing:0003-rename_constraints_add_column to mysql

BEGIN;

-- user_public_key table

ALTER TABLE `user_public_key` RENAME KEY `user_id` TO `uk_user_device_id_version`;
ALTER TABLE `user_public_key` RENAME KEY `public_key_id` TO `uk_public_key_id`;

-- signing_attempt table

ALTER TABLE `signing_attempt` ADD COLUMN `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`;
CREATE INDEX `index_updated_at` ON `signing_attempt` (updated_at);

COMMIT;
