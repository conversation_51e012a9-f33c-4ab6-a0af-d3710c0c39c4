env: stg
configName: config.stg.json

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/transaction-signing-kms-role

gateway:
  enabled: true
  annotations: {}
  hosts:
    - "internal.identity.stg.g-bank.app"
  tls: true

resources:
  limits:
    memory: 2048Mi
  requests:
    cpu: 500m
    memory: 1024Mi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 100
  targetMemoryUtilizationPercentage: 90

deployment:
  enabled: true
  runMysqlMigration: false

envVars:
  MYSQL_HOST: transaction-signing-db.identity.stg.g-bank.app
  MYSQL_HOST_REPLICA: transaction-signing-db.identity.stg.g-bank.app
  SERVICE_CONF: /config_files/config.json
  SECRET_CONF: /vault/secrets/
  DB_NAME: transaction_signing

podAnnotations:
  vault.hashicorp.com/role: "transaction-signing"
  vault.hashicorp.com/agent-inject-secret-gid.json: "kv2/data/identity/app/transaction-signing/gid"
  vault.hashicorp.com/agent-inject-template-gid.json: '{ {{- with secret "kv2/data/identity/app/transaction-signing/gid" -}}"grab_id_service_key":"{{ .Data.data.grab_id_service_key }}"{{- end }} }'
  vault.hashicorp.com/agent-inject-secret-public_key_config.json: "kv2/data/identity/app/transaction-signing/public-key-config"
  vault.hashicorp.com/agent-inject-template-public_key_config.json: |
    {{ with secret "kv2/data/identity/app/transaction-signing/public-key-config" -}}
      {
        "public_key_config_private_key": "{{ .Data.data.private_key }}",
        "public_key_config_public_key": "{{ .Data.data.public_key }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-db_creds.json: "database/creds/stg-identity-transaction-signing-rds-mysql-dba"
  vault.hashicorp.com/agent-inject-template-db_creds.json: |
    {{ with secret "database/creds/stg-identity-transaction-signing-rds-mysql-dba" -}}
      {
        "MYSQL_USERNAME": "{{ .Data.username }}",
        "MYSQL_PASSWORD": "{{ .Data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-db-schema-migration: "database/creds/stg-identity-transaction-signing-rds-mysql-dba"
  vault.hashicorp.com/agent-inject-template-db-schema-migration: |
    {{ with secret "database/creds/stg-identity-transaction-signing-rds-mysql-dba" -}}
      export mysqluser="{{ .Data.username }}"
      export mysqlpass="{{ .Data.password }}"
    {{- end }}

authorizationpolicy:
  enabled: true
  rules:
    - sources:
        [
          "cluster.local/ns/istio-ingress/sa/istio-ingressgateway-service-account",
        ]
    - sources: ["cluster.local/ns/identity/sa/risk-proxy"]
      endpoints:
        - methods: ["POST"]
          paths:
            [
              "/v1/tx-sign/static/mock",
              "/v1/tx-sign/mock",
              "/v1/tx-sign/risk-check/mock",
            ]
    - sources: ["cluster.local/ns/identity/sa/iam-service"]
      endpoints:
        - methods: ["POST"]
          paths: ["/v1/tx-sign/register"]
        - methods: ["PATCH"]
          paths: ["/v1/tx-sign/deregister"]
    - sources: ["cluster.local/ns/sentry-t6/sa/sentry-t6"]
      endpoints:
        - methods: ["POST"]
          paths:
            [
              "/v1/tx-sign/verify",
              "/v1/tx-sign/static/mock",
              "/v1/tx-sign/mock",
            ]
    - sources: ["cluster.local/ns/identity/sa/risk-service"]
      endpoints:
        - methods: ["POST"]
          paths: ["/v1/tx-sign/key/timestamp"]
    - sources:
        [
          "cluster.local/ns/identity/sa/risk-proxy",
          "cluster.local/ns/identity/sa/iam-service",
          "cluster.local/ns/fintrust/sa/risk-service",
        ]
      endpoints:
        - methods: ["GET"]
          paths: ["/health_check"]
