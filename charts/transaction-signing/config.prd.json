{"name": "transaction-signing Service", "serviceName": "transaction-signing", "host": "0.0.0.0", "port": 8080, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/transaction_signing?parseTime=true&loc=UTC&sql_mode=TRADITIONAL", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST_REPLICA$:3306)/transaction_signing?parseTime=true&loc=UTC&sql_mode=TRADITIONAL", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "kmsConfig": {"useDummyPK": false, "keyId": "3a094080-d71f-4f10-bcde-90c1bbbb566e", "region": "ap-southeast-1"}, "grabIDConfig": {"hostAddress": "http://grab-id.identity.svc.cluster.local", "ackServerPath": "/v1/mfa/grabSecureChallenge/ack", "circuitConfig": {"grab-id": {"timeout": 15}}, "serviceName": "TX_SIGN", "serviceKey": "{{grab_id_service_key}}"}, "publicKeyConfig": {"useKMS": false, "useDummyPK": false, "keyID": "dev_v1", "keys": {"dev_v1": {"privateKey": "{{ public_key_config_private_key }}", "publicKey": "{{ public_key_config_public_key }}"}}}, "headerValidationConfig": {"enabled": false, "keys": {"X-Grab-Id-Userid": "^[a-zA-Z0-9-_]{1,36}$", "X-Request-ID": "^[a-zA-Z0-9-_]{1,36}$", "X-Grab-Id-Serviceid": "^[a-zA-Z0-9-_]{1,36}$", "X-Tx-Signature": "^[a-zA-Z0-9/+=]{1,96}$", "X-Grab-Challenge-Id": "^[a-zA-Z0-9-_]{1,255}$", "X-Device-Id": "^[a-zA-Z0-9-_]{1,255}$"}}}