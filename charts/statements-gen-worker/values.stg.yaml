env: stg
configName: config.stg.json

gateway:
  enabled: false
  annotations: {}
  hosts:
    - "internal.cb.stg.g-bank.app"
  tls: {}

serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks/cluster/stg-cb-01/transaction-statements
  name: statements-gen-worker

autoscaling:
  enabled: true

envVars:
  SECRET_CONF: /vault/secrets/
  SERVICE_CONF: /config_files/config.json
  AWS_ROLE_ARN: arn:aws:iam::************:role/eks/cluster/stg-cb-01/transaction-statements
  AWS_WEB_IDENTITY_TOKEN_FILE: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
  AWS_REGION: ap-southeast-1

podAnnotations:
  vault.hashicorp.com/role: "transaction-statements"
  vault.hashicorp.com/agent-inject-secret-transaction_statements_static_creds.json: database/static-creds/stg-data-backend-txn-statement-ro
  vault.hashicorp.com/agent-inject-template-transaction_statements_static_creds.json: |
    {{ with secret "database/static-creds/stg-data-backend-txn-statement-ro" -}}
      {
        "SNOWFLAKE_USER": "{{ .Data.username }}",
        "SNOWFLAKE_PASSWORD": "{{ .Data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-redis_creds.json: "kv/data/security/tf/stg/txn-statements/ec/auth"
  vault.hashicorp.com/agent-inject-template-redis_creds.json: |
    {{ with secret "kv/data/security/tf/stg/txn-statements/ec/auth" -}}
      {
        "REDIS_PASSWORD": "{{ .Data.auth_token }}"
      }
    {{- end }}


volumeMountPath: /config_files

scheduleTime: "30 * * * *"

ttlSecondsAfterFinished: 7200

restartPolicy: OnFailure

concurrencyPolicy: Replace

vaultAnnotation:
  disabled: false

# it's mandantory, don't comment out
suspend: true

parallelism: 1

workerConfig:
  consumer:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
  largeConsumer:
    resources:
      limits:
        cpu: 1500m
        memory: 30Gi
      requests:
        cpu: 750m
        memory: 25Gi
    suspend: true
  publisher:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
  opsS3Publisher:
    scheduleTime: "0 0 3 * *"
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
    _suspend: "Always suspend this job, manually trigger the cronjob when needed"

bizWorkerConfig:
  scheduleTime: "0 0 3 * *"
  consumer:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: false
  largeConsumer:
    scheduleTime: "15 0 3 * *"
    resources:
      limits:
        cpu: 1500m
        memory: 6.5Gi
      requests:
        cpu: 750m
        memory: 4.5Gi
    suspend: false
  publisher:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: false

lendingWorkerConfig:
  scheduleTime: "0 23 28-31 * *"
  consumer:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: false
    parallelism: 3
  publisher:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: false

bizFlexiCreditWorkerConfig:
  scheduleTime: "40 * * * *"
  consumer:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: false
    parallelism: 3
  publisher:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: false