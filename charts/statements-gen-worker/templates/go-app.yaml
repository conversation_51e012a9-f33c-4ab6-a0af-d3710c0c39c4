{{- define "statements-gen-worker.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "CONSUMER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "PUBLISHER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $_ := set $container "env" $env}}
spec:
  suspend: {{ .Values.suspend }} 
  jobTemplate:
    spec:
      parallelism: {{ .Values.parallelism | default 1 }}
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- include "go-app-lib.cron" (list . "statements-gen-worker.cron") }}
---

{{- define "statements-gen-consumer.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "CONSUMER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $requests := dict "memory" .Values.workerConfig.consumer.resources.requests.memory "cpu" .Values.workerConfig.consumer.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.workerConfig.consumer.resources.limits.memory "cpu" .Values.workerConfig.consumer.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
{{ $_ := set $container "env" $env}}
metadata:
  name: statements-gen-consumer
spec:
  schedule: {{ .Values.workerConfig.consumer.scheduleTime | default .Values.scheduleTime }}
  suspend: {{ .Values.workerConfig.consumer.suspend }} 
  jobTemplate:
    spec:
      parallelism: {{ .Values.parallelism | default 1 }}
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- include "go-app-lib.cron" (list . "statements-gen-consumer.cron") }}
---

{{- define "biz-statements-gen-consumer.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "BIZ_CONSUMER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $requests := dict "memory" .Values.bizWorkerConfig.consumer.resources.requests.memory "cpu" .Values.bizWorkerConfig.consumer.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.bizWorkerConfig.consumer.resources.limits.memory "cpu" .Values.bizWorkerConfig.consumer.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
{{ $_ := set $container "env" $env}}
metadata:
  name: biz-statements-gen-consumer
spec:
  schedule: {{ .Values.bizWorkerConfig.scheduleTime }}
  suspend: {{ .Values.bizWorkerConfig.consumer.suspend }}
  jobTemplate:
    spec:
      parallelism: {{ .Values.parallelism | default 1 }}
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- if and .Values.bizWorkerConfig .Values.bizWorkerConfig.consumer -}}
{{- include "go-app-lib.cron" (list . "biz-statements-gen-consumer.cron") }}
---
{{- end -}}

{{- define "lending-statements-gen-consumer.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "LENDING_CONSUMER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $requests := dict "memory" .Values.lendingWorkerConfig.consumer.resources.requests.memory "cpu" .Values.lendingWorkerConfig.consumer.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.lendingWorkerConfig.consumer.resources.limits.memory "cpu" .Values.lendingWorkerConfig.consumer.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
{{ $_ := set $container "env" $env}}
metadata:
  name: lending-statements-gen-consumer
spec:
  schedule: {{ .Values.lendingWorkerConfig.scheduleTime }}
  suspend: {{ .Values.lendingWorkerConfig.consumer.suspend }}
  jobTemplate:
    spec:
      parallelism: {{ .Values.lendingWorkerConfig.consumer.parallelism | default 1 }}
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- include "go-app-lib.cron" (list . "lending-statements-gen-consumer.cron") }}
---

{{- define "biz-flexi-credit-statements-gen-consumer.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "BIZ_LENDING_CONSUMER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $requests := dict "memory" .Values.bizFlexiCreditWorkerConfig.consumer.resources.requests.memory "cpu" .Values.bizFlexiCreditWorkerConfig.consumer.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.bizFlexiCreditWorkerConfig.consumer.resources.limits.memory "cpu" .Values.bizFlexiCreditWorkerConfig.consumer.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
{{ $_ := set $container "env" $env}}
metadata:
  name: biz-flexi-credit-statements-gen-consumer
spec:
  suspend: {{ .Values.bizFlexiCreditWorkerConfig.consumer.suspend }}
  jobTemplate:
    spec:
      parallelism: {{ .Values.parallelism | default 1 }}
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- if and .Values.bizFlexiCreditWorkerConfig .Values.bizFlexiCreditWorkerConfig.consumer -}}
{{- include "go-app-lib.cron" (list . "biz-flexi-credit-statements-gen-consumer.cron") }}
---
{{- end -}}

{{- define "statements-gen-large-consumer.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "LARGE_CONSUMER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $_ := set $container "env" $env}}
{{ $requests := dict "memory" .Values.workerConfig.largeConsumer.resources.requests.memory "cpu" .Values.workerConfig.largeConsumer.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.workerConfig.largeConsumer.resources.limits.memory "cpu" .Values.workerConfig.largeConsumer.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
metadata:
  name: statements-gen-large-consumer
spec:
  suspend: {{ .Values.workerConfig.largeConsumer.suspend }}
  jobTemplate:
    spec:
      parallelism: 1
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: node_type
                        operator: In
                        values:
                          - large
          tolerations:
            - key: "workload"
              operator: "Equal"
              value: "high-resource"
              effect: "NoSchedule"
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}
{{- include "go-app-lib.cron" (list . "statements-gen-large-consumer.cron") }}
---

{{- define "biz-statements-gen-large-consumer.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "BIZ_LARGE_CONSUMER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $_ := set $container "env" $env}}
{{ $requests := dict "memory" .Values.bizWorkerConfig.largeConsumer.resources.requests.memory "cpu" .Values.bizWorkerConfig.largeConsumer.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.bizWorkerConfig.largeConsumer.resources.limits.memory "cpu" .Values.bizWorkerConfig.largeConsumer.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
metadata:
  name: biz-statements-gen-large-consumer
spec:
  schedule: {{ default .Values.bizWorkerConfig.scheduleTime .Values.bizWorkerConfig.largeConsumer.scheduleTime }}
  suspend: {{ .Values.bizWorkerConfig.largeConsumer.suspend }}
  jobTemplate:
    spec:
      parallelism: 1
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: node_type
                        operator: In
                        values:
                          - large
          tolerations:
            - key: "workload"
              operator: "Equal"
              value: "high-resource"
              effect: "NoSchedule"
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- if and .Values.bizWorkerConfig .Values.bizWorkerConfig.largeConsumer -}}
{{- include "go-app-lib.cron" (list . "biz-statements-gen-large-consumer.cron") }}
---
{{- end -}}

{{- define "statements-gen-publisher.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "PUBLISHER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $requests := dict "memory" .Values.workerConfig.publisher.resources.requests.memory "cpu" .Values.workerConfig.publisher.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.workerConfig.publisher.resources.limits.memory "cpu" .Values.workerConfig.publisher.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
{{ $_ := set $container "env" $env}}
metadata:
  name: statements-gen-publisher
spec:
  suspend: {{ .Values.workerConfig.publisher.suspend }} 
  jobTemplate:
    spec:
      parallelism: 1
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- include "go-app-lib.cron" (list . "statements-gen-publisher.cron") }}
---

{{- define "biz-statements-gen-publisher.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "BIZ_PUBLISHER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $requests := dict "memory" .Values.bizWorkerConfig.publisher.resources.requests.memory "cpu" .Values.bizWorkerConfig.publisher.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.bizWorkerConfig.publisher.resources.limits.memory "cpu" .Values.bizWorkerConfig.publisher.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
{{ $_ := set $container "env" $env}}
metadata:
  name: biz-statements-gen-publisher
spec:
  schedule: {{ .Values.bizWorkerConfig.scheduleTime }}
  suspend: {{ .Values.bizWorkerConfig.publisher.suspend }}
  jobTemplate:
    spec:
      parallelism: 1
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- if and .Values.bizWorkerConfig .Values.bizWorkerConfig.publisher -}}
{{- include "go-app-lib.cron" (list . "biz-statements-gen-publisher.cron") }}
---
{{- end -}}

{{- define "biz-flexi-credit-statements-gen-publisher.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "BIZ_LENDING_PUBLISHER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $requests := dict "memory" .Values.bizFlexiCreditWorkerConfig.publisher.resources.requests.memory "cpu" .Values.bizFlexiCreditWorkerConfig.publisher.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.bizFlexiCreditWorkerConfig.publisher.resources.limits.memory "cpu" .Values.bizFlexiCreditWorkerConfig.publisher.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
{{ $_ := set $container "env" $env}}
metadata:
  name: biz-flexi-credit-statements-gen-publisher
spec:
  schedule: {{ .Values.bizFlexiCreditWorkerConfig.scheduleTime }}
  suspend: {{ .Values.bizFlexiCreditWorkerConfig.publisher.suspend }}
  jobTemplate:
    spec:
      parallelism: 1
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- if and .Values.bizFlexiCreditWorkerConfig .Values.bizFlexiCreditWorkerConfig.publisher -}}
{{- include "go-app-lib.cron" (list . "biz-flexi-credit-statements-gen-publisher.cron") }}
---
{{- end -}}

{{- define "lending-statements-gen-publisher.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "LENDING_PUBLISHER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $requests := dict "memory" .Values.lendingWorkerConfig.publisher.resources.requests.memory "cpu" .Values.lendingWorkerConfig.publisher.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.lendingWorkerConfig.publisher.resources.limits.memory "cpu" .Values.lendingWorkerConfig.publisher.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
{{ $_ := set $container "env" $env}}
metadata:
  name: lending-statements-gen-publisher
spec:
  schedule: {{ .Values.lendingWorkerConfig.scheduleTime }}
  suspend: {{ .Values.lendingWorkerConfig.publisher.suspend }}
  jobTemplate:
    spec:
      parallelism: 1
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- include "go-app-lib.cron" (list . "lending-statements-gen-publisher.cron") }}
---

{{- define "ops-s3-statements-gen-publisher.cron" -}}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" "spec" $tpl | first)}}
{{ $env:= get $container "env" }}
{{ $env = append $env (dict "name" "OPS_S3_PUBLISHER_ENABLED" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_ISTIO" "value" "true" ) }}
{{ $env = append $env (dict "name" "MANUAL_QUIT_VAULT" "value" "true" ) }}
{{ $requests := dict "memory" .Values.workerConfig.opsS3Publisher.resources.requests.memory "cpu" .Values.workerConfig.opsS3Publisher.resources.requests.cpu }}
{{ $limits := dict "memory" .Values.workerConfig.opsS3Publisher.resources.limits.memory "cpu" .Values.workerConfig.opsS3Publisher.resources.limits.cpu }}
{{ $_ := set $container "resources" (dict "requests" $requests "limits" $limits) }}
{{ $_ := set $container "env" $env}}
metadata:
  name: ops-s3-statements-gen-publisher
spec:
  schedule: {{ .Values.workerConfig.opsS3Publisher.scheduleTime }}
  suspend: {{ .Values.workerConfig.opsS3Publisher.suspend }}
  jobTemplate:
    spec:
      parallelism: 1
      template:
        metadata:
          annotations:
            vault.hashicorp.com/agent-enable-quit: "true"
        spec:
          containers:
            - {{ toYaml $container | nindent 14 }}
{{- end -}}

{{- if and .Values.workerConfig .Values.workerConfig.opsS3Publisher -}}
{{- include "go-app-lib.cron" (list . "ops-s3-statements-gen-publisher.cron") }}
---
{{- end -}}

{{- define "statements-gen-worker.ingress" -}}
{{- end -}}

{{- if .Values.ingress.enabled -}}
{{- include "go-app-lib.ingress" (list . "statements-gen-worker.ingress") }}
---
{{- end -}}

{{- define "statements-gen-worker.serviceaccount" -}}
{{- end -}}

{{- if .Values.serviceAccount.create -}}
{{- include "go-app-lib.serviceaccount" (list . "statements-gen-worker.serviceaccount") }}
---
{{- end -}}

{{- define "statements-gen-worker.gateway" -}}
{{- end -}}

{{- define "statements-gen-worker.virtualservice" -}}
{{- end -}}

{{- if .Values.gateway.enabled -}}
{{- include "go-app-lib.gateway" (list . "statements-gen-worker.gateway") }}
---
{{- include "go-app-lib.virtualservice" (list . "statements-gen-worker.virtualservice") }}
---
{{- end -}}
