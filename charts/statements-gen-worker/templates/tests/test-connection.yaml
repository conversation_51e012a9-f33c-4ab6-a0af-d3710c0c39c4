apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "statements-gen-worker.fullname" . }}-test-connection"
  labels:
    {{- include "statements-gen-worker.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: 303083450960.dkr.ecr.ap-southeast-3.amazonaws.com/public-images:busybox-1.33.1
      command: ['wget']
      args: ['{{ include "statements-gen-worker.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
