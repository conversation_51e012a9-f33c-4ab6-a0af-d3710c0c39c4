{"name": "statements-gen-worker Service", "serviceName": "statements-gen-worker", "host": "0.0.0.0", "port": 8080, "env": "stg", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": true}, "snowflake_config": {"dsn": "{{ SNOWFLAKE_USER }}:{{ SNOWFLAKE_PASSWORD }}@hq77744.ap-southeast-1.privatelink.snowflakecomputing.com/GOLD/STATEMENTS?warehouse=txn_statements_wh&role=TXN_STATEMENTS_SERVICE_ROLE&client_session_keep_alive=true", "engine": "snowflake"}, "templateVersion": "v0", "depositsTemplateVersions": [{"version": "v0", "effectiveSince": "2022-08-31T16:00:00Z"}, {"version": "v1", "effectiveSince": "2024-11-30T16:00:00Z", "_desc": "this means transactions starting from 1 Dec will be using this template. Please Use actual value for prd config!"}], "templatesPath": "/statement-templates/dbmy", "bizTemplateVersion": "v0", "bizDepositsTemplateVersions": [{"version": "v0", "effectiveSince": "2024-05-29T16:00:00Z"}], "bizTemplatesPath": "/statement-templates/biz", "htmlStatementsPath": "/html-statements", "bizFlexiCreditTemplateVersion": [{"version": "v0", "effectiveSince": "2024-11-29T16:00:00Z00:00"}], "bizFlexiCreditTemplateVersions": [{"version": "v0", "effectiveSince": "2024-11-29T16:00:00Z"}], "bizFlexiCreditTemplatesPath": "/statement-templates/biz-flexi-credit", "receiptTemplateVersion": "v0", "receiptTemplatesPath": "/receipt-templates/dbmy", "htmlReceiptsPath": "/html-receipts", "customerMasterConfig": {"baseURL": "http://customer-master.onboarding.svc.cluster.local", "withHealthCheck": false}, "customerExperienceConfig": {"baseURL": "http://customer-experience.onboarding.svc.cluster.local", "withHealthCheck": false}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local", "withHealthCheck": false}, "transactionHistoryConfig": {"baseURL": "http://transaction-history.core-banking.svc.cluster.local", "withHealthCheck": false}, "pairingServiceConfig": {"baseURL": "http://pairing-service.payments.svc.cluster.local", "withHealthCheck": false}, "paymentExperienceConfig": {"baseURL": "http://payment-experience.payments.svc.cluster.local", "withHealthCheck": false}, "pigeon": {"serviceName": "pigeon", "baseURL": "http://pigeon.pigeon.svc.cluster.local", "withHealthCheck": false}, "transactionStatementClientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "loanGenerateInvoiceKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "txn-statement-client-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-generate-invoice-event", "packageName": "pb", "offsetType": "oldest", "dtoName": "GenerateInvoiceEvent", "enable": false}, "s3Config": {"bucketName": "dbmy-stg-cb-transaction-statements", "flexiLoanBucketName": "dbmy-stg-cb-flexiloan-transaction-statement", "depositsOperationBucketName": "dbmy-stg-cb-deposits-transaction-statements-ops"}, "arbitraryDelayBeforeRedisConn": 5, "redisConfig": {"addr": "clustercfg.dbmy-stg-cb-ec-txn-statements.esalbp.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{REDIS_PASSWORD}}", "tlsEnabled": true}, "flexiLoanTemplateVersion": "v0", "flexiLoanTemplatesPath": "/flexi-loan-templates", "htmlFlexiLoanPath": "/html-flexi-loan", "templatePathForLoanInvoice": "/drawdown-templates", "htmlFilePathForLoanInvoice": "/html-statements", "templateVersionForLoanInvoice": "v0", "dynamicConstants": {"defaultCurrencyCode": "RM", "statementGenerationSLAInDays": 3, "localCurrencyCode": "MYR", "localThousandSeparator": ",", "localDecimalSeparator": "."}, "lendingDynamicConstants": {"penalInterestOverdueRateMultiplyFactor": 0.36, "overdueChargesDays": 120}, "statementGenerationWorkerConfig": {"enable": true, "runMonthly": false, "_runMonthly": true, "waitTimeSeconds": 2, "maxRetryCount": 5, "lockDurationInSeconds": 1, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "bizOAStatementGenerationWorkerConfig": {"statementGenerationDay": 6, "runMonthly": true, "waitTimeSeconds": 2, "maxRetryCount": 5, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "flexiLoanStatementGenerationWorkerConfig": {"enable": true, "runMonthly": false, "waitTimeSeconds": 2, "maxRetryCount": 5, "lockDurationInSeconds": 1, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "bizFlexiCreditStatementGenerationWorkerConfig": {"enable": true, "runMonthly": false, "waitTimeSeconds": 2, "maxRetryCount": 2, "lockDurationInSeconds": 1, "expirationTimeInSecondsForAccountIDInRedisQueue": 18000, "enqueueAccountIDLockDurationInSecond": 300}, "featureFlags": {"enableDebitCardTransactions": true, "enableCardMultiSettlementGrouping": false, "enableAuthzWithProfileID": false}, "tableNamespace": {"schemaName": "STATEMENTS"}, "tenant": "MY", "generateMonthlyStatementsTimeoutInSeconds": 60, "largeConsumerQueueMinTransactionCount": 600, "generateMonthlyLargeStatementsTimeoutInSeconds": 600, "balanceProcessingStrategy": "VERIFY_TRANSACTION_LEVEL", "notificationConfig": {"regeneratedStatementNotification": {"PushTemplate": "regenerated_statement_push", "EmailTemplate": "regenerated_statement_email"}}, "concurrency": 1}