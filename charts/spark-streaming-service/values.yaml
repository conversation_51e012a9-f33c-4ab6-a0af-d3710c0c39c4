appName: spark-streaming-service

pipelines: |
  jobs:
    - name: customer_journal
      reader:
        type: KafkaReader
        endpoint: kafka.audit_log
      transformers:
        - type: ProtobufDecodeTransformer
          input-column: value
          output-column: decoded_struct
          message-name: AuditLog
          # Ref: https://gitlab.myteksi.net/dakota/schemas/-/blob/master/streams/apis/audit_log/pb/audit_log.proto
          message-schema: |
            syntax = "proto3";
            package pb;

            message StreamInfo {
              // streamPartitionID is used by the Coban SDK and SPF for processing partition specific logic
              int64   streamPartitionID   = 1;

              // streamTime is populated by the Coban SDK producer and used to measure lag on consumption
              int64   streamTime        	= 2;

              // streamUpdate is used to detech update type messages produced to Kafka by the SDK.  This is deprecated
              bool    streamUpdate       	= 3;

              // Nonce is used to uniquely identify/dedupe stream messages by Coban libraries.  This is deprecated
              int64    Nonce              = 4;
            }

              message AuditLog {
                StreamInfo streamInfo = 1;
                string customerID = 2;
                int64 eventType = 3;
                int64 category = 4;
                int64 timestamp = 5;
                map<string,string> description = 6;
                int64 source = 7;
                int64 status = 8;
                string customerCif = 9;
                string businessBif = 10;

              // StreamMetadata is a field reserved for internal streams usage
              // Do not modify the pb tag number here, and do not use this number for domain
              // pb fields Do not use this field for domain specific processing, or write to
              // it.

              StreamInfo StreamMetadata = 1235;
            }

            message AuditLogBag {
              repeated AuditLog entries = 1;
            }
        - type: WithColumnTransformer
          columns:
            - col-name: json_data
              expr: to_json(decoded_struct)
        - type: DropColumnTransformer
          columns:
            - decoded_struct
      writer:
        type: StreamSnowflakeWriter
        endpoint: snowflake.landing.audit_log
        table: customer_journal
        output-mode: append
        mode: append
        trigger:
          type: FixedInterval
          interval: 1 hour

    - name: business_customer_risk_assessment
      reader:
        type: KafkaReader
        endpoint: kafka.risk_broker
      transformers:
        - type: ProtobufDecodeTransformer
          input-column: value
          output-column: decoded_struct
          message-name: BusinessCustomerRiskAssessment
          # Ref: https://gitlab.myteksi.net/dbmy/schemas/-/blob/master/streams/apis/business_customer_risk_assessment/pb/business_customer_risk_assessment.proto?ref_type=heads
          message-schema: |
            syntax = "proto3";
            package pb;

            message StreamInfo {
              // streamPartitionID is used by the Coban SDK and SPF for processing partition specific logic
              int64   streamPartitionID   = 1;

              // streamTime is populated by the Coban SDK producer and used to measure lag on consumption
              int64   streamTime        	= 2;

              // streamUpdate is used to detech update type messages produced to Kafka by the SDK.  This is deprecated
              bool    streamUpdate       	= 3;

              // Nonce is used to uniquely identify/dedupe stream messages by Coban libraries.  This is deprecated
              int64    Nonce              = 4;
            }

              message BusinessCustomerRiskAssessment {
                  StreamInfo streamInfo = 1;
                  string bifNumber = 2;
                  string countryOfOperation = 3;
                  string countryOfOperationRisk = 4;
                  string countryOfIncorporation = 5;
                  string countryOfIncorporationRisk = 6;
                  repeated BizProduct products = 7;
                  repeated BizIndustry industries = 8;
                  NameScreeningRisk nameScreeningRisk = 9;
                  repeated BizPerson person = 10;
                  string bizType = 11;
                  int64 salesVolume = 12;
                  int64 numberOfEmployees = 13;
                  string bizRegistrationDate = 14;
                  string legalStructure = 15;
                  string overallRisk = 16;
                  
                // StreamMetadata is a field reserved for internal streams usage
                // Do not modify the pb tag number here, and do not use this number for domain
                // pb fields Do not use this field for domain specific processing, or write to
                // it.
                  StreamInfo StreamMetadata = 1235;
              }
            message BusinessCustomerRiskAssessmentBag {
              repeated BusinessCustomerRiskAssessment entries = 1;
            }
            message NameScreeningRisk {
                string pep = 1;
                string sanction = 2;
                string adverse = 3;
            }
            message BizProduct {
                string productCode = 1;
                string productRisk = 2;
            }
            message BizIndustry {
                string msicCode = 1;
                string industryRisk = 2;
                string specialIndustryRisk = 3;
                bool isManufacturing = 4; 
            }
            message BizPerson {
                string cifNumber = 1;
                string personType = 2;
                string nationality = 3;
                string nationalityRisk = 4;
                string countryOfDomicile = 5;
                string countryOfDomicileRisk = 6;
                NameScreeningRisk nameScreeningRisk = 7;
            }
        - type: WithColumnTransformer
          columns:
            - col-name: json_data
              expr: to_json(decoded_struct)
        - type: DropColumnTransformer
          columns:
            - decoded_struct
      writer:
        type: StreamSnowflakeWriter
        endpoint: snowflake.landing.risk_broker
        table: business_customer_risk_assessment
        output-mode: append
        mode: append
        trigger:
          type: FixedInterval
          interval: 1 hour
