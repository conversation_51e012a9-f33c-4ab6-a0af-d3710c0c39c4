sparkVersion: 3.5.0
imageTag: 3.5.0
jarTag: 7b5517f3
resourcesConfBucket: dbmy-dev-data-s3-resources-conf
s3SparkHSLogBucket: dbmy-dev-data-s3-spark-logs
driverServiceAccount: data-ingestion
driverCoreAmount: 1
driverMemory: 2048m
executorCoreAmount: 1
executorMemory: 2048m
activePipelines:
  - customer_journal
  - business_customer_risk_assessment

endpoints:

  kafka.audit_log.yaml: |
    endpoint-type: kafka
    system: audit_log
    brokers:
      - b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094
      - b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094
      - b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094
    topic: dev-audit-log
    checkpoint-location: s3a://dbmy-dev-data-s3-raw-encrypted/checkpoints/audit_log/customer_journal
    authentication:
      type: KafkaSslAuth
      keystore-location-type: local
      keystore-location: /opt/java/openjdk/lib/security/cacerts

  snowflake.landing.audit_log.yaml: |
    endpoint-type: snowflake
    host: gxbank-dev.snowflakecomputing.com
    database: landing
    schema: audit_log
    warehouse: load_wh
    role: loader
    authentication:
      type: VaultApiAuth
      vault-address: https://vault.infra.dev.g-bank.app
      kubernetes-auth-path: k8s-dev-data-01
      vault-role: data-dev-role
      engine: DB
      secret-path: database/static-creds/dev-data-loader-snowflake-rw

  kafka.risk_broker.yaml: |
    endpoint-type: kafka
    system: risk_broker
    brokers:
      - b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094
      - b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094
      - b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094
    topic: dev-biz-cra
    checkpoint-location: s3a://dbmy-dev-data-s3-raw-encrypted/checkpoints/risk_broker/business_customer_risk_assessment
    authentication:
      type: KafkaSslAuth
      keystore-location-type: local
      keystore-location: /opt/java/openjdk/lib/security/cacerts

  snowflake.landing.risk_broker.yaml: |
    endpoint-type: snowflake
    host: gxbank-dev.snowflakecomputing.com
    database: landing
    schema: risk_broker
    warehouse: load_wh
    role: loader
    authentication:
      type: VaultApiAuth
      vault-address: https://vault.infra.dev.g-bank.app
      kubernetes-auth-path: k8s-dev-data-01
      vault-role: data-dev-role
      engine: DB
      secret-path: database/static-creds/dev-data-loader-snowflake-rw
