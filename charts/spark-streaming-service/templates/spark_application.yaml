apiVersion: sparkoperator.k8s.io/v1beta2
kind: SparkApplication
metadata:
  name: {{ .Values.appName }}
  namespace: data-lake
spec:
  type: Scala
  mode: cluster
  sparkVersion: {{ .Values.sparkVersion }}
  image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/build/data-spark-base:{{ .Values.imageTag }}
  imagePullPolicy: IfNotPresent
  mainClass: com.digibank.data.PipelineMain
  mainApplicationFile: s3a://{{ .Values.resourcesConfBucket }}/jars/loader/data-loader-{{ .Values.jarTag }}.jar
  timeToLiveSeconds: 600
  arguments:
    - --app-type
    - stream
    - --config-source-type
    - local
    - --config-root-dir
    - /etc/stream
    - --config-file
    - pipelines.{{ .Values.appName }}.yaml
    - --job-id
    - stream-{{ .Values.appName }}-{{ .Values.jarTag }}
    {{- range $item := .Values.activePipelines }}
    - --pipeline
    - {{ $item }}
    {{- end }}
  sparkConf:
    spark.driver.extraJavaOptions: -Divy.cache.dir=/tmp -Divy.home=/tmp
    spark.hadoop.fs.s3a.impl: org.apache.hadoop.fs.s3a.S3AFileSystem
    spark.hadoop.fs.s3a.server-side-encryption-algorithm: AES256
    spark.hadoop.fs.s3a.aws.credentials.provider: com.amazonaws.auth.WebIdentityTokenCredentialsProvider
    spark.kubernetes.authenticate.submission.oauthTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    spark.eventLog.enabled: "true"
    spark.eventLog.dir: s3a://{{ .Values.s3SparkHSLogBucket }}/logs
    spark.eventLog.rolling.enabled: "true"
    spark.eventLog.rolling.maxFileSize: 128m
    spark.jars.ivy: /tmp/.ivy
  restartPolicy:
    type: Always
  driver:
    podSecurityContext:
      runAsNonRoot: true
    securityContext:
      allowPrivilegeEscalation: false
      runAsNonRoot: true
    annotations:
      traffic.sidecar.istio.io/excludeInboundPorts: 7078,7079
    cores: {{ .Values.driverCoreAmount }}
    memory: {{ .Values.driverMemory }}
    labels:
      version: {{ .Values.sparkVersion }}
    serviceAccount: {{ .Values.driverServiceAccount }}
    configMaps:
      - name: stream-config-{{ .Values.appName }}
        path: /etc/stream
  executor:
    annotations:
      traffic.sidecar.istio.io/excludeOutboundPorts: 7078,7079
    cores: {{ .Values.executorCoreAmount }}
    memory: {{ .Values.executorMemory }}
    labels:
      version: {{ .Values.sparkVersion }}
    podSecurityContext:
      runAsNonRoot: true
    securityContext:
      allowPrivilegeEscalation: false
      runAsNonRoot: true
    configMaps:
      - name: stream-config-{{ .Values.appName }}
        path: /etc/stream
  dynamicAllocation:
    enabled: true
    initialExecutors: 0
    minExecutors: 0
    maxExecutors: 100

---

apiVersion: v1
kind: ConfigMap
metadata:
  name: stream-config-{{ .Values.appName }}
  namespace: data-lake
data:
  pipelines.{{ .Values.appName }}.yaml: |
    {{ .Values.pipelines | nindent 4 }}
  {{ .Values.endpoints | toYaml | nindent 2 }}
